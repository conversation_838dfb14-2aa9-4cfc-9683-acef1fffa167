package com.app.messej.ui.home.settings.levels

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.enums.UserCitizenship

object LevelUtils {

    @ColorRes
    fun UserCitizenship.setupCitizenshipLevelIconBackground() : Int {
        return when (this) {
            UserCitizenship.VISITOR -> R.color.textColorOnSecondary
            UserCitizenship.RESIDENT -> R.color.colorPodiumSpeakerResident
            UserCitizenship.CITIZEN -> R.color.colorPodiumSpeakerCitizen
            UserCitizenship.OFFICER -> R.color.colorPodiumSpeakerOfficer
            UserCitizenship.AMBASSADOR -> R.color.colorPodiumSpeakerAmbassador
            UserCitizenship.MINISTER -> R.color.colorPrimary
            UserCitizenship.PRESIDENT -> R.color.colorPodiumSpeakerOfficer
            UserCitizenship.GOLDEN -> R.color.colorFlashatGolden
        }
    }

    @ColorRes
    fun UserCitizenship.setupCitizenshipLevelDescriptionTextColor() : Int {
        return when (this) {
            UserCitizenship.VISITOR, UserCitizenship.RESIDENT, UserCitizenship.CITIZEN -> R.color.colorAlwaysDarkSurfaceSecondaryDark
            UserCitizenship.OFFICER -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.MINISTER -> R.color.textColorOnPrimary
            UserCitizenship.PRESIDENT -> R.color.colorPrimary
            UserCitizenship.GOLDEN -> R.color.white
        }
    }

    @Composable
    fun Modifier.setupCitizenshipLevelBackground(citizenShip: UserCitizenship): Modifier {
        val backgroundColor = when (citizenShip) {
            UserCitizenship.VISITOR, UserCitizenship.RESIDENT -> List(2) { (colorResource(R.color.textColorAlwaysDarkSecondaryLight)) }
            UserCitizenship.CITIZEN -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerCitizen))
            UserCitizenship.OFFICER -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerOfficer))
            UserCitizenship.AMBASSADOR -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerAmbassador))
            UserCitizenship.MINISTER -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPrimary))
            UserCitizenship.PRESIDENT -> listOf(Color(0xFFA37A1E), Color(0xFFE6BE69), Color(0xFF956E13))
            UserCitizenship.GOLDEN -> listOf(Color(0xFFFFD473),colorResource(R.color.colorFlashatGolden),)}
        return background(
            brush = Brush.verticalGradient(colors = backgroundColor),
            shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
        ).border(width = 1.dp, shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)), color = backgroundColor.last())
    }

    @Composable
    fun Modifier.setupCitizenshipLevelBannerBackground(citizenShip: UserCitizenship): Modifier {
        val backgroundColor = when (citizenShip) {
            UserCitizenship.VISITOR, UserCitizenship.RESIDENT -> listOf(colorResource(id = R.color.white), colorResource(R.color.textColorOnSecondary))
            UserCitizenship.CITIZEN -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerCitizen))
            UserCitizenship.OFFICER -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerOfficer))
            UserCitizenship.AMBASSADOR -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPodiumSpeakerAmbassador))
            UserCitizenship.MINISTER -> listOf(colorResource(id = R.color.white), colorResource(R.color.colorPrimary))
            UserCitizenship.PRESIDENT -> listOf(Color(0xFFA37A1E), Color(0xFFE6BE69), Color(0xFF956E13))
            UserCitizenship.GOLDEN -> listOf(Color(0xFFFFD473),colorResource(id=R.color.white),Color(0xFFFFD473) )
        }
        return background(brush = Brush.linearGradient(colors = backgroundColor))
    }

    @StringRes
    fun UserCitizenship.displayCitizenshipLevelText(): Int {
        return when (this){
            UserCitizenship.VISITOR, UserCitizenship.RESIDENT -> R.string.user_citizenship_visitor_and_resident
            UserCitizenship.CITIZEN -> R.string.user_citizenship_citizen
            UserCitizenship.OFFICER -> R.string.user_citizenship_officer
            UserCitizenship.AMBASSADOR -> R.string.user_citizenship_ambassador
            UserCitizenship.MINISTER -> R.string.user_citizenship_minister
            UserCitizenship.PRESIDENT -> R.string.user_citizenship_president
            UserCitizenship.GOLDEN -> R.string.user_citizenship_golden
        }
    }

    fun UserCitizenship.upgradeUserLevelDescriptionText(context: Context, currentUserCitizenship: UserCitizenship?=null): String {
            return when (this) {
                UserCitizenship.OFFICER ->
                    context.getString(R.string.upgrade_user_level_upgrade_desc_officer)

                UserCitizenship.AMBASSADOR ->
                    when (currentUserCitizenship) {
                        UserCitizenship.CITIZEN ->
                            context.getString(R.string.upgrade_user_level_upgrade_desc_ambassador_for_citizen)
                        else ->
                            context.getString(R.string.upgrade_user_level_upgrade_desc_ambassador_for_officer)
                    }

                UserCitizenship.MINISTER -> when (currentUserCitizenship) {
                    UserCitizenship.CITIZEN -> context.getString(R.string.upgrade_user_level_upgrade_desc_minister_for_citizen)
                    UserCitizenship.OFFICER -> context.getString(R.string.upgrade_user_level_upgrade_desc_minister_for_officer)
                    UserCitizenship.AMBASSADOR ->  context.getString(R.string.upgrade_user_level_upgrade_desc_minister_for_ambassador)
                    else -> context.getString(R.string.upgrade_user_level_upgrade_desc_minister_for_ambassador)
                }

                else ->
                    context.getString(R.string.upgrade_user_level_upgrade_desc_officer)
            }

    }
}