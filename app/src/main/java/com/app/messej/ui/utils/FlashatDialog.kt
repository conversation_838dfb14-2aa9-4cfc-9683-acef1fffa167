package com.app.messej.ui.utils

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.view.LayoutInflater
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.databinding.LayoutFlashatDialogBinding
import com.app.messej.ui.utils.DataFormatHelper.dpToPx
import com.app.messej.ui.utils.FragmentExtensions.show
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class FlashatDialog private constructor() {

    abstract class Dialog {
        abstract fun setConfirmButton(title: String, @DrawableRes icon: Int = R.drawable.ic_chat_liked, tint: Boolean = true, iconPadding: Boolean = true, onClick: () -> Boolean)
        abstract fun setConfirmButton(@StringRes title: Int, @DrawableRes icon: Int = R.drawable.ic_chat_liked, tint: Boolean = true, iconPadding: Boolean = true, onClick: () -> Boolean)
        abstract fun setConfirmButtonVisible(visible: Boolean)

        abstract fun setCloseButtonText(text: String)
        abstract fun setCloseButtonText(@StringRes text: Int)
        abstract fun setCloseButtonIcon(@DrawableRes icon: Int)
        abstract fun setCloseButtonVisible(visible: Boolean)

        abstract fun setNeutralButton(title:String, @DrawableRes icon: Int, tint: Boolean = false, onClick: () -> Boolean)
        abstract fun setNeutralButtonVisible(visible: Boolean = false)

        abstract fun setCloseButton(title: String, @DrawableRes icon: Int = R.drawable.ic_close, onClick: () -> Boolean)
        abstract fun setCloseButton(@StringRes title: Int, @DrawableRes icon: Int = R.drawable.ic_close, onClick: () -> Boolean)

        abstract fun setTitle(title: CharSequence?, @ColorRes textColor : Int = R.color.textColorSecondary)
        abstract fun setTitle(@StringRes title: Int?, @ColorRes textColor : Int = R.color.textColorSecondary)

        abstract fun setMessage(message: CharSequence)
        abstract fun setMessage(@StringRes message: Int)

        abstract fun setCheckBox(message: CharSequence, checked: Boolean, isVisible: Boolean = true)
        abstract fun setCheckBox(@StringRes message: Int, checked: Boolean, isVisible: Boolean = true)
        abstract val checkBoxChecked: Boolean

        abstract fun setIcon(@DrawableRes icon: Int?)

        abstract fun setCancelable(cancelable: Boolean)
        abstract fun setOnDismissListener(listener: () -> Unit)
    }

    private data class DialogImpl(
        val context: Context,
    ): Dialog() {

        var dialog: AlertDialog
        var binding: LayoutFlashatDialogBinding

        init {
            val inflater = LayoutInflater.from(context)
            binding = DataBindingUtil.inflate<LayoutFlashatDialogBinding>(inflater, R.layout.layout_flashat_dialog, null, false)
            dialog = MaterialAlertDialogBuilder(context, R.style.TransparentMaterialAlertDialog).setView(binding.root).setCancelable(false).create()
            dialog.window?.apply {
                setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
                setDimAmount(0.8f)
            }
            binding.closeButton.setOnClickListener {
                dialog.dismiss()
            }

            binding.icon.isVisible = false
            binding.title.isVisible = false
            binding.message.isVisible = false
            binding.checkBox.isVisible = false
        }

        override fun setIcon(@DrawableRes icon: Int?) {
            binding.alertIcon = icon?.let { ContextCompat.getDrawable(context, icon) }
        }

        override fun setTitle(@StringRes title: Int?, @ColorRes textColor : Int) {
            setTitle(if (title==null) null else context.resources.getString(title), textColor)
        }
        override fun setTitle(title: CharSequence?, @ColorRes textColor : Int) {
            binding.title.isVisible = !title.isNullOrBlank()
            binding.title.text = title
            binding.title.setTextColor(ContextCompat.getColor(context, textColor))
        }

        override fun setNeutralButtonVisible(visible: Boolean) {
            binding.neutralButton.isVisible = visible
            binding.neutralButtonText.isVisible = visible
        }

        override fun setNeutralButton(title: String, icon: Int, tint: Boolean, onClick: () -> Boolean) {
            binding.neutralButtonText.text = title
            binding.neutralIcon.setImageResource(icon)
            binding.neutralButton.setOnClickListener {
                if(onClick.invoke()) {
                    dialog.dismiss()
                }
            }
            binding.neutralIcon.imageTintList = if (tint) ContextCompat.getColor(context, R.color.colorPrimaryDark).let { ColorStateList.valueOf(it) } else null
        }

        override fun setMessage(@StringRes message: Int) = setMessage(context.resources.getString(message))

        override fun setMessage(message: CharSequence) {
            binding.message.isVisible = true
            binding.message.text = message
        }

        override fun setCheckBox(@StringRes message: Int, checked: Boolean, isVisible: Boolean) = setCheckBox(context.resources.getString(message), checked, isVisible)

        override fun setCheckBox(message: CharSequence, checked: Boolean, isVisible: Boolean) {
            binding.checkBox.isVisible = isVisible
            binding.checkBoxTitle.text = message
            binding.checkBoxControl.isChecked = checked
        }

        override val checkBoxChecked: Boolean
            get() = binding.checkBoxControl.isChecked

        override fun setConfirmButton(title: Int, icon: Int, tint: Boolean, iconPadding: Boolean, onClick: () -> Boolean)
        = setConfirmButton(context.resources.getString(title), icon, tint, iconPadding, onClick)

        override fun setConfirmButton(title: String, icon: Int, tint: Boolean, iconPadding: Boolean, onClick: () -> Boolean) {
            binding.confirmText.text = if (title.contains(Constants.CURRENCY_COIN) || title.contains(Constants.CURRENCY_FLIX)) {
                binding.confirmText.isAllCaps = false
                title.uppercase()
                    .replace(Constants.CURRENCY_COIN.uppercase(), Constants.CURRENCY_COIN)
                    .replace(Constants.CURRENCY_FLIX.uppercase(), Constants.CURRENCY_FLIX)
            } else title
            binding.confirmButton.setOnClickListener {
                if(onClick.invoke()) {
                    dialog.dismiss()
                }
            }
            binding.confirmIcon.apply {
                setImageResource(icon)
                imageTintList = if (tint) ContextCompat.getColor(context, R.color.colorSecondary).let { ColorStateList.valueOf(it) } else null
                setPadding(if (iconPadding) 4.dpToPx(binding.root.context) else 0)
            }
        }

        override fun setCloseButton(title: Int, icon: Int, onClick: () -> Boolean) = setCloseButton(context.resources.getString(title), icon, onClick)
        override fun setCloseButton(title: String, icon: Int, onClick: () -> Boolean) {
            binding.closeText.text = title
            binding.closeIcon.setImageResource(icon)
            binding.closeButton.setOnClickListener {
                if(onClick.invoke()) {
                    dialog.dismiss()
                }
            }
        }

        override fun setCloseButtonText(text: Int) = setCloseButtonText(context.resources.getString(text))
        override fun setCloseButtonText(text: String) {
            binding.closeText.text = text
        }

        override fun setCloseButtonIcon(icon: Int) {
            binding.closeIcon.setImageResource(icon)
        }

        override fun setConfirmButtonVisible(visible: Boolean) {
            binding.confirmButton.isVisible = visible
            binding.confirmText.isVisible = visible
        }

        override fun setCloseButtonVisible(visible: Boolean) {
            binding.closeButton.isVisible = visible
            binding.closeText.isVisible = visible
        }

        override fun setCancelable(cancelable: Boolean) {
            dialog.setCancelable(cancelable)
        }

        override fun setOnDismissListener(listener: () -> Unit) {
            dialog.setOnDismissListener {
                listener.invoke()
            }
        }
    }

    companion object {
        fun Fragment.showFlashatDialog(configure: Dialog.() -> Unit): AlertDialog {
            val dialog = DialogImpl(requireContext())
            dialog.configure()

            dialog.dialog.show(viewLifecycleOwner)

            return dialog.dialog
        }

        fun MainActivity.showFlashatDialog(configure: Dialog.() -> Unit): AlertDialog {
            val dialog = DialogImpl(this)
            dialog.apply(configure)

            dialog.dialog.show()

            return dialog.dialog
        }
    }
}