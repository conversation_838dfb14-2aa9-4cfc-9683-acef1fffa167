package com.app.messej.ui.home.settings.levels

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentLevelsBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class LevelsFragment : Fragment() {

    private lateinit var binding: FragmentLevelsBinding
    private val viewModel: LevelsViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_levels, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.apply {
            text = getString(R.string.account_management_levels)
            isAllCaps = true
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        binding.composeView.setContent {
            LevelsComposeScreen(viewModel = viewModel,listener = object : onClick{
                override fun onUpgradeLevelClick() {
                   findNavController().navigateSafe(LevelsFragmentDirections.actionLevelsFragmentToUpgradeUserLevelFragment())
                }

                override fun gotoAuthorities() {
                    if(viewModel.user.premium)
                    findNavController().navigateSafe(LevelsFragmentDirections.actionPremiumAuthoritiesFragment()) else  findNavController().navigateSafe(LevelsFragmentDirections.actionSettingsBottomSheetFragmentToAuthoritiesStandAloneFragment())
                }


            })
        }
    }
}