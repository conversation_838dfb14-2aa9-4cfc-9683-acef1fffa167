package com.app.messej.ui.utils

import android.content.Context
import androidx.annotation.StringRes
import com.app.messej.R
import com.app.messej.data.model.enums.UserCitizenship

object EnumUtils {
    fun UserCitizenship.displayText(c: Context): String {
        return c.resources.getString(displayText())
    }

    @StringRes
    fun UserCitizenship.displayText(): Int {
        return when (this){
            UserCitizenship.CITIZEN -> R.string.user_citizenship_citizen
            UserCitizenship.OFFICER -> R.string.user_citizenship_officer
            UserCitizenship.VISITOR -> R.string.user_citizenship_visitor
            UserCitizenship.RESIDENT -> R.string.user_citizenship_resident
            UserCitizenship.AMBASSADOR -> R.string.user_citizenship_ambassador
            UserCitizenship.MINISTER -> R.string.user_citizenship_minister
            UserCitizenship.PRESIDENT -> R.string.user_citizenship_president
            User<PERSON>itizenship.GOLDEN -> R.string.user_citizenship_golden
        }
    }
}