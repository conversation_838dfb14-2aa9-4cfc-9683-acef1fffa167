package com.app.messej.ui.home.settings.levels.upgradeLevel

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.api.UpgradeUserLevelRequest
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentUpgradeUserLevelBinding
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class UpgradeUserLevelFragment : BottomSheetDialogFragment() {


    private val viewModel: UpgradeUserLevelViewModel by viewModels()
    private lateinit var binding: FragmentUpgradeUserLevelBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_upgrade_user_level, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        binding.composeView.setContent {
            UserLevelComposeScreen(viewModel = viewModel, listener = object : onItemSelectedListener {
                override fun onUpgradeLevelClick(selectedCitizenship: UserCitizenship?, requiredFlix: Int?) {
                    requiredFlix?.let {
                        showUpgradeAlert(it,selectedCitizenship, onConfirm = {
                            viewModel.upgradeUserLevel(UpgradeUserLevelRequest(upgradedLevel = selectedCitizenship))
                        })
                    }
                }

            })
        }
    }

    private fun observe() {
        viewModel.accountDetails.observe(viewLifecycleOwner){}
        viewModel.upgradeSuccess.observe(viewLifecycleOwner) {
            Log.d("USERLEVEL","$it")
            if (it == true) {
                findNavController().popBackStack(R.id.upgradeUserLevelFragment,true)
            }
        }
        viewModel.onUpgradeInsufficientBalance.observe(viewLifecycleOwner){
           if (it.first) {
               showInsufficientBalanceAlert(resources.getString(R.string.upgrade_user_level_insufficient_balance,it.second.toString()), header = resources.getString(R.string.title_insufficient_flix_balance_premium), isBuyCoin = false)
           }
        }
    }

    private fun showUpgradeAlert(requiredFlix: Int?, selectedCitizenship: UserCitizenship?, onConfirm: () -> Unit) {
        viewModel.calculateRequiredBalance(requiredFlix)
        confirmAction(
            message = resources.getString(R.string.upgrade_user_level_alert_message,selectedCitizenship, requiredFlix.toString()), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            onConfirm.invoke()
        }
    }


}