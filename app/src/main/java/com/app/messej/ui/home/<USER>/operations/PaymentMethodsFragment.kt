package com.app.messej.ui.home.businesstab.operations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.databinding.FragmentPaymentMethodsBinding
import com.app.messej.ui.home.businesstab.BusinessCustomerInformationFragmentDirections
import com.app.messej.ui.home.businesstab.HomeBusinessFragment
import com.app.messej.ui.home.businesstab.PaymentListQuickAdapter
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.kennyc.view.MultiStateView


class PaymentMethodsFragment : Fragment() {
    private lateinit var binding: FragmentPaymentMethodsBinding
    private val viewModel: PaymentMethodDetailViewModel by viewModels()
    private var mAdapter: PaymentListQuickAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_payment_methods, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = getString(R.string.select_preferred_payment_method)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private fun observe() {
        viewModel.selectedPaymentMethods.observe(viewLifecycleOwner) { selectedPaymentMethod ->
            // Update UI based on the selected payment method
            // For example, highlight the selected item in the RecyclerView
            mAdapter?.apply {
                selectedItemPosition = viewModel.paymentMethods.value?.indexOf(selectedPaymentMethod)!!
                notifyDataSetChanged() // or use notifyItemChanged() for better performance
            }
        }
        viewModel.dataLoading.observe(viewLifecycleOwner){
            if (it) {binding.multiStateView.viewState = MultiStateView.ViewState.LOADING}
            else binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
        }
        viewModel.paymentMethods.observe(viewLifecycleOwner) { paymentMethods ->
            mAdapter?.apply {
                if(paymentMethods?.isEmpty()== true){
                    binding.multiStateView.viewState = MultiStateView.ViewState.EMPTY
                }
                if (data.size == 0 || paymentMethods?.isEmpty()  == false) {
                    setNewInstance(paymentMethods?.toMutableList())
                } else {
                    setDiffNewData(paymentMethods?.toMutableList())
                }
            }
        }
    }

    private fun setup() {
        binding.acceptButton.setOnClickListener {
            if (viewModel.selectedPaymentMethods.value != null) {
                if (viewModel.selectedPaymentMethods.value?.showPopup == true) {
                    showFlashatDialog {
                        setMessage(getString(R.string.business_sell_flax__mena_fees_desc, viewModel.selectedPaymentMethods.value?.processFee?.toInt()))
                        setCancelable(false)
                        setConfirmButton(R.string.common_proceed) {
                            findNavController().navigateSafe(
                                PaymentMethodsFragmentDirections.actionPaymentMethodsFragmentToDynamicFormFragment(
                                    viewModel.selectedPaymentMethods.value?.paymentMethod.toString(),
                                    viewModel.selectedPaymentMethods.value?.id ?: return@setConfirmButton true,
                                    viewModel.selectedPaymentMethods.value?.isMenaPaymentMethod ?: false
                                )
                            )
                            true
                        }
                        setCloseButton(R.string.common_cancel) {
                            true
                        }
                    }
                } else {
                    findNavController().navigateSafe(
                        PaymentMethodsFragmentDirections.actionPaymentMethodsFragmentToDynamicFormFragment(
                            viewModel.selectedPaymentMethods.value?.paymentMethod.toString(),
                            viewModel.selectedPaymentMethods.value?.id ?: return@setOnClickListener
                        )
                    )
                }
            } else {
                Toast.makeText(requireContext(), getString(R.string.warning_select_payment_method), Toast.LENGTH_SHORT).show()
            }
        }

        binding.btnCancel.setOnClickListener {
            // Show confirmation dialog when cancel button is clicked
            showFlashatDialog {
                setMessage(getString(R.string.payout_cancel_dialog))
                setCancelable(true)
                setConfirmButton(R.string.common_confirm) {
                    findNavController().popBackStack()
                    true
                }
                setCloseButton(R.string.common_later, R.drawable.ic_snooze) {
                    true
                }
            }
        }

        binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
        viewModel.getPaymentMethods()
        initAdapter()
    }

    private fun initAdapter() {
        mAdapter = PaymentListQuickAdapter(mutableListOf(), object : PaymentListQuickAdapter.ItemClickListener {
            override fun onItemClick(item: PaymentMethodResponse.PaymentMethod, position: Int) {
                viewModel.setPaymentMethod(item)
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.paymentMethodList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(PaymentListQuickAdapter.PaymentMethodDiff)
        }
    }

}
