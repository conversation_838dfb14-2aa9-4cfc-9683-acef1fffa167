package com.app.messej.ui.home.settings.levels.upgradeLevel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.UpgradeUserLevelRequest
import com.app.messej.data.model.api.UpgradeUserLevelResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class UpgradeUserLevelViewModel(application: Application) : AndroidViewModel(application){

    private val accountRepo: AccountRepository = AccountRepository(application)
    private val repository = BusinessRepository(application)
    private val profRepo = ProfileRepository(application)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val user: CurrentUser get() = accountRepo.user

    private val _getUpgradeUserLevel = MutableLiveData<UpgradeUserLevelResponse>()
    val getUpgradeUserLevel : LiveData<UpgradeUserLevelResponse> = _getUpgradeUserLevel

    private val _loading = MutableLiveData<Boolean>()
    val loading : LiveData<Boolean> = _loading

    val upgradeSuccess = LiveEvent<Boolean?>()

    init {
        getUpgradeUserLevel()
    }

    private fun getUpgradeUserLevel() {
        viewModelScope.launch(Dispatchers.IO) {
            _loading.postValue(true)
            when (val result: ResultOf<UpgradeUserLevelResponse> = repository.getUserLevelUpgrade()) {
                is ResultOf.Success -> {
                    _loading.postValue(false)
                    _getUpgradeUserLevel.postValue(result.value)
                }
                is ResultOf.APIError->{
                    _loading.postValue(true)
                }
                is ResultOf.Error->{
                    _loading.postValue(true)

                }
            }
        }
    }
    private val _isSubmitting = MutableLiveData<Boolean>()
    val isSubmitting : LiveData<Boolean> = _isSubmitting

    val onUpgradeInsufficientBalance = LiveEvent<Pair<Boolean,Int?>>()
    val onUpgradeError = LiveEvent<String>()

     fun upgradeUserLevel(request: UpgradeUserLevelRequest) {

            viewModelScope.launch(Dispatchers.IO) {
                _isSubmitting.postValue(true)
                try {
                    when (val result = repository.upgradeUserLevel(request)) {
                        is ResultOf.Success -> {
                            profRepo.getAccountDetails()
                            upgradeSuccess.postValue(true)
                        }
                        is ResultOf.APIError -> {
                            if (result.code == 400) {
                                onUpgradeInsufficientBalance.postValue(Pair(true,requiredBalanceFlix.value))
                            }
                        }
                        is ResultOf.Error -> {  }
                    }
                } catch (e: Exception) {
                    Log.d("UpgradeUserLevelVM", "upgrade user level rating Exception: ${e.message}")
                }
                _isSubmitting.postValue(false)
            }
    }

    val requiredBalanceFlix = MutableLiveData<Int>()

    fun calculateRequiredBalance(requiredFlix: Int?){
//        val currentFlix = (_accountDetails.value?.currentFlix)?.toInt()
//       val reqBalance = (requiredFlix ?: 0) - (currentFlix?:0)
        Log.d("reqBALANCE","$requiredFlix")
        requiredBalanceFlix.postValue(requiredFlix?:0)
    }



}