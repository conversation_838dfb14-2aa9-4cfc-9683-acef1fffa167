package com.app.messej.ui.home.settings.privacy

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.settings.PodiumPrivacyResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PodiumPrivacyViewModel(application: Application) : AndroidViewModel(application) {
    private val settingsRepository = SettingsRepository(application)
    private val accountRepository = AccountRepository(application)

    init {
        getPodiumPrivacy()
    }

    private val _isPodiumPrivacyEnabled = MutableLiveData(false)
    val isPodiumPrivacyEnabled: LiveData<Boolean?> = _isPodiumPrivacyEnabled

    private val _isJoinHiddeEnabled = MutableLiveData(false)
    val isJoinHiddenEnabled: LiveData<Boolean?> = _isJoinHiddeEnabled

    private fun getPodiumPrivacy() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PodiumPrivacyResponse> = settingsRepository.getPodiumPrivacyStatus()) {
                is ResultOf.Success -> {
                    _isPodiumPrivacyEnabled.postValue(result.value.excludeLiveFriends)
                    _isJoinHiddeEnabled.postValue(result.value.joinHidden)
                }

                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }
    val userAccount = accountRepository.userFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val joinHiddenVisible = userAccount.map {
        it?.citizenship in listOf(UserCitizenship.GOLDEN,UserCitizenship.MINISTER, UserCitizenship.PRESIDENT)
    }.distinctUntilChanged()


    val podiumPrivacyUpdateSuccess = LiveEvent<Boolean>()

    fun handlePrivacySwitch(checked: Boolean) {
        _isPodiumPrivacyEnabled.value = checked
        updateMessagePrivacy()
    }


    fun handleJoinHiddenSwitch(checked: Boolean) {
        _isJoinHiddeEnabled.value = checked
        updateMessagePrivacy()
    }

    fun updateMessagePrivacy() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = settingsRepository.setPodiumPrivacy(PodiumPrivacyResponse(excludeLiveFriends = _isPodiumPrivacyEnabled.value, joinHidden = _isJoinHiddeEnabled.value))) {
                is ResultOf.Success -> {
                    podiumPrivacyUpdateSuccess.postValue(true)
                }

                is ResultOf.Error -> {
                    podiumPrivacyUpdateSuccess.postValue(false)
                }

                is ResultOf.APIError -> {
                    podiumPrivacyUpdateSuccess.postValue(false)
                }
            }
        }
    }

}