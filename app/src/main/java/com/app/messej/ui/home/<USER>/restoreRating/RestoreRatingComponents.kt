package com.app.messej.ui.home.settings.restoreRating

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import com.app.messej.R
import com.app.messej.ui.composeComponents.FlashatComposeTypography

@Composable
fun RestoreRatingTitleDescriptionView(
    @StringRes title: Int,
    isFlix: Boolean = false,
    value: String? = null,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.fillMaxWidth().weight(weight = 1F),
            text = stringResource(id = title),
            style = FlashatComposeTypography.defaultType.body2,
            color = colorResource(id = R.color.white)
        )
        Text(
            text = buildAnnotatedString {
                if (isFlix) {
                    withStyle(style = SpanStyle(
                        color = colorResource(id = R.color.colorAlwaysLightSecondary),
                        fontStyle = FontStyle.Italic)) { append(stringResource(id = R.string.common_flix)) }
                }
                append(" $value")
            },
            textAlign = TextAlign.End,
            style = FlashatComposeTypography.defaultType.subtitle2,
            color = colorResource(id = R.color.white)
        )
    }
}

@Composable
fun RestoreRatingFlixCoinDetailBackgroundView(
    content : @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)),
                brush = Brush.verticalGradient(colors = listOf(
                    colorResource(id = R.color.colorPrimary),
                    colorResource(id = R.color.colorPrimary).copy(alpha = 0.75F)))
            ).padding(all = dimensionResource(id = R.dimen.element_spacing))
    ) {
        content()
    }
}