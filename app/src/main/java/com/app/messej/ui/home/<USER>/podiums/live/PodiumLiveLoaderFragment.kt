package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumWhoCanJoin
import com.app.messej.databinding.FragmentPodiumLiveLoaderBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showJoinPodiumWithCoinsAlert
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showJoinPodiumWithInsufficientCoinsAlert
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.Synchronize

class PodiumLiveLoaderFragment : Fragment() {


    private lateinit var binding: FragmentPodiumLiveLoaderBinding
    val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    private val args: PodiumLiveLoaderFragmentArgs by navArgs()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_loader, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val kind = PodiumKind.entries.getOrNull(args.kind)
        kind.let {
            Log.d("PLF", "loaderFragment: $it")
           when(it) {
               null -> viewModel.softLoadPodiumDetails(args.podiumId)
               else -> navigateToKind(it)
           }
        }
        observe()
    }

    private fun observe() {
        viewModel.podiumUserRatingError.observe(viewLifecycleOwner) {
            when(it) {
                PodiumWhoCanJoin.RATING_ONLY_HUNDRED -> {
                    showPodiumRatingError(
                        message = getString(R.string.podium_join_with_rating_with_hundred),
                        userRating = viewModel.user.userRatingPercent,
                        isPremium = viewModel.user.premium
                    )
                }
                PodiumWhoCanJoin.RATING_ABOVE_NINETY -> {
                    showPodiumRatingError(
                        message = getString(R.string.podium_join_with_rating_ninety_or_above),
                        userRating = viewModel.user.userRatingPercent,
                        isPremium = viewModel.user.premium
                    )
                }
                PodiumWhoCanJoin.INSUFFICIENT_COINS -> {
                    val podium = viewModel.podium.value ?: return@observe
                    showJoinPodiumWithInsufficientCoinsAlert(
                        coinBalance = viewModel.user.coinBalance,
                        joiningFee = podium.joiningFee
                    )
                }
                PodiumWhoCanJoin.JOINING_FEE_REQUIRED -> {
                    val podium = viewModel.podium.value ?: return@observe
                    showJoinPodiumWithCoinsAlert(joiningFee = podium.joiningFee,onProceed = {
                        // Proceed with joining after user confirms
                        proceedWithJoining(podium)
                    }, onCancel = {
                        popSafely()
                    })
                }
                else -> {/* Will not reach here */ }
            }
        }

        viewModel.onPodiumKindFound.observe(viewLifecycleOwner) {
            navigateToKind(it)
        }

        viewModel.onPodiumLoadError.observe(viewLifecycleOwner) {
            popSafely()
        }

        // Example: Show join confirmation dialog when needed
        // You can call this method when you want to show the join confirmation
        // showPodiumJoinConfirmationDialog("Elsa", "Let the game begin")
    }

    private var didPopBackstack: Boolean by Synchronize(false)

    private fun popSafely() {
        Log.w("PLF", "popSafely: didPopBackstack: $didPopBackstack")
        if (didPopBackstack) return
        didPopBackstack = true
        findNavController().popBackStack(R.id.nav_live_podium, true)
    }

    private fun navigateToKind(kind: PodiumKind) {
        Log.w("PLF", "navigateToKind: $kind")
        val action = when(kind) {
            PodiumKind.LECTURE ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveLectureFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
            PodiumKind.ASSEMBLY ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveAssemblyFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
            PodiumKind.ALONE ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveAloneFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
            PodiumKind.INTERVIEW ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveInterviewFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
            PodiumKind.MAIDAN ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveMaidanFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
            PodiumKind.THEATER ->
                PodiumLiveLoaderFragmentDirections.actionPodiumLiveLoaderFragmentToPodiumLiveTheaterFragment(args.podiumId, enableScrollForTab = args.enableScrollForTab)
        }
        val options = NavOptions.Builder().setPopUpTo(R.id.podiumLiveLoaderFragment, inclusive = true).build()
        findNavController().navigateSafe(action,options)
    }

    private fun showPodiumRatingError(userRating: String, message: String, isPremium: Boolean?) {
        showFlashatDialog {
            setTitle(getString(R.string.title_podium_alert_rating, userRating))
            setMessage(message)
            setIcon(R.drawable.ic_rating_less)
            setConfirmButtonVisible(isPremium == true)
            setCloseButton(title = R.string.common_cancel, icon = R.drawable.ic_close) {
                popSafely()
                true
            }
            setConfirmButton(R.string.restore_rating_header, R.drawable.ic_restore_rating, false) {
                popSafely()
                findNavController().navigateSafe(NavGraphHomeDirections.actionHomeBusinessFragmentToRestoreRatingFragment(false))
                true
            }
        }
    }
    
    private fun proceedWithJoining(podium: Podium) {
        // Navigate to the appropriate podium type after fee confirmation
        podium.kind?.let { navigateToKind(it) }
    }

}