package com.app.messej.ui.home.publictab.stars.search

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.UserStar
import com.app.messej.databinding.FragmentPublicStarsSearchBinding
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class   PublicStarsSearchFragment : Fragment() {

    private lateinit var binding: FragmentPublicStarsSearchBinding

    private var mAdapter: PublicStarsSearchAdapter? = null

    private val viewModel: PublicStarsSearchViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_stars_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(resources.getColor(R.color.white))
            }
        }
    }

    private fun observe() {
        viewModel.starsSearchList.observe(viewLifecycleOwner){
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun setup() {
        initAdapter()
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply {
                visibility = View.GONE
            }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = resources.getString(R.string.huddle_participants_no_result_found_text)
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }

        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicStarsSearchAdapter(object: PublicStarsSearchAdapter.ActionListener {
            override fun onItemClick(item: UserStar) {
                val action = HomePublicFragmentDirections.actionGlobalNavigationStarBroadcast(item.id)
                findNavController().navigateSafe(action)
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.starsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)

                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }
}