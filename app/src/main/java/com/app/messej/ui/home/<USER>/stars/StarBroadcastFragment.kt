package com.app.messej.ui.home.publictab.stars

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.appcompat.view.ActionMode
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentStarBroadcastBinding
import com.app.messej.databinding.LayoutActionModeSearchBinding
import com.app.messej.ui.chat.ActiveChatTracker
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportUserAllowed
import com.app.messej.ui.profile.PublicUserProfileFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class StarBroadcastFragment : BaseChatDisplayFragment(), MenuProvider {

    private lateinit var binding: FragmentStarBroadcastBinding

    private val args: StarBroadcastFragmentArgs by navArgs()

    override val viewModel: StarBroadcastViewModel by navGraphViewModels(R.id.nav_star_broadcast)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_star_broadcast, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.searchFooter.viewModel = viewModel
        binding.searchFooter.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.chatList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView?
        get() = null

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_star_broadcast_options, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> showSearchMode(true)
            R.id.action_more-> {
                findNavController().navigateSafe(StarBroadcastFragmentDirections.actionStarBroadcastFragmentToStarBroadcastOptionsMenuFragment())
            }
            else -> return false
        }
        return true
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.appBar.toolbar)
        }
        viewModel.announceChatEntry(true)
    }

    override fun onStop() {
        super.onStop()
        viewModel.announceChatEntry(false)
    }

    private fun setup() {
        viewModel.setStar(args.broadcaster)

        ActiveChatTracker.registerActiveScreen(ActiveChatTracker.ActiveChatScreen.BroadCastChat(args.broadcaster), viewLifecycleOwner)

        binding.privateMessagePrompt.setOnClickListener {
            viewModel.navigateToPrivateMessage()
        }

        setFragmentResultListener(PublicUserProfileFragment.USER_ACTION_EVENT) { _, bundle ->
            val result = bundle.getString(PublicUserProfileFragment.USER_ACTION_EVENT)
            if (result == PublicUserProfileFragment.USER_ACTION_EVENT_UNFOLLOW) {
                //TODO show undo snackbar
                findNavController().popBackStack()
            }
        }

        binding.appBar.toolbarLayout.setOnClickListener {

            val action = StarBroadcastFragmentDirections.actionGlobalPublicUserProfileFragment(args.broadcaster, popOnAction = true, context = UserProfileContext.STAR_BROADCAST)
            findNavController().navigateSafe(action)
        }

        binding.cancelReportText.apply {
            val cancelText = resources.getString(R.string.common_cancel)
            val text = resources.getString(R.string.broadcast_reported_info,cancelText)
//            val clickableSpan: ClickableSpan = object : ClickableSpan() {
//                override fun onClick(widget: View) {
//                    viewModel.cancelUserReport()
//                }
//            }
//            setText(text.highlightOccurrences(cancelText) {  clickableSpan})
//            movementMethod = LinkMovementMethod.getInstance()
        }
    }

    private fun observe() {
        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            val action = StarBroadcastFragmentDirections.actionGlobalNavigationChatPrivate(it.first, it.second)
            (activity as MainActivity).navController.navigateSafe(action)
        }

        viewModel.starUser.observe(viewLifecycleOwner) {
            binding.cancelReportBanner.visibility = if(it?.reported==true) View.VISIBLE else View.GONE
        }

        viewModel.actionIsStar.observe(viewLifecycleOwner) { star ->
            actionMode?.menu?.findItem(R.id.action_star)?.setIcon(if(star) R.drawable.ic_star else R.drawable.ic_unstar)
        }
        viewModel.onStarAction.observe(viewLifecycleOwner){
            when(it){
                BroadcastAction.STAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_starred), Toast.LENGTH_SHORT).show()
                BroadcastAction.UNSTAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_unstarred), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onStarUnfollowed.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(),getString(R.string.public_star_unfollowing_text,viewModel.starUser.value?.name),Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
            }
        }
        viewModel.lastSeenInfo.observe(viewLifecycleOwner) {
            binding.lastSeen = it
        }

        viewModel.onStarBLocked.observe(viewLifecycleOwner) {
            if (it) {
                //TODO show toast
                findNavController().popBackStack()
            }
        }

        viewModel.superstarHidden.observe(viewLifecycleOwner) {
            if (it) {
                //TODO show toast if needed
                findNavController().popBackStack()
            }
        }

        setFragmentResultListener(StarBroadcastOptionsMenuFragment.SELECTED_OPTION_KEY) { _, bundle ->
            when(bundle.getString(StarBroadcastOptionsMenuFragment.SELECTED_OPTION_KEY)) {
                StarBroadcastOptionsMenuFragment.SELECTED_OPTION_STARRED -> {
                    findNavController().navigateSafe(StarBroadcastFragmentDirections.actionStarBroadcastFragmentToStarBroadcastStarredFragment(viewModel.starId.value!!))
                }
                StarBroadcastOptionsMenuFragment.SELECTED_OPTION_UNFOLLOW -> {
                    viewModel.unfollowUser()
                }
                StarBroadcastOptionsMenuFragment.SELECTED_OPTION_REPORT -> {
                    ensureReportUserAllowed {
                        val user = viewModel.starUser.value ?: return@ensureReportUserAllowed
                        findNavController().navigateSafe(StarBroadcastFragmentDirections.actionGlobalReportFragment(ReportPackage.User(user.asBasicUser()).serialize()))
                    }
                }
                StarBroadcastOptionsMenuFragment.SELECTED_OPTION_BLOCK -> {
                    confirmBlock(getString(R.string.user_block_confirm_title, viewModel.starUser.value?.name?:""), R.string.user_block_confirm_subtitle) {
                        if(it) viewModel.blockUser()
                    }
                }
                StarBroadcastOptionsMenuFragment.SELECTED_OPTION_HIDE -> {
                    viewModel.hideSuperstar()
                }
            }
        }
    }

    private fun confirmBlock(title: String, @StringRes message: Int, onConfirm: (Boolean) -> Unit) {
        val builder = MaterialAlertDialogBuilder(requireContext()).setTitle(title).setMessage(resources.getString(message))
        builder.setPositiveButton(resources.getString(R.string.user_action_block)) { dialog, which ->
            onConfirm.invoke(true)
        }
        builder.setNegativeButton(resources.getString(R.string.common_cancel)) { dialog, which ->

        }
        builder.show()
    }

    override val provideAdapter: ChatAdapter
        get() = ChatAdapter(layoutInflater, viewModel.user.id, allowReplySwipe(), this)

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_star_broadcast_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_star -> viewModel.toggleStar()
                        R.id.action_copy -> viewModel.copySelection()
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun onItemLike(item: AbstractChatMessage, position: Int) {
        viewModel.likeItem(item)
    }

    var searchBinding: LayoutActionModeSearchBinding? = null

    private fun showSearchMode(show: Boolean) {
        if(show) {
            val callback = object:ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?,menu: Menu?): Boolean {
                    val viewB: LayoutActionModeSearchBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_action_mode_search, null, false)
                    viewB.lifecycleOwner = viewLifecycleOwner
                    mode?.customView = viewB.root
                    searchBinding = viewB

                    viewB.apply {
                        keyword = viewModel.searchText
                        showKeyboard(searchBox)
                    }
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?,menu: Menu?): Boolean {
                    searchBinding?.apply {
                        showKeyboard(searchBox)
                    }
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?,item: MenuItem?): Boolean {
                    return false
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.clearSearch()
                }
            }
            actionMode?.finish()
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }
}