package com.app.messej.ui.home.publictab.podiums.challenges

import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.animation.OvershootInterpolator
import android.widget.Toast
import androidx.compose.runtime.mutableStateOf
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.BoardData
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge.ChallengeStatus
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.data.model.socket.BoxChallengeLineDrawnPayload
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemBoxChallengeParticipantTileBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardBoxBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardBoxSpeakerOverlayBinding
import com.app.messej.databinding.LayoutPodiumChatOverlayUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoard
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.Duration

class PodiumBoxChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener, private val boxListener: BoxChallengeEventListener): PodiumChallengePresenter(holder, challenge, challengeListener)  {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardBoxBinding
    private var playerOverlayBinding: LayoutPodiumChallengeBoardBoxSpeakerOverlayBinding? = null
    private var chatOverlayBinding: LayoutPodiumChatOverlayUserJoinedBinding? = null

    private var gameBoard = mutableStateOf(BoxChallengeBoardModel())

    interface BoxChallengeEventListener {
        fun showHowToPlayOverlay(millis: Long)

        fun submitLine(line: BoxChallengeBoardModel.Line, player: ChallengePlayer, gameStatus: ConFourGameStatus?)

        fun sendTimeOut(player: ChallengePlayer, currentPlayerId: Int)

    }

    override fun setupView() {
        liveBinding = LayoutPodiumChallengeBoardBoxBinding.inflate(layoutInflater, holder, false)
        liveBinding.gameBoard.setContent {
            BoxChallengeBoard(gameBoard) { line ->
                drawLine(line)
            }
        }
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_boxes

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        gameBoard.value = gameBoard.value.copy().apply {
            challenge.boxData?.board?.let { nb ->
                board = nb
            }
        }
        refreshScores()
    }

    override fun cleanup() {
        super.cleanup()
        challengeListener.allowCustomBoard(false)
    }

    override fun onSpeakerClick(item: PodiumSpeakerUIModel.ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        return true
    }

    override fun refreshChallengeUI(status: ChallengeStatus) {
        try {
            scope.launch {
                if (status in listOf(ChallengeStatus.GAME_OVER,ChallengeStatus.ENDED) && uiState==UIState.LIVE) {
                    Log.w("PCPBX", "refreshChallengeUI: waiting for winner animation to finish")
                    if (status == ChallengeStatus.GAME_OVER) {
                        animateWin()
                    }
                    winnerAnimationJob?.join()
                    Log.w("PCPBX", "refreshChallengeUI: winner animation finished")
                    hideOverlays()
                    challengeListener.allowCustomBoard(false)
                    super.refreshChallengeUI(status)
                } else {
                    super.refreshChallengeUI(status)
                }
            }
        } catch (_: Exception) {}
    }

    override fun refreshLiveUI() {
        Log.w("PCPBX", "refreshLiveUI: $challenge", )
        scope.launch {
            Log.w("PCPBX", "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}")

            liveBinding.content.isVisible = false

            Log.w("PCPBX", "refreshLiveUI ${challenge.hasStarted} ${challenge.gameOver}" )
            if (challenge.hasStarted) {

                challengeListener.allowCustomBoard(true)
                liveBinding.gameBoard.isVisible = true

                challenge.boxData?.let {
                    gameBoard.value = gameBoard.value.copy().apply {
                        board = it.board
                    }
                }
                if (playerOverlayBinding==null) {
                    challengeListener.showSpeakerOverlay(true, 2f).apply {
                        val binding: LayoutPodiumChallengeBoardBoxSpeakerOverlayBinding =
                            DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_box_speaker_overlay, holder, false)
                        this.fillAddView(binding.root)
                        binding.playerOne.turnHolder.isVisible = false
                        binding.playerTwo.turnHolder.isVisible = false
                        binding.playerOne.playerTimer.isVisible = false
                        binding.playerTwo.playerTimer.isVisible = false
                        refreshScores()
                        playerOverlayBinding = binding
                    }
                }
                challengeListener.showChatOverlay(true).apply {
                    val binding = LayoutPodiumChatOverlayUserJoinedBinding.inflate(layoutInflater, holder, false)
                    this.fillAddView(binding.root)
                    chatOverlayBinding = binding
                }
                if (!challenge.gameOver) {
                    showPlayerTurn()
                } else {
                    // wait for game over event from BE
                }
            } else {
                liveBinding.gameBoard.isVisible = false
                hideOverlays()
                challengeListener.allowCustomBoard(false)
                liveBinding.content.isVisible = true
                liveBinding.content.showStartCountdown({
                                                           if (challengeListener.getLiveViewModel().iAmPartOfRunningChallenge.value == true) {
                                                               boxListener.showHowToPlayOverlay(challenge.countDownRemaining?.toMillis() ?: 0)
                                                           }
                                                       }, {
                                                           refreshLiveUI()
                                                       })
            }
        }
    }

    private fun ChallengePlayer.playerBinding(): ItemBoxChallengeParticipantTileBinding? {
        return when(this) {
            ChallengePlayer.PLAYER1 -> playerOverlayBinding?.playerOne
            ChallengePlayer.PLAYER2 -> playerOverlayBinding?.playerTwo
        }
    }

    private var lastLine: BoxChallengeBoardModel.Line? = null

    fun onLineDrawn(payload: BoxChallengeLineDrawnPayload) {
        Log.w("PCPBX", "onCoinDropped: $payload",)
        challenge.boxData = payload.boxData
        try {
            payload.lastLine?.let { line ->
                lastLine = line
                payload.lastDropPlayer?.let { player ->
                    gameBoard.value = gameBoard.value.copy().apply {
                        drawLine(line, player)
//                        if (gameStatus == ConFourGameStatus.WON) {
//                            getWinner()?.let { animateWin(it) }
//                        }
                    }
                }
            }
        } catch (e: Exception) {
            Toast.makeText(context, e.message, Toast.LENGTH_SHORT).show()
        }
        showPlayerTurn()
    }

    private fun drawLine(line: BoxChallengeBoardModel.Line) {
        try {
            if (!iAmCurrentPlayer) return
            val player = meAsPlayer ?: return
            val result = gameBoard.value.mockDrawLine(line, player)
            lastLine = line
            Log.w("PCPBX", "drawLine: player $player | line: $line | result; $result")
            if (result.first != BoxChallengeBoardModel.LineResult.Invalid) {
                boxListener.submitLine(line, player, result.second)
            } else {
                Toast.makeText(context, "Line already drawn", Toast.LENGTH_SHORT).show()
            }
            challenge.boxData?.currentPlayerUserId = null
        } catch (_: Exception) {
        }
    }

    var winnerAnimationJob: Job? = null

    private fun animateWin() {
        winnerAnimationJob?.cancel()
        val winners = challengeWinners

        winnerAnimationJob = scope.launch {
            winners.forEach { winner ->
                val player = winner.player?: return@forEach
                val playerBinding = player.playerBinding()
                playerBinding?.winnerImage?.apply {
                    isVisible = true
                    setImageResource(player.trophyRes())
                    scaleX = 0.1f
                    scaleY = 0.1f
                    this.animate().apply {
                        interpolator = OvershootInterpolator()
                        setDuration(500)
                        scaleX(1f)
                        scaleY(1f)
                        start()
                    }
                }
            }
            delay(5000)
        }.apply {
            invokeOnCompletion {
                winnerAnimationJob = null
            }
        }
    }

    private fun refreshScores() {
        Log.w("PCPBX", "refreshScores" )

        fun ItemBoxChallengeParticipantTileBinding.updateView(player: ChallengePlayer) {
            player.getScore()?.let { sc ->
                this.speaker = sc
                this.score = gameBoard.value.countBoxes(player.slotState).toString()
                val color = ContextCompat.getColor(context, player.colorRes())
                this.turnHolder.setCardBackgroundColor(color)
                this.turnText.setTextColor(ContextCompat.getColor(context, player.colorOnColorRes()))
                this.scoreHolder.setCardBackgroundColor(color)
                this.scoreText.setTextColor(ContextCompat.getColor(context, player.colorOnColorRes()))
                this.timerProgress.setIndicatorColor(color)
            }
        }
        playerOverlayBinding?.apply {
            playerOne.updateView(ChallengePlayer.PLAYER1)
            playerTwo.updateView(ChallengePlayer.PLAYER2)
        }
    }

    override val boardData: BoardData?
        get() = challenge.boxData

    private fun showPlayerTurn() {

        scope.launch {
            tickerJob?.cancelAndJoin()
            Log.d("PCP", "tickerJob canceled in: showPlayerTurn")

            playerOverlayBinding?.apply {
                Log.w("PCPBX", "showPlayerTurn: refreshing scores...")
                refreshScores()

                val currentPlayer = currentPlayer ?: return@apply
                val playerBinding = currentPlayer.playerBinding() ?: return@apply
                Log.w("PCPBX", "showPlayerTurn: currentPlayer $currentPlayer")

                playerBinding.currentPlayer = true
                playerBinding.playerTimer.isVisible = true
                playerBinding.turnHolder.isVisible = iAmCurrentPlayer

                currentPlayer.otherPlayer.playerBinding()?.apply {
                    this.currentPlayer = false
                    this.playerTimer.isVisible = false
                    this.turnHolder.isVisible = false
                }
                Log.w("PCPBX", "showPlayerTurn: currentPlayer left time ${getCurrentPlayerTimeLeft().seconds}")
                showTimer(playerBinding.timerCount, playerBinding.timerProgress, getCurrentPlayerTimeLeft()) { cancelled ->
                    playerBinding.playerTimer.isVisible = false
                    playerBinding.turnHolder.isVisible = false
                    if (!cancelled) {
//                        delay(2000)
                        //code will only reach here if the coin dropped event is not triggered even after timer runs out.
                        currentPlayerId?.let { boxListener.sendTimeOut(currentPlayer, it) }
                    }
                }
                enableDropControlsIfRequired()
            }
        }
    }

    protected fun getCurrentPlayerTimeLeft(): Duration {
        return challenge.boxData?.turnEndTime?.let {
            Log.w("PCPC4", "getCurrentPlayerTimeLeft: start ${challenge.boxData?.parsedTurnStartTime} end ${challenge.boxData?.turnEndTime} left ${DateTimeUtils.durationFromNowToFuture(it)?.seconds}")
            DateTimeUtils.durationFromNowToFuture(it)
        }?: Duration.ZERO
    }

    private var controlsEnabled = false

    private fun enableDropControlsIfRequired() {
        Log.w("PCPBX", "enableDropControlsIfRequired: $iAmCurrentPlayer" )
        controlsEnabled = iAmCurrentPlayer
        if (!iAmCurrentPlayer) return
    }

    override fun showFinalScores(): Boolean  = false

    override fun onUserJoined(user: PodiumParticipant) {
        chatOverlayBinding?.addToFlow(user)
    }
}