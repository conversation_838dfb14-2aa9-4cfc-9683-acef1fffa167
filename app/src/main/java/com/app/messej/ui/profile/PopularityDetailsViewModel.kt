package com.app.messej.ui.profile

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.entity.UserStatsResult
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PopularityDetailsViewModel(application: Application) : AndroidViewModel(application) {
    private val profileRepo = ProfileRepository(application)

    private val _popularity = MutableLiveData<UserStatsResult?>()
    val popularity: LiveData<UserStatsResult?> = _popularity

    fun getPopularity() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = profileRepo.getPopularity()) {
                is ResultOf.Success -> {
                    _popularity.postValue(result.value)
                }
                is ResultOf.APIError -> {
                    Log.e("PublicUserProfileVM", "API Error: ${result.error}")
                }
                is ResultOf.Error -> {
                    Log.e("PublicUserProfileVM", "Error: ${result.exception.message}")
                }
            }
        }
    }
}