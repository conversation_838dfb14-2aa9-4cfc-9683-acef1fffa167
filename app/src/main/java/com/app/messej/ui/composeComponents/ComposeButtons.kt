package com.app.messej.ui.composeComponents

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.messej.R
import java.util.Locale

@Composable
fun CustomLargeRoundButton(
    modifier: Modifier = Modifier,
    @StringRes text: Int = R.string.common_submit,
    isLoading: Boolean = false,
    isEnabled: Boolean = true,
    isTextCaps: Boolean = true,
    icon: (@Composable () -> Unit)? = null,
    onClick: () -> Unit
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    val isButtonEnabled = isEnabled && !isLoading

    Box(modifier = modifier
        .clip(shape = shape)
        .fillMaxWidth()
    ) {
        Button(
            modifier = Modifier
                .height(height = 60.dp)
                .fillMaxWidth(),
            enabled = isButtonEnabled,
            colors = ButtonDefaults.buttonColors(
                backgroundColor = colorAttributeResource(id = R.attr.buttonBaseColor),
                disabledBackgroundColor = colorAttributeResource(id = R.attr.buttonBaseColor).copy(alpha = 0.3F)
            ), shape = shape,
            content = {
                Row(modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    icon?.let {
                        it()
                        Spacer(modifier = Modifier.width(width = dimensionResource(id = R.dimen.element_spacing)))
                    }
                    Text(
                        text = if (isTextCaps) stringResource(id = text).uppercase(Locale.getDefault()) else stringResource(id = text),
                        style = FlashatComposeTypography.defaultType.button.copy(fontSize = 16.sp),
                        color = if (isButtonEnabled) colorResource(id = R.color.textColorOnPrimary)
                        else colorAttributeResource(id = R.attr.buttonBaseColor).copy(alpha = 0.8F)
                    )
                }
            },
            onClick = onClick
        )
        if (isLoading) {
            LinearProgressIndicator(
                modifier = Modifier
                    .clip(shape = shape)
                    .align(alignment = Alignment.BottomCenter)
                    .fillMaxWidth(),
                color = colorResource(id = R.color.colorPrimary)
            )
        }
    }
}

@Composable
fun CustomOutlinedButton(
    modifier: Modifier = Modifier,
    @StringRes text: Int = R.string.common_submit,
    isEnabled: Boolean = true,
    icon: (@Composable () -> Unit)? = null,
    textColor: Color = colorResource(id = R.color.colorError),
    onClick: () -> Unit
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))

    OutlinedButton (
        modifier = modifier
            .height(height = 60.dp)
            .fillMaxWidth(),
        enabled = isEnabled,
        border = BorderStroke(width = 1.5.dp, color = textColor),
        colors = ButtonDefaults.outlinedButtonColors(
            backgroundColor = Color.Transparent
        ), shape = shape,
        content = {
            Row(modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                icon?.let {
                    it()
                    Spacer(modifier = Modifier.width(width = dimensionResource(id = R.dimen.element_spacing)))
                }
                Text(
                    text = stringResource(id = text).uppercase(Locale.getDefault()),
                    style = FlashatComposeTypography.defaultType.button.copy(fontSize = 16.sp),
                    color = textColor
                )
            }
        },
        onClick = onClick
    )
}

@Preview
@Composable
private fun OutlinedButtonPreview() {
    CustomOutlinedButton(
        onClick = {

        }
    )
}

@Preview
@Composable
private fun ButtonPreview() {
    CustomLargeRoundButton(
        onClick = { },
        isLoading = true,
//        icon = {
//            Icon(
//                painter = painterResource(R.drawable.ic_round_arrow_right),
//                contentDescription = null
//            )
//        }
    )
}

@Composable
fun CustomSmallOutlinedButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    contentColor: Color = colorResource(id = R.color.colorPrimary),
    borderColor: Color = contentColor,
    text: String,
    enabled: Boolean = true,
    @DrawableRes trailingIcon: Int? = null
) {
    OutlinedButton(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        border = BorderStroke(width = 1.dp, color = borderColor),
        colors = ButtonDefaults.outlinedButtonColors(
            backgroundColor = Color.Transparent,
            contentColor = contentColor
        )
    ) {
        Text(
            text = text,
            style = FlashatComposeTypography.defaultType.button,
            color = contentColor
        )
        trailingIcon?.let {
            Icon(
                painter = painterResource(id = it),
                tint = contentColor,
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
private fun CustomOutlinedButtonPreview() {
    CustomSmallOutlinedButton(
        onClick = {},
        enabled = false,
        text = "Outlined Button",
        trailingIcon = R.drawable.ic_caret_right
    )
}

@Composable
fun CustomSmallButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    contentColor: Color = colorResource(id = R.color.colorPrimary),
    text: String,
    enabled: Boolean = true,
    @DrawableRes trailingIcon: Int? = null,
    @DrawableRes leadingIcon: Int? = null

) {
    Button(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        colors = ButtonDefaults.buttonColors(
            backgroundColor = colorResource(R.color.colorSurfaceSecondary),
            contentColor = contentColor
        )
    ) {
        leadingIcon?.let {
            Icon(
                painter = painterResource(id = it),
                tint = contentColor,
                contentDescription = null
            )
        }
        Text(
            text = text,
            style = FlashatComposeTypography.defaultType.button,
            color = contentColor
        )
        trailingIcon?.let {
            Icon(
                painter = painterResource(id = it),
                tint = contentColor,
                contentDescription = null
            )
        }
    }
}
@Preview
@Composable
private fun CustomSmallButtonPreview() {
    CustomSmallButton(
        onClick = {},
        enabled = false,
        text = "small Button",
        leadingIcon = R.drawable.ic_caret_right
    )
}
