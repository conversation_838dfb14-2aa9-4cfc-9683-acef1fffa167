package com.app.messej.ui.home.settings.levels.upgradeLevel


import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.app.messej.R
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer

interface onItemSelectedListener{
    fun onUpgradeLevelClick(selectedCitizenship: UserCitizenship?,requiredFlix:Int?)
}
@Composable
fun UserLevelComposeScreen(viewModel: UpgradeUserLevelViewModel,listener: onItemSelectedListener) {

    val getUpgradeUserLevelState by viewModel.getUpgradeUserLevel.observeAsState()
    val isLoading by viewModel.loading.observeAsState()
    val isSubmitting by viewModel.loading.observeAsState()

    val currentUserCitizenship = getUpgradeUserLevelState?.currentUserLevel

    var selectedCitizenship by remember { mutableStateOf<UserCitizenship?>(null) }
    var requiredFlix by remember { mutableStateOf<Int?>(null) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(all = dimensionResource(id = R.dimen.activity_margin))
    ) {
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.extra_margin))
        Text(
            text = stringResource(id = R.string.upgrade_user_level_header),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            style = FlashatComposeTypography.defaultType.subtitle1,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.extra_margin))
        if (isLoading == true) {
            ComposeShimmerListLayout(itemCount = 3) { brush ->
                ShimmerUserLevelCitizenshipComponent(brush = brush)
            }
        } else {

            if (getUpgradeUserLevelState?.userUpgradeLevelMapping?.Officer != null) {
                CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
                getUpgradeUserLevelState?.userUpgradeLevelMapping?.Officer?.let {
                    UserLevelCitizenshipComponent(
                        citizenship = UserCitizenship.OFFICER, flixAmount = it, currentUserCitizenship = currentUserCitizenship, selectedCitizenship = selectedCitizenship, actionClick = {
                            if(isLoading==true) return@UserLevelCitizenshipComponent
                            selectedCitizenship = UserCitizenship.OFFICER
                            requiredFlix = it
                        })
                }
            }
            if (getUpgradeUserLevelState?.userUpgradeLevelMapping?.Ambassador != null) {
                CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
                getUpgradeUserLevelState?.userUpgradeLevelMapping?.Ambassador?.let {
                    UserLevelCitizenshipComponent(
                        citizenship = UserCitizenship.AMBASSADOR, flixAmount = it, currentUserCitizenship = currentUserCitizenship, selectedCitizenship = selectedCitizenship, actionClick = {
                            if(isLoading==true) return@UserLevelCitizenshipComponent
                            selectedCitizenship = UserCitizenship.AMBASSADOR
                            requiredFlix = it
                        })
                }
            }
            if (getUpgradeUserLevelState?.userUpgradeLevelMapping?.Minister != null) {
                CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
                getUpgradeUserLevelState?.userUpgradeLevelMapping?.Minister?.let {
                    UserLevelCitizenshipComponent(
                        citizenship = UserCitizenship.MINISTER, flixAmount = it, currentUserCitizenship = currentUserCitizenship, selectedCitizenship = selectedCitizenship, actionClick = {
                            if(isLoading==true) return@UserLevelCitizenshipComponent
                            selectedCitizenship = UserCitizenship.MINISTER
                            requiredFlix = it
                        })
                }
            }
        }
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.extra_margin))
        CustomLargeRoundButton(
            isEnabled = selectedCitizenship != null,
            isLoading = isSubmitting == true,
            isTextCaps = false,
            icon = {
            Icon(painter = painterResource(id = R.drawable.ic_promo_upgrade), contentDescription = null, tint = if(selectedCitizenship!=null)Color.Unspecified else colorResource(R.color.colorAlwaysLightSecondaryDarker))
        }, text = R.string.common_upgrade, onClick = {listener.onUpgradeLevelClick(selectedCitizenship,requiredFlix)})
    }



}
