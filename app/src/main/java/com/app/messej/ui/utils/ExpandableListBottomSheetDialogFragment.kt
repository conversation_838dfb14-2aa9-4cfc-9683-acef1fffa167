package com.app.messej.ui.utils

import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.View
import com.app.messej.ui.utils.BottomSheetExtensions.setExpandableBehavior
import com.app.messej.ui.utils.BottomSheetExtensions.setMatchParent
import com.app.messej.ui.utils.DataFormatHelper.pxToDp
import com.app.messej.ui.utils.FragmentExtensions.ifStillAttached
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

abstract class ExpandableListBottomSheetDialogFragment : BottomSheetDialogFragment() {

    override fun onStart() {
        super.onStart()
        setMatchParent()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        dialog.setExpandableBehavior()
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            state = BottomSheetBehavior.STATE_HALF_EXPANDED
        }
    }

    protected fun snapToBottom(view: View, root: View) {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            addBottomSheetCallback(object: BottomSheetCallback() {
                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    try {
                        ifStillAttached {
                            Log.w("FLCOM", "onSlide: $slideOffset")
                            if (slideOffset >= 0) {
                                val offset = (bottomSheet.top - expandedOffset)
                                Log.w("FLCOM", "onSlide: factor: ${offset.pxToDp(requireContext())}dp")
                                view.translationY = offset * -1f
                            }
                        }
                    } finally { }
                }

                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (state== BottomSheetBehavior.STATE_COLLAPSED) {
                        state = BottomSheetBehavior.STATE_HIDDEN
                    }
//                    val st = when(newState) {
//                        1 -> "STATE_DRAGGING"
//                        2 -> "STATE_SETTLING"
//                        3 -> "STATE_EXPANDED"
//                        4 -> "STATE_COLLAPSED"
//                        5 -> "STATE_HIDDEN"
//                        6 -> "STATE_HALF_EXPANDED"
//                        else -> "STATE_UNKNOWN"
//                    }
//                    Log.w("FLCOM", "onStateChanged: $st")
                }
            }.apply {
                root.post { onSlide(root.parent as View, 0f) }
            })
        }
    }
}