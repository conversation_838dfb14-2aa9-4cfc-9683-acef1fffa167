package com.app.messej.ui.home.publictab.myETribe.contactETribe

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import com.app.messej.R
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.ui.composeComponents.ComposeTextField
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.CustomOutlinedButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.ETribeUtil.setupText

@Composable
fun ContactETribeScreen(
    viewModel: ContactETribeViewModel,
    onCancelButtonClick: () -> Unit,
    args: ContactETribeFragmentArgs
) {
    val selectedDropdown by viewModel.selectedDropdown.observeAsState()
    val dropdownList = viewModel.dropdownList
    val contactETribeTotalList = viewModel.contactETribeList
    val selectedContactETribeOptions by viewModel.contactETribeOptions.observeAsState()
    val scrollState = rememberScrollState()
    val isNextButtonEnabled by viewModel.isSNextButtonEnabled.observeAsState()
    val isSubmitting by viewModel.isSubmitting.observeAsState()

    Column(
        modifier = Modifier
            .verticalScroll(state = scrollState)
            .fillMaxSize()
        .padding(all = dimensionResource(id = R.dimen.activity_margin))
    ) {

        Text(
            text = stringResource(id = R.string.e_tribe_enter_message_title),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.body2
        )
        CustomVerticalSpacer(space = R.dimen.activity_margin)
        ComposeTextField(
            state = viewModel.messageTextFieldState,
            singleLine = false,
            onValueChange = viewModel::setMessageText,
            totalCharacterLimit = 200,
            imeAction = ImeAction.Unspecified,
            placeHolderText = stringResource(id = R.string.common_enter_message)
        )
        CustomVerticalSpacer(space = R.dimen.extra_margin)
        Text(
            text = stringResource(id = R.string.contact_e_tribe_member_selection_text),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.body2
        )
        repeat(times = dropdownList.size) {
            val item = dropdownList[it]
            CustomRadioButtonItem(
                text = stringResource(id = item.setupText()) + " (${
                    when(item) {
                        ETribeTabs.All -> args.allUsersCount
                        ETribeTabs.Active -> args.activeUsersCount
                        ETribeTabs.Inactive -> args.inactiveUsersCount
                    }
                })",
                isSelected = selectedDropdown == item,
                onClick = {
                    viewModel.setDropdown(item = item)
                }
            )
        }

        CustomVerticalSpacer(space = R.dimen.extra_margin)
        Text(
            text = stringResource(id = R.string.e_tribe_contact_options_title),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.body2
        )
        CustomVerticalSpacer(space = R.dimen.activity_margin)
        repeat(times = contactETribeTotalList.size) {
            val item = contactETribeTotalList[it]
            CustomCheckBoxButtonItem(
                text = stringResource(id = item.setupText()),
                isSelected = selectedContactETribeOptions?.contains(element = item) == true,
                onClick = { isChecked ->
                    if (isChecked) viewModel.addContactETribeOptions(item)
                    else viewModel.removeContactETribeOption(item)
                }
            )
        }

        CustomVerticalSpacer(space = R.dimen.extra_margin)
        CustomLargeRoundButton(
            isLoading = isSubmitting == true,
            isEnabled = isNextButtonEnabled == true,
            text = R.string.common_next,
            onClick = viewModel::contactETribe
        )

        CustomVerticalSpacer(space = R.dimen.activity_margin)
        CustomOutlinedButton(
            text = R.string.common_cancel,
            isEnabled = isSubmitting != true,
            onClick = onCancelButtonClick
        )
    }
}