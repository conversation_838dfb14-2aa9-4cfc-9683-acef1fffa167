package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.app.Application
import android.util.Log
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeContributionType.CONTRIBUTOR
import com.app.messej.data.model.enums.ChallengeContributionType.FREE
import com.app.messej.data.model.enums.ChallengeContributionType.SELF
import com.app.messej.data.model.enums.ChallengeContributionType.SPEAKERS
import com.app.messej.data.model.enums.ChallengeTimer
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.socket.repository.PodiumEventRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.AsyncExtensions.collectAsync
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.internal.toLongOrDefault
import java.time.Duration

@OptIn(FlowPreview::class)
class PodiumCreateChallengeViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val podiumEventRepo = PodiumEventRepository
    private val profileRepo = ProfileRepository(application)
    private val accountRepo = AccountRepository(application)
    private val businessRepo = BusinessRepository(application) //for single contributor search
    val user: CurrentUser get() = accountRepo.user

    val isGoldenUser : Boolean get() = user.citizenship == UserCitizenship.GOLDEN

    companion object {
        private const val MIN_PRICE_OTHER = 100
        private const val MIN_PRICE_PENALTY_SPEAKERS = 20
        private const val MIN_PRICE_BOX_CHALLENGE = 50
        private const val STEP_PRICE_BOX_CHALLENGE = 50
    }

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    private val _challengeId = MutableLiveData<String>()
    val challengeId: LiveData<String> = _challengeId

    private val _challengeType = MutableLiveData<ChallengeType>()
    val challengeType : LiveData<ChallengeType> = _challengeType

    val podiumDetailsLoading = MutableLiveData(false)

    private val _podium = MutableLiveData<Podium?>(null)
    val podium: LiveData<Podium?> = _podium

    private val _activeChallenge = MutableLiveData<PodiumChallenge?>(null)
    val activeChallenge: LiveData<PodiumChallenge?> = _activeChallenge

    data class SelectableSpeakerUIModel(
        var speaker: PodiumSpeaker,
    ) : BaseObservable() {
        @get:Bindable
        var selected: Boolean = false
            set(value) {
                field = value
                notifyPropertyChanged(BR.selected)
            }
    }

    private fun MutableLiveData<MutableList<SelectableSpeakerUIModel>>.addAndPostSpeaker(item: PodiumSpeaker) {
        this.value?.find { it.speaker.id == item.id }?.let {
            it.speaker = item
            return
        }
        this.postValue(this.value?.apply {
            add(
                SelectableSpeakerUIModel(
                    item
                )
            )
        })
    }

    private fun MutableLiveData<MutableList<SelectableSpeakerUIModel>>.removeAndPostSpeaker(id: Int) {
        this.postValue(this.value?.apply {
            removeIf { it.speaker.id == id }
        })
    }

    private fun MutableLiveData<MutableList<SelectableSpeakerUIModel>>.mapAndPost(list: MutableList<PodiumSpeaker>) {
        val existingSpeakers = value.orEmpty()
        val mapped = list.distinctBy { it.id }.map { ps ->
            val uiModel = existingSpeakers.find { it.speaker.id == ps.id }?: SelectableSpeakerUIModel(
                ps.copy(
                    name = nickNames.nickNameOrName(ps)
                )
            )
            uiModel.apply {
                speaker = ps.copy(
                    name = nickNames.nickNameOrName(ps)
                )
            }
        }.toMutableList()
        this.postValue(mapped)
    }

    init {

        collectAsync(PodiumEventRepository.speakerFlow.filter { _podiumId.value == it.first }) {
            _speakersForFacilitator.addAndPostSpeaker(it.second)
            _speakersForParticipant.addAndPostSpeaker(it.second)
        }

        collectAsync(podiumEventRepo.exitSpeakerFlow.filter { _podiumId.value == it.podiumId }) {
            _speakersForFacilitator.removeAndPostSpeaker(it.userId)
            _speakersForParticipant.removeAndPostSpeaker(it.userId)
        }
    }

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    fun setChallenge(id: String, challengeId : String? = null, invitedParticipants: String? = null) {
        if (_podiumId.value == id) return
        _podiumId.value = id
        challengeId?.let {
            _challengeId.postValue(it)
        }
        loadPodiumDetails(id, challengeId, invitedParticipants = invitedParticipants)
    }

    fun setPodium(pod: Podium, challenge: PodiumChallenge) {
        _podiumId.value = pod.id
        _challengeId.value = challenge.challengeId
        _podium.postValue(pod)
        _activeChallenge.postValue(challenge)
        _challengeType.postValue(challenge.challengeType)
    }

    fun setChallengeType(challengeType: ChallengeType) {
        _challengeType.postValue(challengeType)
    }

    private fun loadPodiumDetails(id: String, challengeId: String?, invitedParticipants: String? = null) {
        podiumDetailsLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            podiumRepository.getPodiumById(id)?.let {
                if (!it.isLive) it.likes = 0
                _podium.postValue(it)
            }
            getPodiumDetails(id,invitedParticipants)?.also {pod ->
                _podium.postValue(pod)
                withContext(Dispatchers.Main) {
                    val sortedList = pod.speakers.partition { it.id == user.id }.toList().flatten().toMutableList()
                    _speakersForFacilitator.mapAndPost(sortedList)
                    _speakersForParticipant.mapAndPost(sortedList)
                }

            }
            challengeId?.let {
                getChallengeDetails(id, it)?.also { challenge ->
                    Log.w("PCVM", "loadPodiumDetails: $it")
                    _activeChallenge.postValue(challenge)
                    _challengeType.postValue(challenge.challengeType)
                    challenge.facilitator?.let {
                        selectFacilitator(it.id)
                    }
                }
            }
            podiumDetailsLoading.postValue(false)
        }
    }

    val onPodiumLoadError = LiveEvent<String>()
    private suspend fun getPodiumDetails(id: String, invitedParticipants: String? = null): Podium? {
        when (val result = podiumRepository.getPodiumDetails(id,invitedParticipants)) {
            is ResultOf.APIError -> {
                onPodiumLoadError.postValue(result.error.toString())
            }

            is ResultOf.Error -> {
                onPodiumLoadError.postValue(result.exception.toString())
            }

            is ResultOf.Success -> {
                return result.value
            }
        }
        return null
    }

    private suspend fun getChallengeDetails(id: String, challengeId: String): PodiumChallenge? {
        when (val result = podiumRepository.getChallengeDetails(id, challengeId)) {
            is ResultOf.APIError -> {
                onPodiumLoadError.postValue(result.error.toString())
            }

            is ResultOf.Error -> {
                onPodiumLoadError.postValue(result.exception.toString())
            }

            is ResultOf.Success -> {
                return result.value
            }
        }
        return null
    }

    /* Facilitator */

    val challengeCreated = LiveEvent<Boolean>()
    val createChallengeLoading = MutableLiveData(false)

    private val _speakersForFacilitator = MutableLiveData<MutableList<SelectableSpeakerUIModel>>(mutableListOf())
    val speakersForFacilitator: LiveData<MutableList<SelectableSpeakerUIModel>> = _speakersForFacilitator

    val selectedFacilitator = _speakersForFacilitator.map { list ->
        if (!list.isNullOrEmpty() && list.none { it.selected }) {
            val default = list.find { fac ->
                fac.speaker.id == user.id
            }?: list[0]
            default.selected = true
        }
        return@map list.find { it.selected }
    }

    fun selectFacilitator(userId: Int) {
        _speakersForFacilitator.value?.forEach {
            it.selected = it.speaker.id == userId
        }
        _speakersForFacilitator.postValue(_speakersForFacilitator.value)
    }

    fun createChallenge() {
        val facilitator = selectedFacilitator.value?: return
        val challengeType = challengeType.value?: return
        createChallengeLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.createChallenge(_podiumId.value.toString(), challengeType, facilitator.speaker.id)) {
                    is ResultOf.Success -> {
                        result.value.let {
                            _challengeId.postValue(it.challengeId)
                            Log.w("PCVM", "createChallenge: $it")
                            _activeChallenge.postValue(it)
                            if (it.facilitator?.id == user.id) { //if i'm the facilitator, move me to main screen
                                callMainScreenEvent(it.podiumId, user.id)
                            }
                            if (it.challengeType == ChallengeType.FLAGS) {
                                when (podiumRepository.createQuestion(it.challengeId, it.podiumId)) {
                                    is ResultOf.Success -> {
                                        challengeCreated.postValue(true)
                                    }
                                    is ResultOf.APIError -> { }
                                    is ResultOf.Error -> { }
                                }
                            } else {
                                challengeCreated.postValue(true)
                            }
                        }

                    }

                    is ResultOf.APIError -> { }
                    is ResultOf.Error -> { }
                }
                createChallengeLoading.postValue(false)
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "startChallenge: ${e.message}")
            }
        }
    }

    private fun callMainScreenEvent(podiumId: String, userId: Int) {
        if (podiumRepository.showInMainScreen(podiumId, userId)) {
            _speakersForFacilitator.value?.let {
                _speakersForFacilitator.postValue(it)
            }
        }
    }

    /* Participants */

    val minParticipants = _challengeType.map {
        PodiumChallenge.minParticipants(it)
    }

    private val _speakersForParticipant = MutableLiveData<MutableList<SelectableSpeakerUIModel>>(mutableListOf())
    val speakersForParticipant: LiveData<MutableList<SelectableSpeakerUIModel>> = _speakersForParticipant

    var challengeParticipantsCount = MutableLiveData<Int>(0)

    fun toggleParticipantSelection(user: SelectableSpeakerUIModel) {
        _speakersForParticipant.value?.find { it.speaker.id == user.speaker.id }?.let {
            it.selected = !it.selected
        }
        challengeParticipantsCount.postValue(_speakersForParticipant.value.orEmpty().count { it.selected })
    }

    var challengeParticipantInviteSent = LiveEvent<Long>()

    val challengeParticipantInviteLoading = MutableLiveData(false)

    fun sendChallengeParticipantInvite() {
        val userIds = _speakersForParticipant.value.orEmpty().filter { it.selected }.map { it.speaker.id }
        val pId = _podiumId.value ?: return
        val cId = _challengeId.value ?: return
        challengeParticipantInviteLoading.value = true
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.inviteToSpeak(pId, cId, userIds)) {
                    is ResultOf.Success -> {
                        val timeInvited = result.value.conFourParticipantRequestedTimeRemaining
                        challengeParticipantInviteSent.postValue(timeInvited)
                    }

                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }
                }
                challengeParticipantInviteLoading.value = false
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "inviteChallengeParticipant: ${e.message}")
            }
        }
    }

    var searchContributorKeyword = MutableLiveData<String>("")

    val liveContributorSearchList = searchContributorKeyword.switchMap {
        val podiumId = podiumId.value ?: return@switchMap null
        podiumRepository.getPodiumLiveUsersListingPager(podiumId, searchTerm = it, exclude = UserCitizenship.VISITOR).liveData.cachedIn(viewModelScope)
    }

    fun minimumConFourParticipantsSelected(): Boolean {
        val participantList = activeChallenge.value?.participantScores?.size ?: 0
        return chooseMoreConFourParticipantList.size + participantList >= 2
    }

    var selectOptionEnabled = MutableLiveData(false)
    val chooseMoreContributorsList = mutableListOf<PodiumParticipant>()
    var chooseMoreConFourParticipantList = mutableListOf<PodiumParticipant>()

    var conFourInviteSent = LiveEvent<Long>()
    fun sendConFourParticipantInvite(podiumId: String, challengeId: String?) {
        val userIds = chooseMoreConFourParticipantList.map { it.id }
        val cId = challengeId ?: return

        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.inviteToSpeak(podiumId, cId, userIds)) {
                    is ResultOf.Success -> {
                        val timeInvited = result.value.conFourParticipantRequestedTimeRemaining
                        conFourInviteSent.postValue(timeInvited)

                        val challenge = activeChallenge.value?: return@launch
//                        _activeChallengeAndScores.postValue(
//                            _activeChallengeAndScores.value?.copy(
//                                challenge = challenge.copy(
//                                    invitedParticipantsTime = result.value.invitedParticipantsTime
//                                )
//                            )
//                        )
                    }

                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "inviteChallengeParticipant: ${e.message}")
            }
        }
    }

    private val _validContributorSelection = MutableLiveData(false)
    val validContributorSelection : LiveData<Boolean> = _validContributorSelection
    fun validateContributorSelection() {
        _validContributorSelection.postValue(chooseMoreContributorsList.isNotEmpty())
    }

    private val _validConFourParticipantSelection = MutableLiveData(false)
    val validConFourParticipantSelection : LiveData<Boolean> = _validConFourParticipantSelection
    fun validConFourParticipantSelection() {
        _validConFourParticipantSelection.postValue(chooseMoreConFourParticipantList.isNotEmpty())
    }

    fun sendContributorRequests(){
        val challenge = activeChallenge.value?:return
        val podiumId = _podiumId.value?: return
        val challengeId = challenge.challengeId
        val fee = challenge.contributors.first().coinsSpent
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.appointContributor(
                podiumId, challengeId, type = ChallengeContributionType.SPEAKERS, contributors = chooseMoreContributorsList, fee = fee?.toLong()
            )) {
                is ResultOf.Success -> {
                    contributionSent.postValue(true)
                }
                is ResultOf.APIError -> {
                    contributionSent.postValue(false)
                }
                is ResultOf.Error -> {
                    contributionSent.postValue(false)
                }
            }
        }
    }

    /* Timer */

    val selectedTimer = MutableLiveData<ChallengeTimer?>(null)
    fun setTimerType(value : ChallengeTimer) {
        selectedTimer.postValue(value)
    }

    val customHours = MutableLiveData(0)
    val customMinutes = MutableLiveData(0)
    val customSeconds = MutableLiveData(0)

    val customDuration: MediatorLiveData<Duration> by lazy {
        val med = MediatorLiveData(Duration.ZERO)
        fun update() {
            val dur = Duration.ofHours(customHours.value?.toLong() ?: 0).plusMinutes(customMinutes.value?.toLong() ?: 0).plusSeconds(customSeconds.value?.toLong() ?: 0)
            med.postValue(dur)
        }
        med.addSource(customHours) { update() }
        med.addSource(customMinutes) { update() }
        med.addSource(customSeconds) { update() }
        med
    }

    val timeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val timer = selectedTimer.value
            val customDurationValid = if (timer == ChallengeTimer.TIMER_CUSTOM) {
                val dur = customDuration.value?: Duration.ZERO
                dur >= Duration.ofMinutes(1) && dur <= Duration.ofHours(4)
            } else true
            med.postValue(timer!=null && customDurationValid)
        }
        med.addSource(selectedTimer) { update() }
        med.addSource(customDuration) { update() }
        med
    }

    fun getSelectedTimerSeconds(): Int {
        return when (selectedTimer.value) {
            ChallengeTimer.TIMER_FIVE -> { 5 * 60 }
            ChallengeTimer.TIMER_TEN -> { 10 * 60 }
            ChallengeTimer.TIMER_TWENTY -> { 20 * 60 }
            ChallengeTimer.TIMER_FORTY -> { 40 * 60 }
            ChallengeTimer.TIMER_CUSTOM -> {
                customDuration.value?.seconds?.toInt()?:0
            }
            null -> { 0 }
        }
    }

    val setChallengeTimerLoading = MutableLiveData(false)
    var onChallengeTimerSet = LiveEvent<Boolean>()
    fun setChallengeTimer(timeInSeconds : Int) {
        val id = _podiumId.value?: return
        val challengeId = _challengeId.value?: return
        setChallengeTimerLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.setChallengeTimer(id, challengeId, timeInSeconds)) {
                    is ResultOf.Success -> {
                        _activeChallenge.postValue(result.value)
                        onChallengeTimerSet.postValue(true)
                    }
                    is ResultOf.APIError -> { }
                    is ResultOf.Error -> { }
                }
                setChallengeTimerLoading.postValue(false)
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "setChallengeTimer: ${e.message}")
            }
        }
    }

    /**
     * Contribution Logics
     */

    var contributorSearchKeyword = MutableLiveData("")
    private val contributorSearchTerm = contributorSearchKeyword.asFlow().debounce(500L).asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _contributionType = MutableLiveData<ChallengeContributionType>(null)
    val contributionType = _contributionType

    val priceAmountString = MutableLiveData<String?>(null)

    fun setContributionType(type: ChallengeContributionType) {
        priceAmountString.postValue("")
        contributionType.postValue(type)
    }

    private val _contributorsSearchList = MutableLiveData<List<DealsBeneficiary>>(null)
    val contributorsSearchList : MediatorLiveData<List<DealsBeneficiary>> by lazy {
        val med = MediatorLiveData<List<DealsBeneficiary>>()
        fun update() {
            _contributorsSearchList.value?.let {
                med.postValue(it.filter {ps ->
                    ps.id != _selectedContributor.value?.id || ps.id != _activeChallenge.value?.facilitator?.id
                })
            }
        }
        med.addSource(_selectedContributor) { update() }
        med.addSource(_activeChallenge) { update() }
        med.addSource(_contributorsSearchList) { update() }
        med
    }

    fun searchContributor(searchTerm:String) {
        viewModelScope.launch(Dispatchers.IO){
            when(val result = businessRepo.getBeneficiaryList(keyword = searchTerm)){
                is ResultOf.Success -> {
                    _contributorsSearchList.postValue(result.value.users.filter { it.id != user.id }) //filter facilitator out
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private val _selectedContributor = MutableLiveData<DealsBeneficiary>()
    val selectedContributor = _selectedContributor

    fun onContributorSelected(item: DealsBeneficiary) {
        _selectedContributor.postValue(item)
    }

    val minimumCoins = _challengeType.map {
        when(it) {
            ChallengeType.BOXES -> MIN_PRICE_BOX_CHALLENGE
            else -> MIN_PRICE_OTHER
        }
    }

    val minimumCoinsString = minimumCoins.map {
        it.toString()
    }

    val validContribution: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val price = priceAmountString.value.orEmpty().trim().toLongOrDefault(0)
            val min = if (_contributionType.value==SPEAKERS && _challengeType.value==ChallengeType.PENALTY) {
                MIN_PRICE_PENALTY_SPEAKERS
            } else minimumCoins.value?:MIN_PRICE_OTHER

            val step = if (_challengeType.value==ChallengeType.BOXES) STEP_PRICE_BOX_CHALLENGE else 1

            val priceValid = price >= min && price % step == 0L

            val valid = when(_contributionType.value) {
                CONTRIBUTOR -> priceValid && _selectedContributor.value != null
                FREE -> true
                SPEAKERS -> priceValid
                SELF -> priceValid
                else -> false
            }
            med.postValue(valid)
        }
        med.addSource(minimumCoins) { update() }
        med.addSource(_contributionType) { update() }
        med.addSource(priceAmountString) { update() }
        med.addSource(_selectedContributor) { update() }
        med
    }

    enum class ContributionErrorType {
        MIN_COINS,
        COIN_STEP
    }

    val speakerContributionError: MediatorLiveData<Pair<ContributionErrorType,Int>?> by lazy {
        val med = MediatorLiveData<Pair<ContributionErrorType,Int>?>(null)
        fun update() {
            val priceStr = priceAmountString.value.orEmpty().trim()
            val price = priceStr.toLongOrDefault(0)

            val min = if (_contributionType.value==SPEAKERS && _challengeType.value==ChallengeType.PENALTY) {
                MIN_PRICE_PENALTY_SPEAKERS
            } else minimumCoins.value?:MIN_PRICE_OTHER

            var error = if (priceStr.isBlank()) null
            else when(_contributionType.value) {
                FREE -> null
                else -> if(price < min) Pair(ContributionErrorType.MIN_COINS, min) else null
            }
            if(error==null && _challengeType.value==ChallengeType.BOXES) {
                if (price % STEP_PRICE_BOX_CHALLENGE != 0L) error = Pair(ContributionErrorType.COIN_STEP, STEP_PRICE_BOX_CHALLENGE)
            }
            med.postValue(error)
        }
        med.addSource(_contributionType) { update() }
        med.addSource(priceAmountString) { update() }
        med.addSource(minimumCoins) { update() }
        med
    }

    val sendContributionLoading = MutableLiveData(false)
    var contributionSent = LiveEvent<Boolean>()
    val onLowCoinBalance = LiveEvent<Boolean>()

    fun sendContribution(){
        val id = _podiumId.value?: return
        val challengeId = _challengeId.value?: return
        val contributorType = contributionType.value?: return
        sendContributionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val priceAmount = priceAmountString.value.orEmpty().trim().toLongOrDefault(0)
                if (contributorType != FREE) {
                    val points = when (val result = profileRepo.getAccountDetails()) {
                        is ResultOf.Success -> result.value.currentCoin
                        else -> {
                            sendContributionLoading.postValue(false)
                            return@launch
                        }
                    }
                    //since facilitator is not a contributor in case of LIKES challenge (SPEAKER contribution) , doesn't need to check coin balance
                    if (points <= priceAmount && (challengeType.value != ChallengeType.LIKES
                                && contributionType.value == SELF)) {
                        onLowCoinBalance.postValue(true)
                        sendContributionLoading.postValue(false)
                        return@launch
                    }
                }

                val speakersForContribution = _speakersForParticipant.value?: return@launch
                val contributors: List<AbstractUser>? = when (contributorType) {
                    SELF -> speakersForContribution.filter { it.speaker.id == user.id }.map {
                        it.speaker
                    }
                    SPEAKERS -> {
                        when (challengeType.value) {
                            ChallengeType.LIKES -> speakersForContribution.filter { it.speaker.id != user.id }
                            ChallengeType.CONFOUR,ChallengeType.PENALTY,ChallengeType.BOXES -> {
                                val participantIds = _activeChallenge.value?.participantScores?.map { it.userId }
                                speakersForContribution.filter { participantIds?.contains(it.speaker.id) == true }
                            }
                            else -> speakersForContribution
                        }.map {
                            it.speaker
                        }
                    }
                    CONTRIBUTOR -> {
                        val contributor = _selectedContributor.value!!  //Button will only enable when contributor is selected. So, asserted as non null.
                        arrayListOf(
                            PodiumSpeaker(
                                id = contributor.id,
                                name = contributor.name,
                                citizenship = contributor.citizenship,
                                username = contributor.username,
                                thumbnail = contributor.thumbnail,
                                verified = contributor.verified,
                                countryCode = contributor.countryCode,
                                membership = contributor.membership
                            )
                        )
                    }
                    FREE -> { null }
                    else -> { null }
                }

                when (val result = podiumRepository.appointContributor(id, challengeId, type = contributorType, contributors = contributors, fee = priceAmount)) {
                    is ResultOf.Success -> {
                        when(contributorType){
                            SELF, FREE -> {
                                startPodiumChallenge()
                            }
                            else -> {
                                contributionSent.postValue(true)
                            }
                        }
                    }
                    is ResultOf.APIError -> { }
                    is ResultOf.Error -> { }
                }
                sendContributionLoading.postValue(false)
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "sendContribution: ${e.message}")
            }
        }
    }

    val participantsCountNotMet = LiveEvent<IntRange>()
    var startChallengeAPIFinished = LiveEvent<Boolean>()

    /* Start Challenge */

    fun startPodiumChallenge() {
        val id = _podiumId.value?: return
        val challengeId = _challengeId.value?: return
        val challengeType = _challengeType.value?: return

        var speakers = speakersForFacilitator.value?: return

        speakers = when(challengeType) {
            ChallengeType.LIKES -> {
                speakers.filter { it.speaker.id != activeChallenge.value?.facilitator?.id }.toMutableList()
            }
            ChallengeType.MAIDAN,ChallengeType.GIFTS,ChallengeType.FLAGS -> speakers
            ChallengeType.PENALTY,ChallengeType.CONFOUR,ChallengeType.BOXES, ChallengeType.KNOWLEDGE  -> {
                val participantIds = activeChallenge.value?.participantScores?.map { it.userId }.orEmpty()
                speakers.filter { participantIds.contains(it.speaker.id) }.toMutableList()
            }
        }

        val range = PodiumChallenge.minParticipants(challengeType)..PodiumChallenge.maxParticipants(challengeType)
        if (speakers.count() !in range) {
            participantsCountNotMet.postValue(range)
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (podiumRepository.startPodiumChallenge(id, challengeId)) {
                    is ResultOf.Success -> {
                        startChallengeAPIFinished.postValue(true)
                    }
                    is ResultOf.APIError -> { }
                    is ResultOf.Error -> { }
                }
            } catch (e: Exception) {
                Log.d("PodiumLCVM", "startPodiumChallenge: ${e.message}")
            }
        }
    }

}