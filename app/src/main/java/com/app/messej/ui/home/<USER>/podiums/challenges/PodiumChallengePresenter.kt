package com.app.messej.ui.home.publictab.podiums.challenges

import android.content.Context
import android.content.res.Resources
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.annotation.CallSuper
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.setMargins
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.viewbinding.ViewBinding
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumChallengeSetupEvent
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.BoardData
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.ConFourData.Companion.TURN_DURATION_SECONDS
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.socket.ChallengeScoreUpdatePayload
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.getResourceProvider
import com.app.messej.databinding.LayoutPodiumChallengeBoardLikesResultScoreBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardResultBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardSetupBinding
import com.app.messej.databinding.LayoutPodiumChatOverlayUserJoinedBinding
import com.app.messej.databinding.LayoutPodiumUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.awaitEnd
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeInOut
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeOutIn
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.transitionToText
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.android.material.progressindicator.CircularProgressIndicator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.Duration
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.min
import kotlin.math.roundToInt


abstract class PodiumChallengePresenter(protected val holder: ViewGroup, protected var challenge: PodiumChallenge, protected val challengeListener: ChallengeEventListener) {

    protected var setupBinding: LayoutPodiumChallengeBoardSetupBinding
    protected var resultBinding: LayoutPodiumChallengeBoardResultBinding

    protected var scores: List<PodiumChallengeScore> = listOf()

    protected var tickerJob: Job? = null

    protected val scope = CoroutineScope(Dispatchers.Main)

    protected abstract val liveBinding: ViewBinding

    companion object {
        @JvmStatic
        @BindingAdapter("customMargin")
        fun setCustomMargins(view: View, margin: Float) { // This methods should not have any return type, = declaration would make it return that object declaration.
            val lp = view.layoutParams as MarginLayoutParams
            lp.setMargins(margin.toInt())
            view.layoutParams = lp
        }
    }

    protected val resources: Resources
        get() = challengeListener.getResources()

    protected val layoutInflater: LayoutInflater
        get() = challengeListener.getLayoutInflater()

    protected val context: Context
        get() = challengeListener.getContext()

    val challengeId: String
        get() = challenge.challengeId

    init {
        setupBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_setup, holder, false)
        resultBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_result, holder, false)
        setupView()
        scores = challenge.participantScores.orEmpty()
        refreshChallengeUI(challenge.status)
    }

    protected enum class UIState {
        SETUP, LIVE, RESULT
    }

    protected var uiState = UIState.SETUP
        private set

    interface ChallengeEventListener {

        fun allowCustomBoard(allow: Boolean)

        fun onRequireSpeakerTileDecor()

        fun getLiveViewModel(): PodiumLiveViewModel

        fun getLayoutInflater(): LayoutInflater

        fun getChatOverlay(): ConstraintLayout

        fun getResources(): Resources

        fun showChallengeResultVideo(winner: Boolean)

        fun onCloseChallenge()

        fun getContext() : Context

        fun showSpeakerOverlay(show: Boolean, aspect: Float = 1f): ConstraintLayout
        fun showChatOverlay(show: Boolean): ConstraintLayout
        fun showChatInputOverlay(show: Boolean): ConstraintLayout
    }

    protected fun setUIState(binding: ViewBinding, state: UIState) {
        Log.w("PCP", "${this@PodiumChallengePresenter} setUIState: $state, previous: $uiState")
        uiState = state
        holder.removeAllViews()
        holder.addView(binding.root, 0)
    }

    protected abstract fun setupView()

    fun onChallengeStatusChange(status: PodiumChallenge.ChallengeStatus) {
        Log.w("PCP", "${this@PodiumChallengePresenter} observe: activeChallengeStatus $status")
        refreshChallengeUI(status)
    }

    protected open val iAmParticipant: Boolean
        get() = scores.find { it.id == myUserId}!=null

    protected open val myScore: PodiumChallengeScore?
        get() = scores.find { it.id == myUserId}

    protected val myUserId: Int
        get() = challengeListener.getLiveViewModel().user.id

    open fun onUpdateChallengeAndScores(cs: PodiumChallenge) {
        challenge = cs
        val oldSize = scores.size
//        val positions = mutableMapOf<Int,Int>()
//        speakers.forEachIndexed { i, ps ->
//            positions[ps.id] = i
//        }
        scores = cs.participantScores
//            .filter {
//            positions[it.id]!=null
//        }.sortedBy {
//            positions[it.id]
//        }
//        Log.w("PCP", "onUpdateChallengeAndScores: scores: ${cs.participantScores.map { Pair(it.id, it.playerColor) }} reordered to: ${scores.map { Pair(it.id, it.color) }}")
//        Log.w("PCP", "${this@PodiumChallengePresenter} observe: activeChallengeAndScores: $scores | $uiState")
        val scoreSizeChanged = scores.size!=oldSize
        when (uiState) {
            UIState.RESULT -> refreshResultScores()
            UIState.LIVE -> onNewScoresAvailable(scoreSizeChanged)
            else -> {}
        }
        challengeListener.onRequireSpeakerTileDecor()
    }

    open fun decorateSpeakerTile(item: ActiveSpeakerUIModel, holder: ViewGroup, mainScreen: Boolean = false) : Boolean {
        return false
    }

    open fun onSpeakerClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        return false
    }

    fun ConstraintLayout.fillAddView(view: View) {
        removeAllViews()
        addView(view)
        val constraintSet= ConstraintSet().apply {
            clone(this)
            connect(view.id, ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, 0)
            connect(view.id, ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, 0)
            connect(view.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0)
            connect(view.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, 0)
        }
        constraintSet.applyTo(this)
    }

    protected open fun refreshChallengeUI(status: PodiumChallenge.ChallengeStatus) {
        scope.launch {
            messageAnimationJob?.cancelAndJoin()
            when (status) {
                PodiumChallenge.ChallengeStatus.SETUP -> {
                    challengeListener.allowCustomBoard(false)
                    showSetupScreen()
                }
                PodiumChallenge.ChallengeStatus.LIVE -> {
                    setUIState(liveBinding, UIState.LIVE)
                    refreshLiveUI()
                }
                PodiumChallenge.ChallengeStatus.GAME_OVER -> {
                    challengeListener.allowCustomBoard(false)
                    showResultUI()
                }
                else -> {}
            }
        }
    }

    protected abstract fun refreshLiveUI()

    open fun onSetupEvent(event: PodiumChallengeSetupEvent) {
        showSetupEventMessage(event)
    }

    /**
     * Called when new scores are available for this challenge.
     * Called via [com.app.messej.data.socket.PodiumSocketEvent.RX_CHALLENGE_SYNC] for all challenges
     * Called via [com.app.messej.data.socket.PodiumSocketEvent.RX_TX_CHALLENGE_SCORE_UPDATE] for LIKES, GIFTS and FLAG.
     */
    protected abstract fun onNewScoresAvailable(forceRefresh: Boolean)

    @CallSuper
    fun onScoreUpdate(ch: ChallengeScoreUpdatePayload) {
        scores.find { it.id == ch.recipientId }?.let {
            it.score += ch.incrementBy?:0.0
        }
        Log.w("PCP", "${this@PodiumChallengePresenter} onScoreUpdate: $ch")
        when(uiState) {
            UIState.SETUP -> {}
            UIState.LIVE -> onNewScoresAvailable(false)
            UIState.RESULT -> refreshResultScores()
        }
    }

    @CallSuper
    open fun cleanup() {
        holder.removeAllViews()
        scope.cancel()
        Log.d("PCP", "tickerJob canceled in: cleanup")
        tickerJob?.cancel()
        messageAnimationJob?.cancel()
        hideOverlays()
    }

    // Setup Screen

    protected fun showSetupScreen(): LayoutPodiumChallengeBoardSetupBinding {
        setupBinding.setTitle(challengeTitle)
        showSetupMessage(settingUpMessage)
        setUIState(setupBinding,UIState.SETUP)
        return setupBinding
    }

    protected fun LayoutPodiumChallengeBoardSetupBinding.setTitle(msg: String) {
        title.text = msg
    }
    protected fun LayoutPodiumChallengeBoardSetupBinding.setTitle(@StringRes res: Int) {
        title.setText(res)
    }

    private var messageAnimationJob: Job? = null

    protected fun showSetupEventMessage(event: PodiumChallengeSetupEvent, baseMessage: String, duration: Long = 5000) {
        messageAnimationJob?.cancel()
        if (uiState!=UIState.SETUP) {
            return
        }
        setupBinding.contributorLayout.isInvisible = true
        messageAnimationJob = scope.launch {
            val nicknames = challengeListener.getLiveViewModel().nickNames
            val text: String? = when(event) {
                is PodiumChallengeSetupEvent.FacilitatorSelected -> resources.getString(R.string.podium_challenge_setup_facilitator_request,nicknames.nickNameOrName(event.userId,event.name))
                is PodiumChallengeSetupEvent.FacilitatorConfirmed -> resources.getString(R.string.podium_challenge_setup_facilitator_accept,nicknames.nickNameOrName(event.userId,event.name))
                is PodiumChallengeSetupEvent.FacilitatorDeclined -> resources.getString(R.string.podium_challenge_setup_facilitator_decline,nicknames.nickNameOrName(event.userId,event.name))
                is PodiumChallengeSetupEvent.DurationSet -> resources.getString(R.string.podium_challenge_setup_duration_set, DateTimeUtils.formatDurationToHours(event.duration,getResourceProvider(resources) ))
                is PodiumChallengeSetupEvent.ContributorRequested -> {
                    when(event.contributorType) {
                        ChallengeContributionType.SELF -> null
                        ChallengeContributionType.SPEAKERS -> resources.getString(R.string.podium_challenge_setup_contributor_request_speaker)
                        ChallengeContributionType.CONTRIBUTOR -> resources.getString(R.string.podium_challenge_setup_contributor_request, nicknames.nickNameOrName(event.contributor))
                        ChallengeContributionType.FREE -> null
                        else -> null
                    }
                }
                is PodiumChallengeSetupEvent.ContributorConfirmed -> {
                    when(event.contributorType) {
                        ChallengeContributionType.SPEAKERS -> resources.getString(R.string.podium_challenge_setup_contributor_accept, nicknames.nickNameOrName(event.contributor))
                        ChallengeContributionType.FREE -> null
                        else -> {
                            setupBinding.contributor = event.contributor
                            setupBinding.content.fadeOutIn(setupBinding.contributorLayout)
                            delay(duration)
                            setupBinding.content.text = baseMessage
                            setupBinding.contributorLayout.fadeOutIn(setupBinding.content)
                            null
                        }
                    }
                }
                is PodiumChallengeSetupEvent.ContributorDeclined -> {
                    if (event.contributorType==ChallengeContributionType.CONTRIBUTOR) {
                        resources.getString(R.string.podium_challenge_setup_single_contributor_decline,nicknames.nickNameOrName(event.contributor))
                    } else resources.getString(R.string.podium_challenge_setup_contributor_decline,nicknames.nickNameOrName(event.contributor))
                }
                is PodiumChallengeSetupEvent.ContributorTimedOut -> {
                    event.contributorIds.forEach { id ->
                        challenge.contributors.find { it.id == id }?.let { contributor ->
                            val msg = if (challenge.contributorType == ChallengeContributionType.CONTRIBUTOR) {
                                resources.getString(R.string.podium_challenge_setup_single_contributor_decline, nicknames.nickNameOrName(contributor))
                            } else resources.getString(R.string.podium_challenge_setup_contributor_decline, nicknames.nickNameOrName(contributor))
                            setupBinding.content.transitionToText(msg)
                            delay(duration)
                        }
                    }
                    setupBinding.content.transitionToText(baseMessage)
                    null
                }
                is PodiumChallengeSetupEvent.ContributorLowBalance -> resources.getString(R.string.podium_challenge_setup_contributor_low_balance,nicknames.nickNameOrName(event.userId,event.name))
            }
            text?.let {
                Log.w("PCP", "showSetupEventMessage: [$text]")
                setupBinding.content.transitionToText(text)
                delay(duration)
                Log.w("PCP", "showSetupEventMessage: [$text] reverted")
                setupBinding.content.transitionToText(baseMessage)
            }
        }
    }

    private val settingUpMessage: String
        get() = resources.getString(R.string.podium_challenge_setup, resources.getString(challengeTitle))

    protected fun showSetupEventMessage(event: PodiumChallengeSetupEvent) {
        showSetupEventMessage(event,settingUpMessage)
    }

    protected fun showSetupMessage(msg: String) {
        messageAnimationJob?.cancel()
        messageAnimationJob = scope.launch {
            Log.w("PCP", "showSetupMessage: [$msg]")
            setupBinding.content.transitionToText(msg)
        }
    }

    protected fun showSetupMessage(@StringRes msg: Int) = showSetupMessage(resources.getString(msg))

    @get:StringRes
    protected abstract val challengeTitle: Int

    protected suspend fun AppCompatTextView.showStartCountdown(onTimerVisible: () -> Unit, onComplete: () -> Unit): Job {
        tickerJob?.cancelAndJoin()
        Log.d("PCP", "tickerJob canceled in: showStartCountdown")
        scope.launch {
            val cd = challenge.countDownRemaining?.seconds ?: 0
            if (cd >= 15) {
                // we have time to show ready test
                val interval = min(cd / 3, 5) * 1000
                Log.w("PCP", "showStartCountdown: interval $interval, actual s: $cd")
                Log.w("PCP", "showStartCountdown: ${resources.getString(challengeTitle)}")
                transitionToText(resources.getString(challengeTitle))
                delay(interval - 400)
                Log.w("PCP", "showStartCountdown: Get Ready")
                transitionToText(resources.getString(R.string.podium_challenge_get_ready))
                delay(interval - 400)
            }
            onTimerVisible.invoke()
            Log.w("PCP", "showStartCountdown: start timer")
            transitionToText(resources.getString(R.string.podium_challenge_countdown, DateTimeUtils.formatDurationToHours(cd, getResourceProvider(resources))))

            DateTimeUtils.countDownTimerFlow(challenge.countDownRemaining?.toMillis() ?: 0).onEach {
                Log.w("PCP", "showStartCountdown: remaining: $it")
                <EMAIL> = resources.getString(R.string.podium_challenge_countdown, DateTimeUtils.formatDurationToHours(it.toLong(), getResourceProvider(resources)))
            }.onCompletion {
                Log.w("PCP", "showStartCountdown: interval done")
                onComplete.invoke()
            }.launchIn(this)
        }.apply {
            tickerJob = this
            Log.d("PCP", "tickerJob set in showStartCountdown")
            return this
        }
    }

    protected suspend fun AppCompatTextView.showStartCountdown(onComplete: () -> Unit): Job {
        return showStartCountdown({ },onComplete)
    }

    protected suspend fun AppCompatTextView.showChallengeTimer(remainingMs: Long = challenge.liveTimeRemaining?.toMillis() ?: 0, redAfter: Int = 15, @ColorRes timerColor: Int = R.color.colorPodiumBoardTimer, onComplete:suspend () -> Unit): Job {
        Log.d("PCP", "tickerJob canceled in: showChallengeTimer")
        tickerJob?.cancel()
        Log.d("PCP", "showChallengeTimer: remainingMs: $remainingMs | endTimeUTC: ${challenge.endTimeUTC} | parsedEndTime: ${challenge.parsedEndTime} | now: ${ZonedDateTime.now()}")
        val remainingSeconds = remainingMs/1000
        val color = if(remainingSeconds<=redAfter) R.color.colorError else timerColor
        setTextColor(ContextCompat.getColor(context, color))
        DateTimeUtils.countDownTimerFlow(remainingMs).onEach {
            if (it==redAfter) {
                setTextColor(ContextCompat.getColor(context, R.color.colorError))
            }
            <EMAIL> = DateTimeUtils.formatSeconds(it)
        }.onCompletion { cause ->
            if (cause is CancellationException) {
                // Flow was canceled
                println("Flow was canceled")
            } else {
                // Flow completed normally
                // only call onComplete if the job was not cancelled
                onComplete.invoke()
            }
            tickerJob = null
            Log.d("PCP", "tickerJob set to null in showChallengeTimer")
        }.launchIn(scope).apply {
            tickerJob = this
            Log.d("PCP", "tickerJob set in showChallengeTimer")
            return this
        }
    }

    protected val challengeWinners
        get() = scores.filter { it.winner }

    protected open var preResultMessage: String? = null

    fun setParticipantExit(user: Int) {
        val profile = scores.find { it.id == user }?: return
        preResultMessage = resources.getString(R.string.podium_challenge_participant_exit_message,profile.name)
    }

    protected open suspend fun showResultUI() {
        tickerJob?.cancelAndJoin()
        Log.d("PCP", "tickerJob canceled in: showResultUI")
        setUIState(resultBinding,UIState.RESULT)
        resultBinding.apply {
            title.setText(challengeTitle)
            tickerJob = scope.launch {
                content.isInvisible = true
                scoreHolder.isInvisible = true
                winnerLayout.isInvisible = true
                preResultMessage?.let {
                    content.transitionToText(it)
                    preResultMessage = null
                    delay(5000)
                }
                content.transitionToText(resources.getString(R.string.podium_challenge_game_over))
                delay(5000)
                val winners = challengeWinners
                closeButton.isVisible = challengeListener.getLiveViewModel().iAmManager.value == true || winners.find { it.id == myUserId } != null
                closeButton.setOnClickListener {
                    challengeListener.onCloseChallenge()
                }

                val vm = challengeListener.getLiveViewModel()

                withContext(Dispatchers.Main) {
                    scores.find { it.id == myUserId }?.let {
                        challengeListener.showChallengeResultVideo(it.winner)
                    }
                }
                Log.w("PCP", "showResultUI: $scores | winners: ${winners.size} | $winners")
                val winnerText = when (winners.size) {
                    0 -> resources.getString(R.string.podium_challenge_winner_ticker_name_zero)
                    1 -> resources.getString(R.string.podium_challenge_winner_ticker_name_one, vm.nickNames.nickNameOrName(winners.getOrNull(0)))
                    else -> resources.getString(R.string.podium_challenge_winner_ticker_name_other, winners.size)
                }
                content.transitionToText(winnerText)

                delay(5000)
                if (winners.isNotEmpty()) {
                    getExtraTextForWinnerTicker(winners)?.let {
                        content.transitionToText(it)
                        delay(5000)
                    }
                    val coins = winners[0].coinsWon ?: 0.0
                    if (coins > 0) {
                        content.transitionToText(resources.getString(R.string.podium_challenge_winner_ticker_coins, "%.2f".format(Locale.US, coins)))
                        delay(5000)
                    }
                }

                content.fadeOutIn(scoreHolder) { outV, inV ->
                    outV.isVisible = false
                    inV.isVisible = true
                    refreshResultScores()
                }
                delay(5000)

                if (winners.isEmpty()) {
                    return@launch
                }
                cycleWinners()

                while (showFinalScores()) {
                    resultUiCarousel()
                }
            }
            Log.d("PCP", "tickerJob set in showResultUI")
        }
    }

    protected open fun showFinalScores(): Boolean = true

    protected open fun convertScore(sc: PodiumChallengeScore): String {
        return sc.score.numberToKWithFractions()
    }

    protected fun refreshResultScores() {
        resultBinding.scoreHolder.removeAllViews()
        scores.forEach { sc ->
            val b = DataBindingUtil.inflate<LayoutPodiumChallengeBoardLikesResultScoreBinding>(layoutInflater, R.layout.layout_podium_challenge_board_likes_result_score, resultBinding.scoreHolder, false)
            b.user = sc
            b.score.text = convertScore(sc)
            Log.d("PCP", "refreshResultScores: score for ${b.user?.name} is $sc: ${convertScore(sc)}")
            resultBinding.scoreHolder.addView(b.root)
        }
    }

    protected open fun getExtraTextForWinnerCard(winner: PodiumChallengeScore): String? = null

    protected open fun getExtraTextForWinnerTicker(winners: List<PodiumChallengeScore>): String? = null

    private suspend fun cycleWinners() {
        val winners = challengeWinners

        val interval = if (winners.size>2) 3000L else 5000L

        winners.forEachIndexed { i, winner ->
            resultBinding.apply {
                val prevView = if (i == 0) scoreHolder else winnerLayout
                prevView.fadeOutIn(winnerLayout) { _, _ ->
                    scoreHolder.isInvisible = true
                    winnerLayout.isVisible = true
                    resultBinding.winner = winner.copy(
                        name = challengeListener.getLiveViewModel().nickNames.nickNameOrName(winner)
                    )
                    getExtraTextForWinnerCard(winner).let {
                        winnerLikes.text = it
                        winnerLikes.isVisible = !it.isNullOrBlank()
                    }
                    winnerCoins.isVisible = (winner.coinsWon?:0.0)>0.0
                    winnerCoins.text = resources.getString(R.string.common_coins, winner.coinsWon?.numberToKWithFractions())
                    try {
                        val color = winner.color.toColorInt()
                        winnerStats.setCardBackgroundColor(color)
                    } catch (_: Exception) {
                    }
                }
                delay(interval)
            }
        }
    }

    protected suspend fun resultUiCarousel() {
        resultBinding.apply {
            winnerLayout.fadeOutIn(scoreHolder) { _, _ ->
                winnerLayout.isInvisible = true
                scoreHolder.isVisible = true
            }
            delay(5000)
            cycleWinners()
        }
    }

    private var pulseAnim: Job? = null

    protected fun pulseAnimation(image: AppCompatImageView) {
        fun reset() {
            image.apply {
                alpha = 1f
                scaleX = 1f
                scaleY = 1f
            }
            pulseAnim?.cancel()
            pulseAnim = null
        }

        reset()

        pulseAnim = scope.launch {
            image.animate().apply {
                interpolator = AccelerateDecelerateInterpolator()
                alpha(0f)
                scaleX(1.8f)
                scaleY(1.8f)
                setDuration(250)
                start()
                awaitEnd()
                reset()
            }
        }
    }

    // For Board Challenges (Box, Con4)
    protected fun showTimer(tv: AppCompatTextView, progress: CircularProgressIndicator, remaining: Duration,onComplete: suspend (cancelled: Boolean) -> Unit) {
        progress.setProgress((remaining.seconds.toFloat()/TURN_DURATION_SECONDS*100).roundToInt(),false)
        scope.launch {
            val timerStart = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"))
            try {
                Log.d("PCPC4", "showTimer : starting timer: $timerStart")
                DateTimeUtils.countDownTimerFlow(remaining.toMillis()).onEach {
                    tv.text = it.toString()
                    val progressRemaining = (it.toFloat() / TURN_DURATION_SECONDS * 100).roundToInt()
                    progress.setProgress(progressRemaining, true)
                }.onCompletion { cause ->
                    if (cause is CancellationException) {
                        Log.d("PCPC4", "showTimer : timer cancelled")
                        onComplete.invoke(true) // Mark as cancelled
                    } else {
                        Log.d("PCPC4", "showTimer : timer completed normally")
                        onComplete.invoke(false) // Normal finish
                    }
                }.launchIn(this)
            }
            catch (e: Exception) {
                Log.w("PCPC4", "showPlayerTurn: timer cancelled: $timerStart")
                onComplete.invoke(true)
            }
        }.apply {
            tickerJob = this
            Log.d("PCP", "tickerJob set in showTimer")
        }
    }

    @ColorRes
    protected fun ChallengePlayer.colorRes(): Int {
        return when(this) {
            ChallengePlayer.PLAYER1 -> R.color.colorPrimary
            ChallengePlayer.PLAYER2 -> R.color.colorSecondary
        }
    }

    @ColorRes
    protected fun ChallengePlayer.colorOnColorRes(): Int {
        return when(this) {
            ChallengePlayer.PLAYER1 -> R.color.textColorOnPrimary
            ChallengePlayer.PLAYER2 -> R.color.textColorOnSecondary
        }
    }

    @DrawableRes
    protected fun ChallengePlayer.trophyRes(): Int {
        return when(this) {
            ChallengePlayer.PLAYER1 -> R.drawable.ic_challenge_winner_primary
            ChallengePlayer.PLAYER2 -> R.drawable.ic_challenge_winner_secondary
        }
    }

    // NOTE: Below functions to be used only for Boxes and ConFour

    val meAsPlayer: ChallengePlayer?
        get() {
            return myUserId.toPlayer()
        }

    protected fun Int.toPlayer(): ChallengePlayer? {
        val id = this
        return scores.find { it.userId == id }?.player
    }

    protected fun ChallengePlayer.getScore(): PodiumChallengeScore? {
        return scores.find { it.participantTokenNumber == this.tokenNumber }
    }

    open val boardData: BoardData? = null

    val currentPlayerId: Int?
        get() = boardData?.currentPlayerUserId

    val currentPlayer: ChallengePlayer?
        get() = currentPlayerId?.toPlayer()

    protected val iAmCurrentPlayer: Boolean
        get() = currentPlayer == meAsPlayer

    open fun onUserJoined(user: PodiumParticipant) {
        // override to show this in overlays
    }

    protected fun hideOverlays() {
        challengeListener.showSpeakerOverlay(false)
        challengeListener.showChatOverlay(false)
        challengeListener.showChatInputOverlay(false)
    }

    protected fun LayoutPodiumChatOverlayUserJoinedBinding.addToFlow(user: PodiumParticipant) {
        val layoutBinding: LayoutPodiumUserJoinedBinding = LayoutPodiumUserJoinedBinding.inflate(layoutInflater, userJoinedHolder, false)
        layoutBinding.user = user
        layoutBinding.root.let { root ->
            root.id = View.generateViewId()
            userJoinedHolder.addView(root)
            userJoinedFlow.addView(root)
            root.alpha = 0f
            scope.launch {
                try {
                    root.fadeInOut {
                        delay(5000)
                    }
                    userJoinedFlow.removeView(root)
                    userJoinedHolder.removeView(root)
                } finally {
                }
            }
        }
    }

}