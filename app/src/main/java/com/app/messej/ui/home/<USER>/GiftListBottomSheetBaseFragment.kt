package com.app.messej.ui.home.gift

import GiftListAdapter
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.CurrencyType
import com.app.messej.data.model.enums.GiftType
import com.app.messej.databinding.FragmentGiftBottomSheetBinding
import com.app.messej.databinding.LayoutEmojiSendConfirmationBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.showAlertWithSingleButton
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.tabs.TabLayout
import com.kennyc.view.MultiStateView

abstract class GiftListBottomSheetBaseFragment : BottomSheetDialogFragment() {

    lateinit var binding: FragmentGiftBottomSheetBinding
    val viewModel: GiftListingViewModel by viewModels()
    var mAdapter: GiftListAdapter? = null
    val giftCommonViewModel: GiftCommonViewModel by activityViewModels()
    val podiumLiveViewModel: PodiumLiveViewModel by activityViewModels()
    private var isDialogVisible = false
    private var giftClickEnabled = true

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.giftItems.observe(viewLifecycleOwner) {
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.accountDetails.observe(viewLifecycleOwner){
        }

        viewModel.nameOrNickname.observe(viewLifecycleOwner) {}

        viewModel.errorMessage.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()

        }

        viewModel.isGiftReceiverBanned.observe(viewLifecycleOwner) {
            showAlertWithSingleButton(
                message = R.string.banned_user_gift_receive_alert_message,
                onClick = { }
            )
        }

        viewModel.insufficientBalance.observe(viewLifecycleOwner) {
            if (it == CurrencyType.COINS) {
                findNavController().navigateSafe(GiftListBottomSheetFragmentDirections.actionGlobalGiftLowBalance(it))
            }
            else if (it == CurrencyType.FLIX) {
//                showToast(getString(R.string.insufficient_flix_balance))
                findNavController().navigateSafe(GiftListBottomSheetFragmentDirections.actionGlobalGiftLowBalance(it))
            }

        }

        viewModel.giftItemDetails.observe(viewLifecycleOwner) {
            it.let {
                val item=it.second
                val itemDetails=it.first

                    MaterialDialog(requireContext()).show {
                        val view = DataBindingUtil.inflate<LayoutEmojiSendConfirmationBinding>(layoutInflater, R.layout.layout_emoji_send_confirmation, null, false)
                        view.viewModel = viewModel
                        customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                        cancelable(false)
                        view.imageUrl = item.thumbnail
                        view.giftType = item.giftType
                        view.giftName = item.giftName

                        view.confirmationNote.text = if (!viewModel.challenge_id.value.isNullOrBlank())
                            getString(R.string.gift_confirmation_note_challenge,
                             itemDetails.sentCoins?.toInt().toString(),
                            viewModel.nameOrNickname.value.toString(),
                             itemDetails.userRating?.toInt().toString(),
                             itemDetails.receiverRating?.toInt().toString(),
                             itemDetails.receivedCoins.toString()
                        )
                        else if(item.isFlix){
                            getString(R.string.gift_message_flix, item.flix.toString(),
                                      viewModel.nameOrNickname.value.toString(),
                                      itemDetails.userRating?.toInt().toString(),
                                      itemDetails.sendingFee?.toInt().toString(),
                                      itemDetails.receivedFlix?.toString())
                        }
                        else if(item.isCoin){
                            getString(R.string.gift_message_coin,item.coins.toString(), viewModel.nameOrNickname.value.toString(),itemDetails.userRating?.toInt().toString(),
                                      itemDetails.receiverRating?.toInt().toString(), itemDetails.receivedCoins.toString())
                        }
                        else getString(R.string.gift_confirmation_note,
                                         itemDetails.sentCoins?.toInt().toString(),
                                         viewModel.nameOrNickname.value.toString(),
                                         itemDetails.userRating?.toInt().toString()+"%",
                                         itemDetails.receiverRating?.toInt().toString(),
                                         itemDetails.receivedCoins.toString())

                    view.actionConfirm.setOnClickListener {
                        onGiftItemClick(item,false)
                        giftClickEnabled = true
                        dismiss()
                    }
                    view.actionCancel.setOnClickListener {
                        viewModel.setGiftLoading(false)
                        giftClickEnabled = true
                        dismiss()
                    }
                }
            }
        }
        viewModel.giftType.observe(viewLifecycleOwner){
            Log.d("GiftType", it.name)
//            val layoutManager = LinearLayoutManager(requireContext())
            val layoutManager =  GridLayoutManager(binding.root.context,3)
            mAdapter = GiftListAdapter(it,object : GiftListAdapter.ActionListener {
                override fun onItemClick(item: GiftItem) {
                    if (giftClickEnabled) {
                        giftClickEnabled=false
                        ensureInteractionAllowed {
                            onGiftItemClick(item, true)
                        }
                    }
                    giftClickEnabled = true
                }

                override fun isChallenge(): Boolean {
                    return !viewModel.challenge_id.value.isNullOrBlank()
                }
                override fun isVisitor(): Boolean {
                    return viewModel.isVisitor
                }

                override fun availableFLixBalance(): Double {
                    return viewModel.accountDetails.value?.currentFlix?:0.0
                }

                override fun availableCoinBalance(): Double {
                    return viewModel.accountDetails.value?.currentCoin?:0.0
                }


            })

            mAdapter?.apply {
                addLoadStateListener { loadState ->
                    binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                    else if (loadState.append.endOfPaginationReached) {
                        if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                    } else MultiStateView.ViewState.CONTENT

                }
                registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                    override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                        super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                        if ((fromPosition == 0 || toPosition == 0) && layoutManager.findFirstCompletelyVisibleItemPosition() == 0) {
                            layoutManager.scrollToPosition(0)
                        }
                    }
                })
            }

            binding.list.layoutManager = layoutManager
            binding.list.adapter = mAdapter
        }
    }

    private fun setup() {
    }

    fun setUpTabData() {
        fun getTab(tab: GiftType): TabLayout.Tab {
            return binding.giftTab.newTab().apply {
                text = getString(
                    when (tab) {
                        GiftType.PERSONAL -> R.string.gift_tab_title_personal
                        GiftType.VIP -> R.string.gift_tab_title_vip
                        GiftType.BANK -> R.string.gift_tab_title_bank
                    }
                )
                tag = tab
            }
        }
        binding.giftTab.addTab(getTab(GiftType.PERSONAL))
        binding.giftTab.addTab(getTab(GiftType.VIP))
        binding.giftTab.addTab(getTab(GiftType.BANK))

        //By default Personal gift
        binding.giftTab.getTabAt(0)?.select()
        viewModel.giftType.postValue(GiftType.PERSONAL)


        binding.giftTab.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                // Perform action when tab is selected
                tab?.let {
                    viewModel.giftType.postValue(tab.tag as GiftType)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // Perform action when tab is unselected
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // Perform action when tab is reselected
            }
        })

    }


    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }


    abstract fun onGiftItemClick(item: GiftItem, preview: Boolean)


}