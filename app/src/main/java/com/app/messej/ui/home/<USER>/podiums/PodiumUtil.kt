package com.app.messej.ui.home.publictab.podiums

import android.content.Context
import android.content.res.ColorStateList
import android.util.Log
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.paging.PagingData
import androidx.paging.PagingDataEvent
import androidx.paging.PagingDataPresenter
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.databinding.DialogGenderPodiumBinding
import com.app.messej.ui.utils.EnumUtils.displayText
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.roundToInt

object PodiumUtil {

    fun PodiumKind.displayText(c: Context): String {
        return c.resources.getString(displayText())
    }

    @StringRes
    fun PodiumKind.displayText(): Int {
        return when (this) {
            PodiumKind.ASSEMBLY -> R.string.podium_kind_assembly
            PodiumKind.LECTURE -> R.string.podium_kind_lecture
            PodiumKind.ALONE -> R.string.podium_kind_alone
            PodiumKind.INTERVIEW -> R.string.podium_kind_interview
            PodiumKind.MAIDAN -> R.string.podium_kind_maidan
            PodiumKind.THEATER -> R.string.podium_kind_theater
        }
    }

    @JvmStatic
    @BindingAdapter("kindChipColor")
    fun setKindChipColors(view: AppCompatTextView, kind: PodiumKind?) {
        val colors = kind?.chipColors(view.context)?: return
        colors.first?.let { view.setTextColor(it) }
        view.backgroundTintList = colors.second?.let { ColorStateList.valueOf(it) }
    }

    fun PodiumKind.chipColors(c: Context): Pair<Int?, Int?> {
        @ColorRes var kindTextColor: Int = R.color.textColorSecondary
        @ColorRes var kindBackgroundColor: Int = R.color.colorSurfaceSecondaryDark
        when(this){
            PodiumKind.ASSEMBLY -> {
                kindTextColor = R.color.colorSecondary
                kindBackgroundColor = R.color.colorPrimary
            }
            PodiumKind.ALONE ->{
                kindTextColor = R.color.colorPrimary
                kindBackgroundColor = R.color.colorKindAloneBackground
            }
            PodiumKind.LECTURE ->{
                kindTextColor = R.color.colorPrimary
                kindBackgroundColor = R.color.colorSecondary
            }
            PodiumKind.INTERVIEW -> {
                kindTextColor = R.color.colorPrimary
                kindBackgroundColor = R.color.colorPass
            }
            PodiumKind.MAIDAN -> {
                kindTextColor = R.color.colorSecondary
                kindBackgroundColor = R.color.colorPass
            }

            PodiumKind.THEATER -> {
                kindTextColor = R.color.colorPrimaryDarkest
                kindBackgroundColor = R.color.colorPrimaryLighter
            }
        }
        return Pair(ContextCompat.getColor(c,kindTextColor),ContextCompat.getColor(c,kindBackgroundColor))
    }

    fun Fragment.validateAndConfirmJoin(pod: Podium, user: CurrentUser, valid: () -> Unit){
        validateJoin(pod,user) {
            if ((pod.managerId == user.id)|| pod.isAdmin) {
                valid.invoke()
            }
            else if ((pod.joiningFee ?: 0) > 0) {
                showJoinPodiumWithCoinsAlert(joiningFee = pod.joiningFee, onProceed = valid)
            }
            else {
                valid.invoke()
            }
        }
    }

    suspend fun Fragment.validateAndConfirmJoinFromGreenDot(podiumId: String,postOwner: String, user: CurrentUser, valid: () -> Unit){
            val loader = showLoader()

            // Create repository instance to fetch podium details
            val podiumRepository = PodiumRepository(requireActivity().application)
            withContext(Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.getPodiumDetails(podiumId)) {
                        is ResultOf.Success -> {
                            val podium = result.value
                            withContext(Dispatchers.Main) {
                                loader.dismiss()
                                // Validate join with the fetched podium details
                                validateJoin(podium, user) {

                                    // Check if user is manager or admin - they should bypass fee validation
                                    if (podium.managerId == user.id || podium.isAdmin) {
                                        // Show confirmation dialog for managers/admins without fee prompt
                                        showPodiumJoinConfirmationDialog(
                                            hostName = postOwner, podiumTitle = podium.name, onJoinConfirmed = valid
                                        )
                                    } else if ((podium.joiningFee ?: 0) > 0) {
                                        // Show fee dialog only for regular users
                                        showJoinPodiumWithCoinsAlert(joiningFee = podium.joiningFee, onProceed = valid)
                                    } else {
                                        // Show confirmation dialog for regular users with no fee
                                        showPodiumJoinConfirmationDialog(
                                            hostName = postOwner, podiumTitle = podium.name, onJoinConfirmed = valid
                                        )
                                    }
                                }
                            }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }
                } catch (e: Exception) {
                    Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
                    ResultOf.Error(Exception(e))
                }
            }
        }

    private fun Fragment.validateJoin(pod: Podium, user: CurrentUser, valid: () -> Unit) {
        val gender = user.profile.gender
//check podiumis live or not
        if (!pod.isLive) {
            showPodiumAlreadyEndedDialog()
        }
        // Check if user is blocked from this podium
        else if (pod.isUserBlocked) {
            showToast(R.string.podium_error_blocked)
        }
        // Check private podium access first
        else if (pod.isPrivate && !pod.isInvited && !pod.isInvitee && pod.managerId != user.id && !pod.isAdmin) {
            showPodiumPrivateAccessDenied()
        }
        // User is Male and trying to join a Women-only podium
        else if (pod.entry == PodiumEntry.WOMEN_ONLY && gender != Gender.FEMALE) {
            showPodiumGenderAccessDenied(isForMenOnly = false)
        }
        // User is Female and trying to join a Men-only podium
        else if (pod.entry == PodiumEntry.MEN_ONLY && gender != Gender.MALE) {
            showPodiumGenderAccessDenied(isForMenOnly = true)
        }
        else if ((pod.managerId == user.id)|| pod.isAdmin) {
            valid.invoke()
        }
        else if (pod.requiredUserRating == 100 && user.userRatingPercent.toInt() != 100) {
            showUserJoinByRatingError(
                message = getString(R.string.podium_join_with_rating_with_hundred),
                userRating = user.userRatingPercent,
                isPremium = user.premium
            )
        } else if ((pod.requiredUserRating ?: 0) > user.userRatingPercent.toInt()) {
            showUserJoinByRatingError(
                message = getString(R.string.podium_join_with_rating_ninety_or_above),
                userRating = user.userRatingPercent,
                isPremium = user.premium
            )
        } else if ((pod.joiningFee ?: 0) > 0 && (pod.joiningFee ?: 0) > user.coinBalance) {
            showJoinPodiumWithInsufficientCoinsAlert(coinBalance = user.coinBalance, joiningFee = pod.joiningFee)
        }
        else {
            valid.invoke()
        }
    }

    fun Fragment.showUserJoinByRatingError(message: String, userRating: String?, isPremium: Boolean?) {
        showFlashatDialog {
            setTitle(title = getString(R.string.title_podium_alert_rating, userRating), textColor = R.color.colorError)
            setMessage(message)
            setIcon(R.drawable.ic_rating_less)
            setConfirmButtonVisible(isPremium == true)
            setConfirmButton(R.string.restore_rating_header, R.drawable.ic_restore_rating, false) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionHomeBusinessFragmentToRestoreRatingFragment(false))
                true
            }
        }
    }

    fun Fragment.showJoinPodiumWithInsufficientCoinsAlert(coinBalance: Double?, joiningFee: Int?) {
        Log.d("PodiumUtil", "Coin Balance ->$coinBalance, Joining Fee -> $joiningFee")
        showFlashatDialog {
            setTitle(title = getString(R.string.podium_join_coin_balance_title, "${coinBalance ?: 0.0}"), textColor = R.color.colorError)
            setMessage(getString(R.string.podium_join_with_coins_insufficient_message, "${joiningFee ?: 0}"))
            setIcon(R.drawable.ic_rating_less)
            setConfirmButtonVisible(true)
            setConfirmButton(R.string.title_buy_coins, R.drawable.ic_coin, false) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = true))
                true
            }
        }
    }

    fun Fragment.showJoinPodiumWithCoinsAlert(joiningFee: Int?, onProceed: () -> Unit, onCancel: (() -> Unit)? = null) {
        showFlashatDialog {
            setMessage(getString(R.string.podium_join_with_coins_message, "${joiningFee ?: 0}", "${joiningFee ?: 0}"))
            setConfirmButtonVisible(true)
            setConfirmButton(R.string.common_proceed) {
                onProceed()
                true
            }
            setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) {
                onCancel?.invoke()
                true
            }
        }
    }

    fun Fragment.showPodiumGenderAccessDenied(isForMenOnly: Boolean) {
        showFlashatDialog {
            setTitle(getString(R.string.title_access_denied),if (isForMenOnly)R.color.colorLegalAppeal else R.color.colorPodiumLiveFriends)
            setMessage(
                if (isForMenOnly) getString(R.string.podium_men_only_access_message)
                else getString(R.string.podium_women_only_access_message)
            )
            if (isForMenOnly)setIcon(R.drawable.ic_men_only_acess) else setIcon(R.drawable.ic_women_only_access)
            setConfirmButtonVisible(false) // No confirm button needed for this error
            setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) {
                // Just dismiss the dialog, no navigation needed in utility method
                true
            }
        }
    }

    fun Fragment.showPodiumPrivateAccessDenied() {
        showFlashatDialog {
            setTitle(getString(R.string.title_access_denied),R.color.colorPromoBoardRed)
            setMessage(getString(R.string.podium_private_access_message))
            setIcon(R.drawable.ic_private_acess)
            setConfirmButtonVisible(false)
            setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) {
                true
            }
        }
    }


    fun Fragment.showPodiumJoinConfirmationDialog(
        hostName: String,
        podiumTitle: String,
        onJoinConfirmed: () -> Unit
    ) {
        showFlashatDialog {
            setMessage(getString(R.string.podium_join_confirmation, hostName, podiumTitle))
            setCloseButton(title = R.string.common_cancel, icon = R.drawable.ic_close) {
                // Handle cancel action - user chose not to join
                true
            }
            setConfirmButton(R.string.common_confirm, R.drawable.ic_chat_liked, true) {
                // Handle join action - user confirmed to join the podium
                onJoinConfirmed.invoke()
                true
            }
        }
    }

    fun Fragment.showPodiumAlreadyEndedDialog() {
        showFlashatDialog {
            setMessage(getString(R.string.podium_already_ended_message))
            setConfirmButtonVisible(false)
            setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) {
                true
            }
        }
    }



    private fun Fragment.showCustomisedDialog(content: Int,podiumGender: Gender) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<DialogGenderPodiumBinding>(layoutInflater, R.layout.dialog_gender_podium, null, false)
            view.content = getString(content)
            view.gender = podiumGender
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }
    }

    fun<T : Any> Fragment.getPagingDataPresenter(data: LiveData<PagingData<T>>, onLoad: () -> Unit = {}): PagingDataPresenter<T> {

        val pdp = object : PagingDataPresenter<T>() {
            override suspend fun presentPagingDataEvent(event: PagingDataEvent<T>) {
//                Log.w("PDPresenter", "presentPagingDataEvent: ${event.toString()}")
                onLoad.invoke()
            }
        }

        data.observe(viewLifecycleOwner) { pagingData ->
            lifecycleScope.launch {
//                Log.w("PDPresenter", "addObservers: livePodiumsList")
                pagingData?.let { pdp.collectFrom(it) }
            }
        }

        return pdp
    }

    fun PagingDataPresenter<Podium>.findIndexOf(id: String): Int {
        return snapshot().indexOfFirst {
            it?.id == id
        }
    }

    fun PagingDataPresenter<PodiumFriend>.findIndexOfFriend(id: String): Int {
        return snapshot().indexOfLast {
            it?.podiumId == id
        }
    }

    fun Fragment.setCitizenshipWithFlixRate(textView: AppCompatTextView, data: PodiumUserLikesCoinsResponse, isHost: Boolean) {
        val flixRate = data.flixRate
        val flixRatePercentage = (flixRate * 100).roundToInt().toString()
        var textColor = R.color.textColorOnPrimary
        var backgroundColor = R.color.colorPrimary


        var notchText = data.userCitizenship?.displayText()?.let { resources.getString(it) }.orEmpty()

        when (data.userCitizenship) {
            UserCitizenship.VISITOR -> {
                textColor = R.color.textColorOnSecondary
                backgroundColor = R.color.colorAlwaysLightSurfaceSecondaryDarker
            }
            UserCitizenship.RESIDENT -> {
                textColor = R.color.textColorOnPrimary
                backgroundColor = R.color.colorPodiumSpeakerResident
            }
            UserCitizenship.CITIZEN -> {
                textColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorPodiumSpeakerCitizen
            }
            UserCitizenship.OFFICER -> {
                textColor = R.color.textColorAlwaysLightPrimary
                backgroundColor = R.color.colorPodiumSpeakerOfficer
            }
            UserCitizenship.AMBASSADOR -> {
                textColor = R.color.colorPrimaryColorDarkest1
                backgroundColor = R.color.colorPodiumSpeakerAmbassador
            }
            UserCitizenship.MINISTER->{
                textColor = R.color.textColorOnPrimary
                backgroundColor = R.color.colorPrimary
            }
            UserCitizenship.PRESIDENT->{
                textColor = R.color.colorPrimary
                backgroundColor = R.color.colorSecondaryDark
            }
            UserCitizenship.GOLDEN->{
                textColor = R.color.white
                backgroundColor = R.color.colorSecondaryDark
            }
            null -> {}
        }
        if (isHost) {
            notchText = resources.getString(R.string.podium_speaker_prefix_manager, notchText)
        }
        textView.text = buildString {
            append(notchText)
            append(" ")
            append(resources.getString(R.string.common_percentage_value,flixRatePercentage))
        }
        textView.setTextColor(ContextCompat.getColor(requireContext(), textColor))
        Log.d("PodiumUtil", "setCitizenshipWithFlixRate: ${data.userCitizenship}")
        when (data.userCitizenship) {
            UserCitizenship.PRESIDENT -> {
                textView.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_president_flix_rate)
            }
            UserCitizenship.GOLDEN -> {
                textView.background = ContextCompat.getDrawable(requireContext(), R.drawable.bg_golden_podium_speaker_title)
            }
            else -> {
                textView.background.setTint(ContextCompat.getColor(requireContext(), backgroundColor))
            }
        }
    }
}