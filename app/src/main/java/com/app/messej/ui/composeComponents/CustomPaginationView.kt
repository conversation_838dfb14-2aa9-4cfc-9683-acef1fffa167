package com.app.messej.ui.composeComponents

import androidx.annotation.DrawableRes
import androidx.annotation.FloatRange
import androidx.annotation.StringRes
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import com.app.messej.R
import kotlinx.coroutines.delay

@Composable
fun <T : Any> CustomPaginationView(
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    lazyPagingItem: LazyPagingItems<T>,
    contentPadding: PaddingValues = PaddingValues(vertical = dimensionResource(id = R.dimen.activity_margin)),
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.activity_margin)),
    lazyColumnContent: LazyListScope.() -> Unit,
    loadingAnimation: @Composable () -> Unit = { CustomLinearProgressIndicator() },
    @DrawableRes emptyItemIcon: Int,
    @StringRes emptyViewTitle: Int
) {
    when (lazyPagingItem.loadState.refresh) {
        is LoadState.NotLoading -> {
            if (lazyPagingItem.itemCount == 0) {
                ListEmptyItemView(
                    textId = emptyViewTitle,
                    icon = emptyItemIcon,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                LazyColumn(
                    modifier = modifier,
                    state = lazyListState,
                    contentPadding = contentPadding,
                    verticalArrangement = verticalArrangement
                ) {
                    lazyColumnContent(this)
                    customPaginationLazyListAppendView(
                        lazyPagingItem = lazyPagingItem,
                        listScope = this
                    )
                }
            }
        }

        is LoadState.Loading -> {
            Box(modifier = Modifier.fillMaxSize()) {
                loadingAnimation()
            }
        }

        is LoadState.Error -> {
            ListErrorItemView(
                icon = emptyItemIcon,
                onRetry = { lazyPagingItem.retry() }
            )
        }
    }
}

private fun <T : Any> customPaginationLazyListAppendView(
    lazyPagingItem: LazyPagingItems<T>,
    listScope: LazyListScope
) {
    listScope.apply {
        when (lazyPagingItem.loadState.append) {
            is LoadState.NotLoading -> {}

            is LoadState.Loading -> {
                item {
                    //If need to show loading at the bottom of pagination when requesting new page
                    Box(modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CustomLinearProgressIndicator(
                            modifier = Modifier.fillMaxWidth(0.5F)
                        )
                    }
                }
            }

            is LoadState.Error -> {
                item {
                    // Can use this if the next page is in error state.
                    // Visible at the bottom of the list.
                }
            }
        }
    }
}

@Composable
fun CustomLinearProgressIndicator(
    modifier: Modifier = Modifier,
    color : Color = colorResource(id = R.color.colorPrimary),
    strokeCap: StrokeCap = StrokeCap.Round
) {
    LinearProgressIndicator(
        modifier = modifier.fillMaxWidth(),
        color = color,
        strokeCap = strokeCap
    )
}

@Composable
fun CustomLinearProgressIndicatorWithProgress(
    modifier: Modifier = Modifier,
    color : Color = colorResource(id = R.color.colorPrimary),
    strokeCap: StrokeCap = StrokeCap.Round,
    @FloatRange(from = 0.0, to = 1.0) progress: Float
) {
    var initialProgress by rememberSaveable { mutableFloatStateOf(0F) }

    LaunchedEffect(Unit) {
        delay(timeMillis = 300)
        initialProgress = progress
    }

    val progressAnimated by animateFloatAsState(
        targetValue = initialProgress,
        animationSpec = tween(easing = LinearEasing)
    )

    LinearProgressIndicator(
        progress = progressAnimated,
        modifier = modifier.fillMaxWidth(),
        color = color,
        strokeCap = strokeCap
    )
}