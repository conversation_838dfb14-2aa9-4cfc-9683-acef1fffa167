package com.app.messej.ui.legal.report

import android.util.Log
import android.view.View
import androidx.core.view.isVisible
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.ReportPreviewProvider
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserCitizenship.MINISTER
import com.app.messej.data.model.enums.UserCitizenship.PRESIDENT
import com.app.messej.databinding.LayoutReportContentPreviewBinding
import com.app.messej.ui.legal.report.ReportUtils.canBan
import com.google.gson.Gson
import kotlin.math.log

object ReportUtils {

    fun CurrentUser.canReport(user: AbstractUser?): Boolean {
        user?: return false
        if (id == user.id) return false
        return this.citizenship.canReport(user.citizenship?: return false)
    }

    fun CurrentUser.canReport(user: UserCitizenship?): <PERSON><PERSON><PERSON> {
        return this.citizenship.canReport(user?: return false)
    }

    fun UserCitizenship.canReport(user: UserCitizenship): Boolean {
        Log.d("REPF", "canReport: $this -> $user")
        val list: List<UserCitizenship> = when(this) {
            UserCitizenship.GOLDEN -> UserCitizenship.entries.filter { it<this }
            else -> UserCitizenship.entries.filter { it.ordinal<this.ordinal-2 }
        }
        return user in list
    }

    fun CurrentUser.canReportAndHide(user: AbstractUser?): Boolean {
        user?: return false
        if (id==user.id) return false
        return this.citizenship.canReportAndHide(user.citizenship?: return false)
    }

    fun CurrentUser.canReportAndHide(user: UserCitizenship?): Boolean {
        return this.citizenship.canReportAndHide(user?: return false)
    }

    fun UserCitizenship.canReportAndHide(user: UserCitizenship): Boolean {
        val list: List<UserCitizenship> = when(this) {
            UserCitizenship.GOLDEN,MINISTER,PRESIDENT -> UserCitizenship.entries.filter { it<this }
            else -> UserCitizenship.entries.filter { it.ordinal<this.ordinal-2 }
        }
        return user in list
    }

    fun CurrentUser.canBan(user: AbstractUser?): Boolean {
        user?: return false
        if (id==user.id) return false
        return this.citizenship.canBan(user.citizenship?: return false)
    }

    fun CurrentUser.canBan(user: UserCitizenship?): Boolean {
        return this.citizenship.canBan(user?: return false)
    }

    fun UserCitizenship.canBan(user: UserCitizenship): Boolean {
        val list: List<UserCitizenship> = when(this) {
            UserCitizenship.GOLDEN,PRESIDENT -> UserCitizenship.entries.filter { it<this }
            else -> UserCitizenship.entries.filter { it.ordinal<this.ordinal-2 }
        }
        return user in list
    }

    fun LayoutReportContentPreviewBinding.setup(item: ReportPreviewProvider, isSelf: Boolean = false, isReportBottomSheet: Boolean = false) {
        this.preview = item
        this.isSelf = isSelf

        fun mediaTypeImage(mediaType: MediaType?): Int {
            return when(mediaType) {
                MediaType.IMAGE -> R.drawable.img_document_share_image
                MediaType.AUDIO -> R.drawable.ic_podium_mic_speaker_on
                MediaType.VIDEO -> R.drawable.ic_media_play
                else -> R.drawable.ic_comment
            }
        }

        when(item.contentType) {
            ReportContentType.FLASH -> {
                previewType.text = root.context.getString(R.string.case_detail_preview_flash)
                previewIcon.setImageResource(R.drawable.ic_media_play)
            }
            ReportContentType.POSTAT -> {
                previewType.text = root.context.getString(R.string.case_detail_preview_postat)
                previewIcon.setImageResource(R.drawable.ic_attach_gallery)
            }
            ReportContentType.HUDDLE_POST -> {
                previewType.text = root.context.getString(R.string.case_detail_preview_huddle)
                previewIcon.setImageResource(mediaTypeImage(item.mediaMeta?.mediaType))
            }
            ReportContentType.PODIUM -> {
                previewType.isVisible = true
                previewIcon.isVisible = true
                previewType.text = root.context.getString(R.string.podium_common)
                previewIcon.setImageResource(R.drawable.ic_podium_mic_speaker_on)
                userLayout.visibility = if (isReportBottomSheet) View.INVISIBLE else View.VISIBLE
            }
            else -> {
                previewIcon.setImageResource(mediaTypeImage(item.mediaMeta?.mediaType))
                when(item.mediaMeta?.mediaType) {
                    MediaType.IMAGE -> {
                        previewType.text = root.context.getString(R.string.message_list_preview_photo)
                    }
                    MediaType.AUDIO -> {
                        previewType.text = root.context.getString(R.string.message_list_preview_audio, item.mediaMeta?.mediaDuration.orEmpty())
                    }
                    MediaType.VIDEO -> {
                        previewType.text = root.context.getString(R.string.message_list_preview_video, item.mediaMeta?.mediaDuration.orEmpty())
                    }
                    else -> {
                        previewType.text = root.context.getString(R.string.report_preview_text_message)
                    }
                }
            }
        }

        item.countryFlag?.let { previewFlag.setImageResource(it) }
    }
}