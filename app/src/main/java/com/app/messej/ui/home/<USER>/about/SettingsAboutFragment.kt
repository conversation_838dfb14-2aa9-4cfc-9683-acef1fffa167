package com.app.messej.ui.home.settings.about

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentSettingsAboutBinding
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.settings.SettingsViewModel
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class SettingsAboutFragment : Fragment() {

    private lateinit var binding: FragmentSettingsAboutBinding
    private val viewModel: SettingsViewModel by viewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_settings_about, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.account_help_sub_header)
    }
    private fun observe() {
        viewModel.user.observe(viewLifecycleOwner){
            //Hiding as per requirement
//            it?.let {
//                if (it.profile.premium) {
//                    binding.subscriptionSettings.subTitle = getString(R.string.settings_sub_title_already_subscribed, it.profile.formattedSubscriptionExpiration)
//                } else {
//                    binding.subscriptionSettings.subTitle = getString(R.string.settings_sub_title_subscription)
//                    binding.locationSettings.root.visibility = View.GONE
//                }
//            }
        }
        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
//            binding.subscriptionSettings.root.isClickable = it
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        setUpOnClickListeners()
    }

    private fun setUpOnClickListeners() {
        with(binding) {
            privacyPolicy.root.setOnClickListener { navigation(DocumentType.PRIVACY_POLICY) }
            termsUse.root.setOnClickListener { navigation(DocumentType.TERMS_OF_USE) }
//            subscriptionSettings.root.setOnClickListener { upgradeToPremium() }
//            locationSettings.root.setOnClickListener { onLocationClick() }
            usernameGuide.root.setOnClickListener { navigation(DocumentType.USERNAME_GUIDE_LINES) }
            communityGuide.root.setOnClickListener { navigation(DocumentType.COMMUNITY_GUIDE_LINES) }
            cookiesPolicy.root.setOnClickListener { navigation(DocumentType.COOKIES_POLICY) }
            huddlePolicy.root.setOnClickListener { navigation(DocumentType.HUDDLE_POLICY) }
            homeAppBusinessPolicy.root.setOnClickListener { navigation(DocumentType.HOME_APP_BUSINESS) }
            groupPolicy.root.setOnClickListener { navigation(DocumentType.GROUP_POLICY) }
            giftPolicy.root.setOnClickListener { navigation(DocumentType.GIFT_POLICY)}
            ppPolicy.root.setOnClickListener{navigation(DocumentType.PP_RULES_REGULATIONS)}
            podiumPolicy.root.setOnClickListener{navigation(DocumentType.PODIUM_POLICY)}
            stateAffairPolicy.root.setOnClickListener{navigation(DocumentType.STATE_AFFAIRS_ABOUT)}
            deleteSettings.root.setOnClickListener { onDeleteAccountClick() }
            legalAffairsPolicy.root.setOnClickListener { navigation(type = DocumentType.LEGAL_AFFAIRS_ABOUT) }
        }
    }
    
    fun navigation(type: DocumentType) {
        findNavController().navigateSafe(
            SettingsAboutFragmentDirections.actionGlobalPolicyFragment(type, false)
        )
    }

    private fun onLocationClick() {
//        findNavController().navigateSafe(SettingsAboutFragmentDirections.actionSettingsAboutFragmentToChangeUserLocationFragment())
    }

    //Hiding this and moved to account management screen
//    private fun upgradeToPremium() {
//        if (subscriptionStatusViewModel.subscriptionLoaded.value==false) return
//        subscriptionStatusViewModel.isActive.value?.let { isActive ->
//            when (isActive) {
//                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
//                    findNavController().navigateSafe(
//                        SettingsAboutFragmentDirections.actionSettingsAboutFragmentToAlreadySubscribedFragment(false)
//                    )
//                }
//                else -> {
//                    findNavController().navigateSafe(
//                        SettingsAboutFragmentDirections.actionGlobalUpgradePremiumFragment()
//                    )
//                }
//            }
//        }
//    }

    private fun onDeleteAccountClick() {
        findNavController().navigateSafe(SettingsAboutFragmentDirections.actionSettingsAboutFragmentToDeleteAccountFragment())
    }
}