package com.app.messej.ui.home.settings.levels

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import com.app.messej.R
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer

interface onClick{
    fun onUpgradeLevelClick()
    fun gotoAuthorities()
}
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun LevelsComposeScreen(
    viewModel: LevelsViewModel,listener:onClick
) {
    val scrollState = rememberScrollState()
    val isAlertDialogVisible by viewModel.isAlertDialogVisible.observeAsState()
    val accountDetails by viewModel.accountDetails.observeAsState()

    if (isAlertDialogVisible == true) {
        AboutUserLevelDialogView(
            onClose = {
                viewModel.setAlertDialogVisibility(isVisible = false)
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(state = scrollState)
    ) {
        LevelScreenCitizenShipBannerView(
            citizenShip = accountDetails?.citizenship,
            citizenShipFromDate = accountDetails?.formatedCitizenshipUpdatedDate,
            onClick = {
                viewModel.setAlertDialogVisibility(isVisible = true)
            },
       listener = listener
        )
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        Text(
            text = stringResource(id = R.string.level_citizenship_progress_title),
            modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            style = FlashatComposeTypography.defaultType.subtitle1,
            color = colorResource(id = R.color.textColorPrimary)
        )
        Text(
            text = stringResource(id = R.string.level_citizenship_progress_description),
            modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.line_spacing))
                .padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            style = FlashatComposeTypography.defaultType.caption,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        FlowRow(modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
                maxItemsInEachRow = 2,
                verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
                horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
        ) {
            repeat(times = viewModel.citizenShipAndDescription.size) {
                val item = viewModel.citizenShipAndDescription[it]
                CitizenshipLevelView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(weight = 1F),
                    citizenship = item.first,
                    iconVisible = item.first.isPresident,
                    onIconClick = {
                        listener.gotoAuthorities()
                    },
                    description = stringResource(id = item.second)
                )
            }
        }
        repeat(times = viewModel.citizenShipAndDescriptionPresidentAndGolden.size) {
            val item = viewModel.citizenShipAndDescriptionPresidentAndGolden[it]
            CitizenshipLevelView(
                modifier = Modifier.fillMaxWidth()
                    .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
                    .padding(top = dimensionResource(id = R.dimen.element_spacing)),
                citizenship = item.first,
                description = stringResource(id = item.second)
            )
        }
    }
}