package com.app.messej.ui.home.settings.levels

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.AlertDialog
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.app.messej.R
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.ETribeUtil.setupETribeCitizenshipTextColor
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.settings.levels.LevelUtils.displayCitizenshipLevelText
import com.app.messej.ui.home.settings.levels.LevelUtils.setupCitizenshipLevelBackground
import com.app.messej.ui.home.settings.levels.LevelUtils.setupCitizenshipLevelBannerBackground
import com.app.messej.ui.home.settings.levels.LevelUtils.setupCitizenshipLevelDescriptionTextColor
import com.app.messej.ui.home.settings.levels.LevelUtils.setupCitizenshipLevelIconBackground
import com.app.messej.ui.utils.EnumUtils.displayText
import java.util.Locale

@Composable
fun LevelScreenCitizenShipBannerView(
    citizenShip: UserCitizenship?,
    citizenShipFromDate: String?,
    onClick: () -> Unit,
    listener:onClick?=null
) {
    if (citizenShip == null) return
    Column( modifier = Modifier
        .fillMaxWidth()
        .setupCitizenshipLevelBannerBackground(citizenShip = citizenShip)
        .padding(all = dimensionResource(id = R.dimen.activity_margin)))
    {
        Row(modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier
                .fillMaxWidth()
                .weight(weight = 1F)) {
                Text(text = buildAnnotatedString {
                    append(stringResource(id = if (citizenShip.isPresident) R.string.level_citizenship_banner_title_president else R.string.level_citizenship_banner_title))
                    withStyle(style = SpanStyle(color = colorResource(id = R.color.colorPrimary))) {
                        append(" ${stringResource(id = citizenShip.displayText()).uppercase(Locale.getDefault())}")
                    }
                }, style = FlashatComposeTypography.defaultType.subtitle1)

                citizenShipFromDate?.let {
                    Text(
                        text = stringResource(id = R.string.level_citizenship_since_date, citizenShipFromDate),
                        style = FlashatComposeTypography.defaultType.overline
                    )
                }
            }
            Icon(modifier = Modifier
                .clip(shape = CircleShape)
                .clickable { onClick() },
                 painter = painterResource(id = R.drawable.ic_exclamation_mark_rounded),
                 tint = colorResource(id = when (citizenShip) {
                     UserCitizenship.MINISTER, UserCitizenship.PRESIDENT, UserCitizenship.RESIDENT, UserCitizenship.VISITOR -> R.color.white
                     else ->R.color.colorPrimary
                 }), contentDescription = null
            )
        }
        if(citizenShip.canUpgradeUserLevel) {
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
            CustomLargeRoundButton(icon = {
                Icon(painter = painterResource(id = R.drawable.ic_promo_upgrade), contentDescription = null, tint = Color.Unspecified)
            }, text = R.string.upgrade_my_level, onClick = {listener?.onUpgradeLevelClick()}, isTextCaps = false)
        }
        Icon(modifier = Modifier.clip(shape = CircleShape).clickable { onClick() },
            painter = painterResource(id = R.drawable.ic_exclamation_mark_rounded),
            tint = colorResource(id = when (citizenShip) {
                UserCitizenship.MINISTER, UserCitizenship.PRESIDENT, UserCitizenship.RESIDENT, UserCitizenship.VISITOR -> R.color.white
                UserCitizenship.GOLDEN ->R.color.colorFlashatGolden
                else ->R.color.colorPrimary
            }), contentDescription = null
        )
    }

}


@Preview
@Composable
fun LevelScreenCitizenShipBannerViewPreview() {
    LevelScreenCitizenShipBannerView(
        citizenShip = UserCitizenship.CITIZEN,
        citizenShipFromDate = "10/06/2024",
        onClick = {},
        listener = null
    )
}

@Composable
fun CitizenshipLevelView(
    modifier: Modifier = Modifier,
    citizenship: UserCitizenship,
    description: String,
    iconVisible: Boolean = false,
    onIconClick: (() -> Unit)? = null
) {
    val isPresident = citizenship == UserCitizenship.PRESIDENT
    val isRTLDirection = LocalLayoutDirection.current == LayoutDirection.Rtl

    Column(modifier = modifier
        .setupCitizenshipLevelBackground(citizenShip = citizenship)
        .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = dimensionResource(id = R.dimen.activity_margin), end = dimensionResource(id = R.dimen.line_spacing)),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Box(
                modifier = Modifier
                    .width(intrinsicSize = IntrinsicSize.Max)
                    .height(intrinsicSize = IntrinsicSize.Max),
                contentAlignment = Alignment.CenterStart
            ) {
                Image(
                    painter = painterResource(id = if (isPresident) R.drawable.bg_president_level_text else R.drawable.bg_citizenship_left),
                    modifier = Modifier
                        .rotate(degrees = if (isRTLDirection) 180F else 0F)
                        .fillMaxWidth()
                        .fillMaxHeight(),
                    colorFilter = if (isPresident) null else ColorFilter.tint(color = colorResource(id = citizenship.setupCitizenshipLevelIconBackground())),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Text(
                    text = stringResource(id = citizenship.displayCitizenshipLevelText()),
                    style = FlashatComposeTypography.defaultType.subtitle1,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = dimensionResource(id = R.dimen.element_spacing), end = dimensionResource(id = R.dimen.extra_margin)
                        ), color = colorResource(id = citizenship.setupETribeCitizenshipTextColor())
                )
            }
            if (iconVisible) {
                Icon(
                    modifier = Modifier
                        .clip(shape = CircleShape)
                        .clickable { onIconClick?.let { it() } },
                    painter = painterResource(id = R.drawable.ic_exclamation_mark_rounded),
                    tint = Color.Unspecified, contentDescription = null
                )
            }
        }
        Text(
            text = description,
            minLines = 2,
            style = FlashatComposeTypography.defaultType.caption.copy(fontWeight = FontWeight.W600),
            modifier = Modifier
                .padding(all = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            color = colorResource(id = citizenship.setupCitizenshipLevelDescriptionTextColor())
        )
    }
}

@Preview
@Composable
fun CitizenshipLevelViewPreview() {
    CitizenshipLevelView(
        citizenship = UserCitizenship.MINISTER,
        description = "Upgrading the Flashat Premium Version",
    )
}

@Composable
fun AboutUserLevelDialogView(
    onClose: () -> Unit
) {
    val descriptionArray = stringArrayResource(id = R.array.about_user_levels_description)
    AlertDialog(
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false),
        onDismissRequest = { onClose() },
        backgroundColor = colorResource(id = R.color.colorSurfaceSecondaryDark),
        confirmButton = {
            TextButton(onClick = onClose) {
                Text(text = stringResource(id = R.string.common_close),
                     color = colorResource(id = R.color.colorError),
                     style = FlashatComposeTypography.defaultType.body2
                )
            }
        },
        title = {
            Text(text = stringResource(id = R.string.about_user_level_title),
                 color = colorResource(id = R.color.colorPrimary),
                 style = FlashatComposeTypography.defaultType.subtitle2
            )
        },
        text = {
            Column(verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))) {
                repeat(descriptionArray.size) {
                    val item = descriptionArray[it]
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Box(modifier = Modifier
                            .padding(top = 6.dp, end = 6.dp)
                            .size(size = 5.dp)
                            .background(color = colorResource(id = R.color.textColorPrimary), shape = CircleShape))
                        Text(text = item,
                             color = colorResource(id = R.color.textColorPrimary),
                             style = FlashatComposeTypography.defaultType.body2
                        )
                    }
                }
            }
        }
    )
}

@Preview
@Composable
fun AboutUserLevelDialogPreview() {
    AboutUserLevelDialogView(onClose = {})
}