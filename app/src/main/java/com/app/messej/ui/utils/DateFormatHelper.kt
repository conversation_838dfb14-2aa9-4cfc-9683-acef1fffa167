package com.app.messej.ui.utils

import android.content.Context
import com.app.messej.R
import com.app.messej.data.utils.DateTimeUtils
import java.text.SimpleDateFormat
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

object DateFormatHelper {

    /**
     * Used in main chat lists
     */
    fun humanizeMessageTime(time: ZonedDateTime?, c: Context): String {
        val date = DateTimeUtils.getLocalDate(time ?: return "")
        return if (date.isEqual(LocalDate.now())) {
            DateTimeUtils.format(time, format = DateTimeUtils.FORMAT_READABLE_TIME)
        } else if (date.isEqual(LocalDate.now().minusDays(1))) {
            c.getString(R.string.common_date_yesterday)
        } else if (Duration.between(time.toInstant(), Instant.now()).toDays() < 7) {
            DateTimeUtils.format(time, format = DateTimeUtils.FORMAT_DAY_LONG)
        } else {
            DateTimeUtils.format(time, format = DateTimeUtils.FORMAT_DDMMYY_SLASHED)
        }
    }

    /**
     * humanize date to the following format:
     * Today: If same day
     * Yesterday: If 1 day ago
     * Wednesday: If in the past 7 days
     * 11 Jan 2023: otherwise
     */
    fun humanizeChatDate(time: ZonedDateTime?, c: Context): String {
        val date = DateTimeUtils.getLocalDate(time ?: return "")
        return if (date.isEqual(LocalDate.now())) {
            c.getString(R.string.common_date_today)
        } else if (date.isEqual(LocalDate.now().minusDays(1))) {
            c.getString(R.string.common_date_yesterday)
        } else if (Duration.between(time.toInstant(), Instant.now()).toDays() < 7) {
            DateTimeUtils.format(time, format = DateTimeUtils.FORMAT_DAY_LONG)
        } else {
            DateTimeUtils.format(time, format = DateTimeUtils.FORMAT_READABLE_DATE)
        }
    }

     fun getConvertedDate(inputDateString: String): String {
        val inputDateFormat = SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH)
        val inputDate = inputDateFormat.parse(inputDateString)

        val outputDateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.ENGLISH)
        return outputDateFormat.format(inputDate)
    }

    fun getPeriod(expiryDate: String): Period {
        val currentDate = LocalDate.now()
        val formatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH).withZone(ZoneId.of("GMT"))
        val zonedDateTime = ZonedDateTime.parse(expiryDate, formatter)
        return Period.between(currentDate, zonedDateTime.toLocalDate())
    }

    fun getRemainingLocalDate(expiryDate: String):ZonedDateTime {
        val formatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH).withZone(ZoneId.of("GMT"))
        val zonedDateTime = ZonedDateTime.parse(expiryDate, formatter)
        return zonedDateTime
    }

    fun convertToGmtFormattedDate(isoString: String): String {
        val localDateTime = LocalDateTime.parse(isoString) // Parses "2026-07-08T11:47:49"
        val zonedDateTime = localDateTime.atZone(ZoneId.of("GMT")) // Set as GMT
        val formatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH)
        return zonedDateTime.format(formatter)
    }

}