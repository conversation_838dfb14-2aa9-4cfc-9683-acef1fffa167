package com.app.messej.ui.legal.report

import android.app.Dialog
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.graphics.drawable.toDrawable
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.ReportCategoryResponse.ReportCategory
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentReportBinding
import com.app.messej.databinding.ItemCaseDetailIdCardViewBinding
import com.app.messej.databinding.ItemReportProofBinding
import com.app.messej.ui.legal.report.ReportUtils.setup
import com.app.messej.ui.legal.report.ReportViewModel.ProofMediaUIModel
import com.app.messej.ui.profile.PublicUserProfileFragmentArgs
import com.app.messej.ui.profile.PublicUserProfileViewModel
import com.app.messej.ui.utils.BindingExtensions.setupIDCard
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.TextFormatUtils.setAsMandatory
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder


class ReportFragment : BottomSheetDialogFragment() {

    private val args : ReportFragmentArgs by navArgs()
    private lateinit var binding: FragmentReportBinding

    private val viewModel : ReportViewModel by viewModels()

    protected var mProofAdapter: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>? = null

    private var idCardBinding: ItemCaseDetailIdCardViewBinding? = null
    private val userViewModel: PublicUserProfileViewModel by viewModels()
    private lateinit var userIdCardArgs: PublicUserProfileFragmentArgs

    companion object {
        const val REPORT_REQUEST_KEY = "report_item"
        const val REPORT_TYPE = "report_type_key"
        const val REPORT_DID_REPORT = "report_success_key"

        fun parseResult(bundle: Bundle): Pair<ReportContentType,Boolean> {
            return Pair(
                ReportContentType.entries[bundle.getInt(REPORT_TYPE)],
                bundle.getBoolean(REPORT_DID_REPORT),
            )
        }

        fun Fragment.setReportListener(onResult: (type: ReportContentType,success: Boolean) -> Unit): Unit {
            setFragmentResultListenerOnActivity(REPORT_REQUEST_KEY) { _, bundle ->
                val data = parseResult(bundle)
                onResult.invoke(data.first,data.second)
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable=false
            }
        }
        return dialog
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_report, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.preview.hideIdCard = true
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = false
        initAdapter()
        setup()
        observe()
    }

    private fun setup() {
        viewModel.setReportRequest(args.reportPackage)

        binding.reportButton.setOnClickListener {
            val report = viewModel.reportPackage.value?: return@setOnClickListener
            val reason = viewModel.reason.value?: return@setOnClickListener
            val rct = viewModel.reportContentType.value?: return@setOnClickListener
            confirmAction(
                message = if (rct.first==ReportContentType.USER) {
                    if (rct.second==ReportType.REPORT) getString(R.string.report_user_confirm_message,report.user?.name,reason.categoryText)
                    else resources.getString(R.string.report_user_ban_confirm_message,report.user?.name)
                }
                else {
                    if (rct.second == ReportType.REPORT_AND_HIDE) {
                        getString(R.string.report_and_hide_content_confirm_message,report.user?.name)
                    } else {
                        if (rct.first == ReportContentType.PODIUM) getString(if (viewModel.user.citizenship.isPresident) R.string.report_podium_president_confirm_message else R.string.report_podium_confirm_message)
                        else getString(R.string.report_content_confirm_message,report.user?.name)
                    }
                }
            ) {
                viewModel.reportMessage()
            }
        }
        binding.reportCancelButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.addProofButton.setOnClickListener {
            selectMediaFromGallery()
        }

        binding.preview.userLayout.setOnClickListener {
            showIdCard()
        }

        binding.proofTitle.setAsMandatory()
    }

    private fun showIdCard() {
        idCardBinding = ItemCaseDetailIdCardViewBinding.inflate(LayoutInflater.from(requireContext()))

        idCardBinding?.layoutIdCard?.setupIDCard(
            profile = userViewModel.profile.value,
            context = requireContext(),
            lifecycleOwner = viewLifecycleOwner,
            publicUserProfileArgs = userIdCardArgs,
            viewModel = userViewModel
        )

        val materialAlertDialog = MaterialAlertDialogBuilder(requireContext(), R.style.TransparentMaterialAlertDialog)
            .setView(idCardBinding?.root)
            .create()
        materialAlertDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        materialAlertDialog.show()

        idCardBinding?.actionClose?.setOnClickListener {
            materialAlertDialog.dismiss()
        }
    }

    private fun observe() {

        viewModel.commentValid.observe(viewLifecycleOwner) {
            val error = if (it) null else resources.getString(R.string.report_message_comment_error_max, ReportViewModel.COMMENT_MAX_LENGTH)
            binding.tilComment.isErrorEnabled = (error!=null)
            binding.tilComment.error = error
        }

        viewModel.reportPackage.observe(viewLifecycleOwner) {
            it?: return@observe
            it.user?.id?.let { userId ->
                userViewModel.setUserId(
                    id = userId,
                    context = UserProfileContext.GENERAL,
                    isPopupButtonVisible = false
                )
                userIdCardArgs = PublicUserProfileFragmentArgs(
                    id = userId,
                    context = UserProfileContext.GENERAL,
                    popOnAction = false
                )
            }

            userViewModel.profile.observe(viewLifecycleOwner) { profile ->
                idCardBinding?.layoutIdCard?.setupIDCard(
                    profile = profile,
                    context = requireContext(),
                    lifecycleOwner = viewLifecycleOwner,
                    publicUserProfileArgs = userIdCardArgs,
                    viewModel = userViewModel
                )
            }

            binding.preview.setup(item = it, isReportBottomSheet = true)
        }

        viewModel.reportContentType.observe(viewLifecycleOwner) {


            binding.reportTitle.text = when(it?.first) {
                ReportContentType.USER -> {
                    if (it.second==ReportType.REPORT) getString(R.string.report_user_reason_title)
                    else getString(R.string.report_user_ban_reason_title)
                }
                ReportContentType.PODIUM -> getString(R.string.report_podium_content_reason_title)
                else -> getString(R.string.report_content_reason_title)
            }

            if (it?.first == ReportContentType.PODIUM) {
                binding.uploadFileDescription.text = getString(R.string.report_podium_file_upload_description)
                binding.uploadFileDescription.isVisible = true
            }

            binding.reportTitle.setAsMandatory()
        }

        viewModel.reportCategories.observe(viewLifecycleOwner){
            initCategoriesAdapter(it?: emptyList())
        }

        viewModel.proofMedia.observe(viewLifecycleOwner){
            Log.d("RPF", "proof: $it")
            mProofAdapter?.apply {
                if (data.isEmpty() || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.onInvalidData.observe(viewLifecycleOwner){
            showToast("Invalid Data")
            findNavController().popBackStack()
        }

        viewModel.onReportMessageComplete.observe(viewLifecycleOwner) {
            Log.w("REPFRAG", "observe: onReportMessageComplete $it")
            findNavController().popBackStack()
            requireActivity().supportFragmentManager.setFragmentResult(REPORT_REQUEST_KEY, bundleOf(REPORT_TYPE to it.ordinal, REPORT_DID_REPORT to true))
        }

        viewModel.onReportError.observe(viewLifecycleOwner) {
            showToast(it)
        }
    }

    private fun initCategoriesAdapter(cats: List<ReportCategory>) {

        val adapter = object: ArrayAdapter<ReportCategory>(requireContext(), R.layout.item_general_dropdown, cats) {

            private val mCats = cats
            var selectedPos: Int? = null
            override fun getItem(position: Int) = mCats[position]
            override fun getCount() = mCats.size
            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                var cView = convertView
                if (cView == null) {
                    cView = layoutInflater.inflate(R.layout.item_general_dropdown, parent, false)
                }
                cView!!
                getItem(position).let { cat ->
                    (cView.findViewById<AppCompatTextView>(R.id.text)).text = cat.categoryText
                    (cView.findViewById<AppCompatImageView>(R.id.checked_icon)).visibility = if (position == selectedPos) View.VISIBLE else View.GONE
                }
                return cView
            }
        }
        (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(adapter)
            setOnItemClickListener { _, _, position, _ ->
                adapter.selectedPos = position
                val item = adapter.getItem(position)
                setText(item.categoryText)
                viewModel.reason.postValue(item)
            }
        }

        viewModel.reason.value?.let { reason ->
            val index = cats.indexOfFirst { reason.categoryId == it.categoryId }
            if(index==-1) return@let
            adapter.selectedPos = index
            (binding.categoryDropdown.editText as? AutoCompleteTextView)?.setText(reason.categoryText)
        }

    }

    private fun initAdapter() {
        val differ = object : DiffUtil.ItemCallback<ProofMediaUIModel>() {
            override fun areItemsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem.proof.uuid == newItem.proof.uuid
            }

            override fun areContentsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem == newItem
            }
        }

        mProofAdapter = object: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>(R.layout.item_report_proof, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemReportProofBinding>, item: ProofMediaUIModel) {
                holder.dataBinding?.apply {
                    model = item
                    fileType.setImageResource(
                        when (item.proof.mediaType) {
                            MediaType.IMAGE -> R.drawable.ic_attach_gallery
                            MediaType.VIDEO -> R.drawable.ic_attach_video
                            else -> 0
                        }
                    )
                    deleteButton.setOnClickListener {
                        viewModel.removeMedia(item)
                    }
                }
            }
        }
        binding.proofList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mProofAdapter
        }
        mProofAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(differ)
        }
    }

    private val selectImageOrVideoFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
            uri?.let {
                viewModel.addMedia(uri)
            }
        }

    private fun selectMediaFromGallery() {
        val request = if(viewModel.user.premium) arrayOf("image/*", "video/*") else arrayOf("image/*")
        selectImageOrVideoFromGalleryResult.launch(request)
    }
}