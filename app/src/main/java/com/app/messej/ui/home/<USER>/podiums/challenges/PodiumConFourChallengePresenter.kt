package com.app.messej.ui.home.publictab.podiums.challenges

import android.annotation.SuppressLint
import android.graphics.Color
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.GridLayout
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.BoardData
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge.ChallengeStatus
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.api.podium.challenges.SlotState
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.data.model.socket.ConFourTokenDroppedPayload
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemConfourParticipantTileBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardConfourBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardConfourSlotBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardConfourSlotCoverBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardConfourSpeakerOverlayBinding
import com.app.messej.databinding.LayoutPodiumChatOverlayUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.awaitEnd
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.printableString
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import jp.wasabeef.transformers.glide.ColorFilterTransformation
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.Duration
import kotlin.math.roundToInt

class PodiumConFourChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener, private val conFourListener: ConFourChallengeEventListener): PodiumChallengePresenter(holder, challenge, challengeListener) {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardConfourBinding
    private var playerOverlayBinding: LayoutPodiumChallengeBoardConfourSpeakerOverlayBinding? = null
    private var chatOverlayBinding: LayoutPodiumChatOverlayUserJoinedBinding? = null

    private var gameBoard = ConnectFourBoard()

    companion object {
        @JvmStatic
        @BindingAdapter("imageUrl", "playerActive")
        fun loadImage(view: AppCompatImageView, url: String?, active: Boolean) { // This methods should not have any return type, = declaration would make it return that object declaration.
            val placeholder = ContextCompat.getDrawable(view.context,R.drawable.im_user_placeholder_square_always_dark)
            if (url == null) {
                Glide.with(view.context).load(placeholder).error(placeholder)
                    .transition(DrawableTransitionOptions.withCrossFade()).into(view)
                return
            }
            var glide = Glide.with(view.context).load(url).error(placeholder)
                .transition(DrawableTransitionOptions.withCrossFade())
            if (!active) {
                glide = glide.transform(ColorFilterTransformation(Color.argb(0.8f,0f,0f,0f)))
//                glide = glide.transform(
//                    VignetteFilterTransformation(
//                        PointF(0.5f, 0.5f),
//                        floatArrayOf(0.0f, 0.0f, 0.0f), 0f, 0.75f)
//            )
            }
            glide.into(view)
        }
    }

    interface ConFourChallengeEventListener {
        fun showHowToPlayOverlay(millis: Long)

        fun submitCoinDrop(slot: ConnectFourBoard.DropPoint, player: ChallengePlayer, gameStatus: ConFourGameStatus?)

        fun sendTimeOut(player: ChallengePlayer, currentPlayerId: Int)

        fun playDropSound()
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_confour_title


    override fun setupView() {
        liveBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_confour, holder, false)
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        val board = challenge.conFourData?.board ?: return
        val boardRequiresRedraw = gameBoard.needsRepair(board, lastDrop)
        val diff = gameBoard.calculateDiff(board)
        if (boardRequiresRedraw) {
            Log.e("PCPC4", "onNewScoresAvailable: gameboard needs repair")
            Log.w("PCPC4", "gameboard: \n${gameBoard.printableString}")
            Log.w("PCPC4", "payload: \n${board.printableString()}")
        }
        Log.w("PCPC4", "onNewScoresAvailable: controlsEnabled $controlsEnabled, iAmCurrentPlayer $iAmCurrentPlayer")
        Log.w("PCPC4", "onNewScoresAvailable: forceRefresh $forceRefresh boardRequiresRedraw $boardRequiresRedraw resetControls: ${controlsEnabled != iAmCurrentPlayer}")
        if ((forceRefresh || boardRequiresRedraw || controlsEnabled != iAmCurrentPlayer)) refreshChallengeUI(challenge.status)
        else refreshScores()
    }

    override fun cleanup() {
        super.cleanup()
        for (row in 0 until ConnectFourBoard.ROWS) {
            for (col in 0 until ConnectFourBoard.COLS) {
                gameBoardSlotBindings[row][col] = null
            }
        }
        challengeListener.allowCustomBoard(false)
        hideOverlays()
    }

    override fun onSpeakerClick(item: PodiumSpeakerUIModel.ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        return true
    }

    private data class SlotAndCover(
        val slot: LayoutPodiumChallengeBoardConfourSlotBinding,
        val cover: LayoutPodiumChallengeBoardConfourSlotCoverBinding
    )

    private var gameBoardSlotBindings: Array<Array<SlotAndCover?>> = Array(ConnectFourBoard.ROWS) {
        Array(ConnectFourBoard.COLS) {
            null
        }
    }

    override fun refreshChallengeUI(status: ChallengeStatus) {
        try {
            scope.launch {
                if (status in listOf(ChallengeStatus.GAME_OVER,ChallengeStatus.ENDED) && uiState==UIState.LIVE) {
                    Log.w("PCPC4", "refreshChallengeUI: waiting for winner animation to finish")
                    winnerAnimationJob?.join()
                    Log.w("PCPC4", "refreshChallengeUI: winner animation finished")
                    hideOverlays()
                    challengeListener.allowCustomBoard(false)
                    super.refreshChallengeUI(status)
                } else {
                    super.refreshChallengeUI(status)
                }
            }
        } catch (_: Exception) {}
    }

    private fun populateBoard() {
        gameBoard.board.let { board ->
            board.forEachIndexed { rowIndex, row ->
                row.forEachIndexed { colIndex, value ->
                    gameBoardSlotBindings[rowIndex][colIndex]?.slot?.setState(value)
                }
            }
        }
    }

    override fun refreshLiveUI() {
        Log.w("PCPC4", "refreshLiveUI: $challenge", )
        scope.launch {
            Log.w("PCPC4", "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}", )

            liveBinding.handIcon.setCustomThumbDrawable(R.drawable.ic_challenge_confour_hand)
            liveBinding.gameBoardView.isVisible = false
            liveBinding.content.isVisible = false
            
            Log.w("PCPC4", "refreshLiveUI ${challenge.hasStarted} ${challenge.gameOver}" )
            if (challenge.hasStarted) {

                challengeListener.allowCustomBoard(true)
                liveBinding.gameBoardView.isVisible = true

                liveBinding.gridLayout.removeAllViews()
                liveBinding.gridLayoutCover.removeAllViews()

                fun View.setSlotLP(row: Int, col: Int) {
                    val layoutParams = this.layoutParams as GridLayout.LayoutParams
                    layoutParams.rowSpec = GridLayout.spec(row)
                    layoutParams.columnSpec = GridLayout.spec(col, 1f)
                    this.layoutParams = layoutParams
                }
                for (row in 0 until ConnectFourBoard.ROWS) {
                    for (col in 0 until ConnectFourBoard.COLS) {
                        val slot: LayoutPodiumChallengeBoardConfourSlotBinding =
                            DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_confour_slot, liveBinding.gridLayout, false)
                        val slotCover: LayoutPodiumChallengeBoardConfourSlotCoverBinding =
                            DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_confour_slot_cover, liveBinding.gridLayoutCover, false)

                        slot.root.setSlotLP(row, col)
                        slotCover.root.setSlotLP(row, col)
                        liveBinding.gridLayout.addView(slot.root)
                        liveBinding.gridLayoutCover.addView(slotCover.root)
                        gameBoardSlotBindings[row][col] = SlotAndCover(slot, slotCover)
                    }
                }

                challenge.conFourData?.let {
                    gameBoard.board = it.board
                }

                populateBoard()

                fun View.setBias(bias: Float) {
                    val layoutParams = this.layoutParams as ConstraintLayout.LayoutParams
                    layoutParams.horizontalBias = bias
                    this.layoutParams = layoutParams
                }
                if (playerOverlayBinding==null) {
                    challengeListener.showSpeakerOverlay(true, 2f).apply {
                        val binding = LayoutPodiumChallengeBoardConfourSpeakerOverlayBinding.inflate(layoutInflater, holder, false)
                        this.fillAddView(binding.root)
                        binding.playerOne.apply {
                            winsChip.setBias(0f)
                            coinsChip.setBias(0f)
                            levelChip.setBias(0f)
                        }
                        binding.playerTwo.apply {
                            winsChip.setBias(1f)
                            coinsChip.setBias(1f)
                            levelChip.setBias(1f)
                        }
                        refreshScores()
                        playerOverlayBinding = binding
                    }
                }
                challengeListener.showChatOverlay(true).apply {
                    val binding = LayoutPodiumChatOverlayUserJoinedBinding.inflate(layoutInflater, holder, false)
                    this.fillAddView(binding.root)
                    chatOverlayBinding = binding
                }
                if (!challenge.gameOver) {
                    showPlayerTurn()
                } else {
                    // wait for game over event from BE
                }
            } else {
                hideOverlays()
                challengeListener.allowCustomBoard(false)
                liveBinding.content.isVisible = true
                liveBinding.content.showStartCountdown({
                       if (challengeListener.getLiveViewModel().iAmPartOfRunningChallenge.value == true) {
                           conFourListener.showHowToPlayOverlay(challenge.countDownRemaining?.toMillis() ?: 0)
                       }
                   }, {
                       refreshLiveUI()
                   })
            }
        }
    }

    @DrawableRes
    private fun ChallengePlayer.coinRes(): Int {
        return when(this) {
            ChallengePlayer.PLAYER1 -> R.drawable.ic_confour_coin_one
            ChallengePlayer.PLAYER2 -> R.drawable.ic_confour_coin_two
        }
    }

    private fun ChallengePlayer.playerBinding(): ItemConfourParticipantTileBinding? {
        return when(this) {
            ChallengePlayer.PLAYER1 -> playerOverlayBinding?.playerOne
            ChallengePlayer.PLAYER2 -> playerOverlayBinding?.playerTwo
        }
    }

    private fun LayoutPodiumChallengeBoardConfourSlotBinding.setState(state: SlotState) {
        val color = state.player?.coinRes()
        coin.setImageResource(color ?: 0)
        coin.isVisible = color != null
    }

    private var lastDrop: ConnectFourBoard.DropPoint? = null

    fun onCoinDropped(payload: ConFourTokenDroppedPayload) {
        Log.w("PCPC4", "onCoinDropped: $payload",)
        if (uiState!=UIState.LIVE) return
        challenge.conFourData = payload.confourData
        if (gameBoard.needsRepair(payload.gameBoard,payload.lastDrop)) {
            Log.e("PCPC4", "gameboard needs repair, [Ignore: ${payload.lastDrop}]")
            Log.w("PCPC4", "gameboard: \n${gameBoard.printableString}")
            Log.w("PCPC4", "payload: \n${payload.gameBoard.printableString()}")
            refreshLiveUI()
        } else {
            try {
                payload.lastDrop?.let { drop ->
                    lastDrop = drop
                    payload.lastDropPlayer?.let { player ->
                        gameBoard.registerDrop(drop, player)
                        animateCoinDrop(drop, player)
                        if (gameBoard.gameStatus == ConFourGameStatus.WON) {
                            gameBoard.connectFourLine?.let {
                                animateWin(it)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(context, e.message, Toast.LENGTH_SHORT).show()
            }
        }
        showPlayerTurn()
    }

    private fun dropCoin(column: Int) {
        try {
            val player = meAsPlayer ?: return
            val slot = gameBoard.dropPiece(column, player)
            lastDrop = slot?.first
            Log.w("PCPC4", "dropCoin: player $player | slot; $slot")
            if (slot != null) {
                slot.first.let { conFourListener.submitCoinDrop(it, player, slot.second) }
            } else {
                Toast.makeText(context, "Column is full", Toast.LENGTH_SHORT).show()
            }
            challenge.conFourData?.currentPlayerUserId = null
        } catch (_: Exception) {
        }
    }

    private fun animateCoinDrop(point: ConnectFourBoard.DropPoint, player: ChallengePlayer) {
        scope.launch {
            try {
                gameBoardSlotBindings[point.row][point.column]?.slot?.apply {
                    val slotHeight = root.height
                    coin.translationY = ((slotHeight * point.row) + (slotHeight * 0.5f)) * -1
                    setState(player.slotState)
                    coin.animate().apply {
                        interpolator = AccelerateDecelerateInterpolator()
                        setDuration(200L + (point.row * 100))
                        translationY(0f)
                        start()
                        awaitEnd()
                    }
                }
                conFourListener.playDropSound()
            } finally {
                gameBoardSlotBindings[point.row][point.column]?.slot?.apply {
                    coin.translationY = 0f
                }
                populateBoard()
            }
        }
    }

    var winnerAnimationJob: Job? = null

    private fun animateWin(line: ConnectFourBoard.ConnectFourLine) {
        winnerAnimationJob?.cancel()
        val playerBinding = line.player.playerBinding()?: return
        winnerAnimationJob = scope.launch {
            line.connectionSlots.forEach { slot ->
                gameBoardSlotBindings[slot.row][slot.column]?.cover?.apply {
                    this.slotMatch.isVisible = true
                }
            }
            playerBinding.winnerImage.apply {
                isVisible = true
                setImageResource(line.player.trophyRes())
                scaleX = 0.1f
                scaleY = 0.1f
                this.animate().apply {
                    interpolator = OvershootInterpolator()
                    setDuration(500)
                    scaleX(1f)
                    scaleY(1f)
                    start()
                    awaitEnd()
                }
            }
            delay(5000)
        }.apply {
            invokeOnCompletion {
                winnerAnimationJob = null
            }
        }
    }

    override fun onUpdateChallengeAndScores(cs: PodiumChallenge) {
        Log.w("PCPC4", "onUpdateChallengeAndScores: Con4:${challenge.conFourData}")
        super.onUpdateChallengeAndScores(cs)
    }

    private fun refreshScores() {
        Log.w("PCPC4", "refreshScores" )

        fun ItemConfourParticipantTileBinding.updateView(player: ChallengePlayer) {
            player.getScore()?.let { sc ->
                this.score = sc
                this.coinColorView.setImageResource(player.coinRes())
                val color = ContextCompat.getColor(context,player.colorRes())
                this.turnHolder.setCardBackgroundColor(color)
                this.turnText.setTextColor(ContextCompat.getColor(context,player.colorOnColorRes()))
                this.timerProgress.setIndicatorColor(color)
            }
        }
        playerOverlayBinding?.apply {
            playerOne.updateView(ChallengePlayer.PLAYER1)
            playerTwo.updateView(ChallengePlayer.PLAYER2)
        }
    }

    override fun convertScore(sc: PodiumChallengeScore): String {
        return sc.coinsWon?.numberToKWithFractions()?:"0"
    }

    private var controlsEnabled = false

    override val boardData: BoardData?
        get() = challenge.conFourData

    private fun showPlayerTurn() {

        scope.launch {
            tickerJob?.cancelAndJoin()
            Log.d("PCP", "tickerJob canceled in: showPlayerTurn")

            playerOverlayBinding?.apply {
                Log.w("PCPC4", "showPlayerTurn: refreshing scores...")
                refreshScores()

                val currentPlayer = currentPlayer ?: return@apply
                val playerBinding = currentPlayer.playerBinding() ?: return@apply
                Log.w("PCPC4", "showPlayerTurn: currentPlayer $currentPlayer")

                playerBinding.currentPlayer = true
                playerBinding.playerTimer.isVisible = true
                playerBinding.turnHolder.isVisible = iAmCurrentPlayer

                currentPlayer.otherPlayer.playerBinding()?.apply {
                    this.currentPlayer = false
                    this.playerTimer.isVisible = false
                    this.turnHolder.isVisible = false
                }
                Log.w("PCPC4", "showPlayerTurn: currentPlayer left time ${getCurrentPlayerTimeLeft().seconds}")
                showTimer(playerBinding.timerCount, playerBinding.timerProgress, getCurrentPlayerTimeLeft()) { cancelled ->
                    playerBinding.playerTimer.isVisible = false
                    playerBinding.turnHolder.isVisible = false
                    if (!cancelled) {
//                        delay(2000)
                        //code will only reach here if the coin dropped event is not triggered even after timer runs out.
                        currentPlayerId?.let { conFourListener.sendTimeOut(currentPlayer, it) }
                    }
                }
                enableDropControlsIfRequired()
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun enableDropControlsIfRequired() {
        Log.w("PCPC4", "enableDropControlsIfRequired" )
        Log.w("PCPC4", "enableDropControlsIfRequired: meAsPlayer; $meAsPlayer")
        liveBinding.handIcon.visibility = if (meAsPlayer==null) View.GONE else View.INVISIBLE
        Log.w("PCPC4", "enableDropControlsIfRequired: ${challenge.conFourData?.currentPlayerUserId} == $myUserId")
        Log.w("PCPC4", "enableDropControlsIfRequired: ${challenge.conFourData}")
        controlsEnabled = iAmCurrentPlayer
        if (!iAmCurrentPlayer) return
        liveBinding.handIcon.isVisible = true
        val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                val column = liveBinding.handIcon.value.roundToInt().coerceIn(0 until ConnectFourBoard.ROWS + 1)
                dropCoin(column)
                return super.onDoubleTap(e)
            }
        })

        liveBinding.handIcon.setOnTouchListener { _, event ->
            gestureDetector.onTouchEvent(event)
        }
    }

    private fun getCurrentPlayerTimeLeft(): Duration {
        return challenge.conFourData?.turnEndTime?.let {
            Log.w("PCPC4", "getCurrentPlayerTimeLeft: start ${challenge.conFourData?.parsedTurnStartTime} end ${challenge.conFourData?.turnEndTime} left ${DateTimeUtils.durationFromNowToFuture(it)?.seconds}")
            DateTimeUtils.durationFromNowToFuture(it)
        }?: Duration.ZERO
    }

    override fun showFinalScores(): Boolean  = false

    override fun onUserJoined(user: PodiumParticipant) {
        chatOverlayBinding?.addToFlow(user)
    }

}