package com.app.messej.ui.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.data.model.entity.UserStatsResult
import com.app.messej.databinding.FragmentPopularityDetailsBottomSheetDialogBinding
import com.app.messej.ui.style.FlashatComposeTypography
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import java.util.Locale.getDefault


class PopularityDetailsBottomSheetDialogFragment : BottomSheetDialogFragment() {

    private var binding: FragmentPopularityDetailsBottomSheetDialogBinding? = null
    private val popularityDetailsViewModel : PopularityDetailsViewModel by viewModels()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.Widget_Flashat_SettingsBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = FragmentPopularityDetailsBottomSheetDialogBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {

        popularityDetailsViewModel.getPopularity()

        binding?.composeView?.setContent {
            val popularityDetails by popularityDetailsViewModel.popularity.observeAsState()
            PopularityCalculationsScreen(popularityData = popularityDetails)
        }
    }
}



data class PopularityItem(
    val description: String,
    val points: String,
)

@Composable
fun PopularityCalculationsScreen(modifier: Modifier = Modifier,popularityData: UserStatsResult?) {
    val scroll = rememberNestedScrollInteropConnection()
    val context = LocalContext.current
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = dimensionResource(R.dimen.activity_margin), topEnd = dimensionResource(R.dimen.activity_margin))) // Rounded corners
            .background(color = colorResource(R.color.colorSurface)) // Background color for the top part
            .nestedScroll(scroll)
            .padding(dimensionResource(R.dimen.activity_margin)),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    )
    {
        item {
            Text(
                text = stringResource(R.string.popularity_bottom_sheet_title).uppercase(getDefault()),
                textAlign = TextAlign.Center,
                color = colorResource(R.color.textColorPrimary),
                style = FlashatComposeTypography.defaultType.h6.copy(),
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .padding(top = 8.dp)
                    .fillMaxWidth()
            )
        }
        item {
            DashedDivider()
        }
        item {
            Text(text = stringResource(R.string.popularity_bottom_sheet_description,popularityData?.dears?:0.toString()),
                 textAlign = TextAlign.Center,
                 color = colorResource(R.color.textColorPrimary),
                 style = FlashatComposeTypography.defaultType.body1,
                 modifier = Modifier.fillMaxWidth(),
                 fontWeight = FontWeight.Bold)
        }
        val tribeItems = listOf(
            PopularityItem(context.getString(R.string.popularity_citizens_count,popularityData?.citizens?:0.toString()), context.getString(R.string.popularity_50_point) ),
            PopularityItem(context.getString(R.string.popularity_officers_count,popularityData?.officers?:0.toString()), context.getString(R.string.popularity_100_point)),
            PopularityItem(context.getString(R.string.popularity_ambassadors_count,popularityData?.ambassadors?:0.toString()), context.getString(R.string.popularity_150_point)),
            PopularityItem(context.getString(R.string.popularity_ministers_count,popularityData?.ministers?:0.toString()), context.getString(R.string.popularity_250_point)),
            )
        items(tribeItems) { item ->
            PopularityItemRow(item = item)
        }
        item {
            TotalPointCard(popularityData = popularityData)
        }
        item {
            DashedDivider()
        }
        item {
            Text(text = stringResource(R.string.popularity_fan_count,popularityData?.fans.toString()), textAlign = TextAlign.Center, modifier = Modifier.fillMaxWidth(), fontWeight = FontWeight.Bold,color = colorResource(R.color.textColorPrimary))
        }

        val fanItems = listOf(
            PopularityItem(context.getString(R.string.popularity_fans_count,popularityData?.activeFans?:0.toString()), context.getString(R.string.popularity_25_point)),
        )
        items(fanItems) { item ->
            PopularityItemRow(item = item)
        }
        item {
            DashedDivider()
        }

        item {
            Text(text = stringResource(R.string.popularity_maidan_win_count,popularityData?.maidanWins?:0.toString()), textAlign = TextAlign.Center, modifier = Modifier.fillMaxWidth(), fontWeight = FontWeight.Bold, color = colorResource(R.color.textColorPrimary))
        }
        item{
            NotesSection()
        }
    }
}

@Composable
fun PopularityItemRow(item: PopularityItem, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.element_spacing))
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_business_tick),
                tint = colorResource(R.color.chatMessageTickColorPurple),
                contentDescription = "Check Icon",
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
            )
            Divider(
                color = colorResource(R.color.colorPrimary),
                modifier = Modifier
                    .height(48.dp)
                    .padding(vertical = 4.dp)
                    .size(1.dp)

            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = colorResource(R.color.textInputBackground), shape = RoundedCornerShape(8.dp))
                .padding(all = dimensionResource(R.dimen.activity_margin)),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(text = item.description, style = MaterialTheme.typography.body2,color = colorResource(R.color.textColorPrimary))
            Text(text = item.points, style = MaterialTheme.typography.body2,color = colorResource(R.color.textColorPrimary))
        }
    }
}

@Composable
fun DashedDivider(modifier: Modifier = Modifier) {
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = dimensionResource(R.dimen.activity_margin), vertical = dimensionResource(R.dimen.element_spacing))
            .height(1.dp)
    ) {
        val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
        drawLine(
            color = Color.Gray,
            start = Offset(0f, 0f),
            end = Offset(size.width, 0f),
            pathEffect = pathEffect
        )
    }
}

@Composable
fun TotalPointCard(modifier: Modifier = Modifier,popularityData: UserStatsResult?){
        Text(
            text = stringResource(R.string.popularity_tribe_score,popularityData?.tribeScore.toString()),
            modifier = modifier
                .padding(horizontal = dimensionResource(R.dimen.activity_margin), vertical = dimensionResource(R.dimen.element_spacing))
                .border(
                    width = 1.dp, color = colorResource(R.color.colorPrimary), shape = RoundedCornerShape(4.dp)
                )
                .background(color = colorResource(R.color.textInputBackground))
                .padding(all = dimensionResource(R.dimen.activity_margin))
                .fillMaxWidth(),
            fontWeight = FontWeight.Bold,
            style = MaterialTheme.typography.body1,
            color = colorResource(R.color.colorPrimary),
            )
    }

@Composable
fun NotesSection(modifier: Modifier = Modifier){
    Column(modifier = modifier
        .fillMaxWidth()
        .padding(dimensionResource(R.dimen.activity_margin))
    ) {
        Text(
            text = stringResource(R.string.popularity_sub_title),
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp),
            color = colorResource(R.color.textColorSecondary),
        )
        Text(text = stringResource(R.string.popularity_point_detail), fontSize = 12.sp, color = colorResource(R.color.textColorPrimary))
    }
}

@Preview(showBackground = true)
@Composable
fun PopularityCalculationsScreenPreview() {
    PopularityCalculationsScreen(popularityData = null)
}

