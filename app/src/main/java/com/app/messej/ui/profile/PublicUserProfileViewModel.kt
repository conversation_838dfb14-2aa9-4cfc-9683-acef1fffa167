package com.app.messej.ui.profile

import android.app.Application
import android.util.Log
import androidx.annotation.DrawableRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.huddles.PrivateChatUserInfo
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.enums.EditableFieldMode
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PublicUserProfileViewModel(application: Application) : AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(application)
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val huddleRepo = HuddlesRepository(application)
    private val chatEventRepo = PrivateChatEventRepository
    private val accountRepository = AccountRepository(application)

    val user = accountRepository.user

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val userId = MutableLiveData<Int?>(null)
    val userIdLiveData: LiveData<Int?> = userId

    val nickNameInput = MutableLiveData("")

    private var profileContext: UserProfileContext = UserProfileContext.GENERAL

    val accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    @DrawableRes
    var countryFlag: Int? = null

    fun setUserId(id: Int, context: UserProfileContext, isPopupButtonVisible: Boolean= true) {
        profileContext = context
        userId.value = id
        getUserProfile(id)
        if (context == UserProfileContext.PRIVATE_CHAT) {
            getChatInfo(id)
        }
        setPopupButtonVisibility(isVisible = isPopupButtonVisible)
    }


    private val _profile = userId.switchMap {
        it ?: return@switchMap MutableLiveData<OtherUser?>(null)
        profileRepo.getLocalOtherUserProfile(it)
    }
    val profile: LiveData<OtherUser?> = _profile

    val isCurrentUser = userId.map { it == user.id }

    val isPopupButtonVisible = MutableLiveData<Boolean>()

    val currentUserProfile: LiveData<CurrentUser.Profile?> = accountRepo.userProfileFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val aboutUser: LiveData<String?> = MediatorLiveData<String?>().apply {
        fun updateAboutUser() {
            value = if (isCurrentUser.value == true) {
                currentUserProfile.value?.about ?: accountDetails.value?.about
            } else {
                _profile.value?.about
            }
        }
        addSource(isCurrentUser) { updateAboutUser() }
        addSource(currentUserProfile) { updateAboutUser() }
        addSource(accountDetails) { updateAboutUser() }
        addSource(_profile) { updateAboutUser() }
    }

    val profileCitizenship=profile.map {
       it?: return@map null
         it.citizenship
    }.distinctUntilChanged()

    private fun setPopupButtonVisibility(isVisible: Boolean) {
        isPopupButtonVisible.value = isVisible
    }

    private fun getUserProfile(id: Int) {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = profileRepo.getPublicUserDetails(id)) {
                is ResultOf.Success -> {
                    _dataLoading.postValue(false)
                }

                is ResultOf.APIError -> {
                    _dataLoading.postValue(false)
                }

                is ResultOf.Error -> {
                    _dataLoading.postValue(false)
                }
            }
        }
    }

    val showFollowButton = _profile.map {
        it?.isSuperstar == false
    }

    val followed = _profile.map {
        it?.isStar == true
    }

    val userNickName: LiveData<NickName?> = userId.switchMap { userId ->
        profileRepo.getNickNamesLiveData(true, viewModelScope).map {
            return@map it.find { user -> user.userId == userId }
        }
    }

    val nickNameOrName: MediatorLiveData<String?> by lazy {
        val med = MediatorLiveData<String?>()
        fun update() {
            med.postValue(userNickName.value?.nickName ?: profile.value?.name)
        }
        med.addSource(_profile) { update() }
        med.addSource(userNickName) { update() }
        med
    }

    val hasNickName = userNickName.map {
        it?.nickName != null
    }

    var onNickNameChanged = LiveEvent<Boolean>()

    val nickNameDisplayMode = MutableLiveData(EditableFieldMode.ENTERED)

    fun showNickNameEdit() {
        nickNameInput.postValue(userNickName.value?.nickName.orEmpty())
        nickNameDisplayMode.postValue(EditableFieldMode.EDIT)
    }

    fun cancelNickNameEdit() {
        nickNameInput.postValue("")
        updateNickNameFieldMode()
    }

    private fun updateNickNameFieldMode() {
        userNickName.value.let {
            it?.let {
                nickNameDisplayMode.postValue(EditableFieldMode.ENTERED)
            } ?: kotlin.run {
                nickNameDisplayMode.postValue(EditableFieldMode.NOT_ENTERED)
            }
        }
    }
    var _countryList = MutableLiveData<Map<String,Int>>()

    init {
        viewModelScope.launch {
            userNickName.asFlow().collect {
                nickNameInput.postValue(it?.nickName)
                updateNickNameFieldMode()
            }
        }
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    fun getFlag(map: Map<String, Int>,flag:String?) {
        val flags = map
        Log.d("COUNTRYFLAG","countryFlag1: $flag")
        countryFlag = if(isCurrentUser.value==true)
        flags.get(user.countryCodeIso.toString()) else
            flags.get(flag)
    }
    
    val nicknameSaving = MutableLiveData<Boolean>(false)

    fun updateUserNickName() {
        viewModelScope.launch(Dispatchers.IO) {
            val id = userId.value ?: return@launch
            when (profileRepo.updateUserNickName(id, nickNameInput.value ?: "")) {
                is ResultOf.Success -> {
                    delay(500)
                    cancelNickNameEdit()
                    onNickNameChanged.postValue(true)
                }

                is ResultOf.APIError -> {
                    cancelNickNameEdit()
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    val onFollowedUser = LiveEvent<OtherUser>()

    val onUnfollowedUser = LiveEvent<OtherUser>()

    private val _followActionLoading = MutableLiveData<Boolean>(false)
    val followActionLoading: LiveData<Boolean> = _followActionLoading

    fun toggleFollow() {
        val user = _profile.value ?: return
        if (user.isStar) unFollowUser() else followUser()
    }

    private fun followUser() {
        viewModelScope.launch(Dispatchers.IO) {
            val user = _profile.value ?: return@launch
            when (profileRepo.followUser(user)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(
                        user.copy(
                            isStar = true
                        )
                    )
                    onFollowedUser.postValue(user)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    private fun unFollowUser() {
        viewModelScope.launch(Dispatchers.IO) {
            val user = _profile.value ?: return@launch
            when (profileRepo.unFollowStar(user.id)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(
                        user.copy(
                            isStar = false
                        )
                    )
                    onUnfollowedUser.postValue(user)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    val onToggleLikerPrivacy = LiveEvent<String?>()

    private val _togglePrivacyActionLoading = MutableLiveData<Boolean>(false)
    val togglePrivacyActionLoading: LiveData<Boolean> = _togglePrivacyActionLoading

    fun hideLiker(hide: Boolean) {
        _togglePrivacyActionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val user = _profile.value ?: return@launch
            when (val result: ResultOf<String> = profileRepo.hideLiker(hide, user.id)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(
                        user.copy(
                            likersPrivacy = hide
                        )
                    )
                    onToggleLikerPrivacy.postValue(result.value)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
            _togglePrivacyActionLoading.postValue(false)
        }
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String, Int>>()

    fun navigateToPrivateMessage() {
        viewModelScope.launch {
            val id = userId.value ?: return@launch
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(id)
            onNavigateToPrivateMessage.postValue(Pair(roomId, id))
        }
    }

    // Private chat actions

    private val _chatInfo = MutableLiveData<PrivateChatUserInfo?>(null)
    val chatInfo: LiveData<PrivateChatUserInfo?> = _chatInfo

    private val _chatInfoLoading = MutableLiveData<Boolean>(false)
    val chatInfoLoading: LiveData<Boolean> = _chatInfoLoading

    private fun getChatInfo(id: Int) {
        _chatInfoLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PrivateChatUserInfo> = huddleRepo.getPrivateChatReceiverInfo(id)) {
                is ResultOf.Success -> {
                    _chatInfo.postValue(result.value)
                }

                else -> {}
            }
            _chatInfoLoading.postValue(false)
        }
    }

    enum class PrivateChatStatus { ACTIVE, BLOCKED, RESTRICTED }

    val chatStatus = _chatInfo.map {
        it ?: return@map null
        return@map if (it.chatBlocked == true) PrivateChatStatus.BLOCKED
        else if (it.chatType == PrivateChat.ChatType.REQUEST) PrivateChatStatus.RESTRICTED
        else PrivateChatStatus.ACTIVE
    }

    val onUserRestricted = LiveEvent<Boolean>()

    val onUserBlocked = LiveEvent<Boolean>()

    private suspend fun getRoomId(): String? {
        val id = userId.value ?: return null
        return HuddlesRepository(getApplication()).getPrivateChatRoomId(id)
    }

    fun restrictUser() {
        _chatInfoLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val id = userId.value ?: return@launch
            val roomId = getRoomId() ?: return@launch
            when (val result: ResultOf<String> = profileRepo.restrictUser(roomId, id)) {
                is ResultOf.Success -> {
                    _chatInfo.value?.let {
                        _chatInfo.postValue(
                            it.copy(
                                chatType = PrivateChat.ChatType.REQUEST
                            )
                        )
                    }
                    onUserRestricted.postValue(true)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
            _chatInfoLoading.postValue(false)
        }
    }

    fun blockChat() {
        _chatInfoLoading.postValue(true)
        viewModelScope.launch {
            val id = userId.value ?: return@launch
            val room = getRoomId() ?: return@launch
            val sent = chatEventRepo.toggleChatBlock(id)
            if (sent) {
                _chatInfo.value?.let {
                    _chatInfo.postValue(
                        it.copy(
                            chatBlocked = true
                        )
                    )
                }
                huddleRepo.deletePrivateChat(room)
                getUserProfile(id)
                onUserBlocked.postValue(true)
            }
            _chatInfoLoading.postValue(false)
        }
    }

    fun unblockChat() {
        _chatInfoLoading.postValue(true)
        viewModelScope.launch {
            val id = userId.value ?: return@launch
            val room = getRoomId() ?: return@launch
            val sent = chatEventRepo.toggleChatBlock(id)
            if (sent) {
                _chatInfo.value?.let {
                    _chatInfo.postValue(
                        it.copy(
                            chatBlocked = false
                        )
                    )
                }
                getUserProfile(id)
                onUserBlocked.postValue(false)
            }
            _chatInfoLoading.postValue(false)
        }
    }

    val onTempBlockUser = LiveEvent<Boolean>()

    val onTempUnBlockUser = LiveEvent<Boolean>()
    fun blockUserTemporarily() {
        viewModelScope.launch {
            val id = userId.value ?: return@launch
            if (profile.value?.blockedByLeader == false) {
                when (profileRepo.blockUnblockUserTemporarily(id, true)) {
                    is ResultOf.Success -> {
                        onTempBlockUser.postValue(true)
                    }

                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }
                }
            } else {
                when (profileRepo.blockUnblockUserTemporarily(id, false)) {
                    is ResultOf.Success -> {
                        onTempUnBlockUser.postValue(true)
                    }

                    is ResultOf.APIError -> {
                    }

                    is ResultOf.Error -> {
                    }
                }
            }

        }
    }

}