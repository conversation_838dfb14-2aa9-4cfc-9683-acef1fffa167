package com.app.messej.ui.home.businesstab

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.app.messej.BuildConfig
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.SubscriptionProfile
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.utils.AuthUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentBuyFlaxBinding
import com.app.messej.databinding.ItemBuyFlaxSuccessBinding
import com.app.messej.databinding.LayoutPurchaseRetryBinding
import com.app.messej.ui.home.businesstab.BuyCoinsViewModel.FlaxRatesAndProductDetails
import com.app.messej.ui.home.businesstab.adapter.BusinessBuyFlaxRatesAdapter
import com.app.messej.ui.home.gift.GiftCommonViewModel
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BuyCoinsFragment() : Fragment(), BusinessBuyFlaxRatesAdapter.ActionListener {

    private lateinit var binding: FragmentBuyFlaxBinding
    private val viewModel: BuyCoinsViewModel by viewModels()
    private val giftCommonViewModel: GiftCommonViewModel by activityViewModels()
    private val args: BuyCoinsFragmentArgs by navArgs()
    // Declare action as a global property (nullable)
    private var action: Pair<Boolean, Boolean?>? = null
    private var apiLoader : MaterialDialog? = null
    private var isSuccess: Boolean = false

    /**IN APP PAYMENT */
    private lateinit var billingClient: BillingClient

    private var processInProgress = false
    private lateinit var adapter : BusinessBuyFlaxRatesAdapter


    companion object {
        const val IN_APP_KEY = "buyflax"

        const val ARG_BUY_COIN = "buy_coin"
        const val ARG_PURCHASE_ITEM ="purchase_item"

        fun getActionBundle(action: Boolean,isBuyCoin: Boolean) = Bundle().apply {
            putBoolean(ARG_BUY_COIN, action)
            putBoolean(ARG_PURCHASE_ITEM, isBuyCoin)
        }

        fun parseActionBundle(bundle: Bundle?): Pair<Boolean, Boolean?>  {
            val action = bundle?.getBoolean(ARG_BUY_COIN) ?: false
            val purchaseItem = bundle?.getBoolean(ARG_PURCHASE_ITEM)
            return Pair(action, purchaseItem)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_buy_flax, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                binding.customActionBar.toolBarTitle.text = getString(R.string.transactions)
                bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
            }
        }
         binding.customActionBar.toolBarTitle.text = if(args.isBuyCoin) getString(R.string.title_buy_coins) else getString(R.string.title_buy_flix)
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                if(giftCommonViewModel.processWait.value == true){
                    showToast(R.string.business_process_not_complete)
                    return}
                else findNavController().popBackStack()
            }
        })
        binding.customActionBar.toolbar.apply {
            setNavigationOnClickListener {
                if(giftCommonViewModel.processWait.value == true){
                    showToast(R.string.business_process_not_complete)
                    return@setNavigationOnClickListener}
                else findNavController().popBackStack()
          }
        }
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        action = parseActionBundle(arguments)
        if(action!!.first){
            binding.customActionBar.appbar.visibility=View.GONE
            viewModel.setPurchaseType(if (action!!.second==true) PurchaseItem.BUY_COIN else PurchaseItem.BUY_FLIX)

        }else{
            binding.customActionBar.appbar.visibility=View.VISIBLE
             viewModel.setPurchaseType(if (args.isBuyCoin) PurchaseItem.BUY_COIN else PurchaseItem.BUY_FLIX)
        }
        setUp()
        observe()
    }

    private fun setUp() {
        /**OLD*/
        binding.textPurchaseFlaxHistory.setOnClickListener {

            viewModel.purchaseItem.value?.let {
                if (action!!.first) {
                    if (action!!.second == true) findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlaxPurchaseHistoryFragment(it)) else findNavController().navigate(
                        NavGraphHomeDirections.actionGlobalFlixPurchaseHistoryFragment(
                            it
                        )
                    )

                } else {
                    if (args.isBuyCoin) findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlaxPurchaseHistoryFragment(it)) else findNavController().navigate(
                        NavGraphHomeDirections.actionGlobalFlixPurchaseHistoryFragment(
                            it
                        )
                    )
                }
            }
        }
        /**NEW*/
        billingClient = BillingClient.newBuilder(requireContext()).setListener(purchasesUpdatedListener).enablePendingPurchases().build()

        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                    if(action!!.first) {
                        if (action!!.second == true) viewModel.getDealsBuyCoinRates() else viewModel.getDealsBuyFlixRates()
                    }else{
                        if (args.isBuyCoin) viewModel.getDealsBuyCoinRates() else viewModel.getDealsBuyFlixRates()
                    }
                }else{
                    Log.e("BillingSetup", "Billing setup failed with response code: ${billingResult.responseCode}")
                }
            }

            override fun onBillingServiceDisconnected() {
                // Try to restart the connection on the next request to
                // Google Play by calling the startConnection() method.
                Log.w("BillingSetup", "Billing service disconnected. Attempting to reconnect...")
                billingClient.startConnection(this)
            }
        })

        binding.banner.getButton.setOnClickListener {
            buyCoinsFromWeb()
        }
        giftCommonViewModel.getGiftList()
    }

    private fun buyCoinsFromWeb() {

        val purchaseType=if(action!!.first) {
            if (action!!.second == true) PurchaseItem.BUY_COIN.toString() else PurchaseItem.BUY_FLIX.toString()
        }else{
            if (args.isBuyCoin) PurchaseItem.BUY_COIN.toString() else PurchaseItem.BUY_FLIX.toString()
        }

        val url = "${BuildConfig.BASE_URL_BUY_COINS_FROM_WEB}/verify-user?access_token=${viewModel.accessTokens}&packageType=${purchaseType}"
        Log.d("URLBUYY", ""+url)
        startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
    }

    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            when (billingResult.responseCode) {

                BillingClient.BillingResponseCode.OK -> {
                    for (purchase in purchases!!) {
                        viewModel.setLoading(true)
                        handlePurchase(purchase)
                    }
                }

                BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED->{
                    /*isSuccess=true*/
                    viewModel.setLoading(false)
                    giftCommonViewModel.setRestricted(false)
                    processInProgress=false
                }
                BillingClient.BillingResponseCode.USER_CANCELED -> {
                    viewModel.setLoading(false)
                    giftCommonViewModel.setRestricted(false)
                    processInProgress=false
                }
                else -> {
                    Log.d("BillingError", "Response Code: "+billingResult.responseCode+" Debug Message: "+billingResult.debugMessage)
                    Toast.makeText(requireContext(),"Response Code: "+billingResult.responseCode+"  Debug Message: "+billingResult.debugMessage,Toast.LENGTH_SHORT).show()
                    // Handle any other error codes.
                    viewModel.setLoading(false)
                    giftCommonViewModel.setRestricted(false)
                    processInProgress=false
                }
            }
        }

//    private fun getProductDetails() {
//        viewModel.dealsFlaxPurchaseList.value?.let {
//        }
//    }
    private fun observe() {
    viewModel.isLoading.observe(viewLifecycleOwner) {
        Log.d("qaz", "observe "+it)
        if (it) showAPILoader() else hideAPILoader()
    }

        viewModel.dealsFlaxPurchaseList.observe(viewLifecycleOwner){ list->
            if(list.isNullOrEmpty())return@observe
            val productIds: List<String> = list.map { productId->
                productId.androidProductId.toString() }
            Log.d("productIds", ""+list.toString())

            val queryProductDetailsParams = productIds.let {
                QueryProductDetailsParams.newBuilder().setProductList(productIds.map {
                    QueryProductDetailsParams.Product.newBuilder().setProductId(it).setProductType(BillingClient.ProductType.INAPP).build()
                }).build()
            }

            billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->
                viewModel.setProductDetails(productDetailsList)
            }
        }

        viewModel.productDetailsList.observe(viewLifecycleOwner) {
            if (it.isNotEmpty()) {
                it?.map { data->
                    if(data.flaxName!=null){
                        adapter = BusinessBuyFlaxRatesAdapter(
                            buyFlaxRates = it,
                            listener = this,
                            selectedItem = viewModel.currentlySelectedItem.value,
                            isBuyCoinVisible = if(action!!.first) {
                                action?.second == true } else { args.isBuyCoin }
                        )
                        binding.buyFlaxList.adapter = adapter
                    }
                }
            }
        }

    binding.btnRecharge.setOnClickListener {
        viewModel.currentlySelectedItem.value?.let {
            startPaymetProcess(item = it)
        }
    }

    giftCommonViewModel.giftList.observe(viewLifecycleOwner) {
        viewModel.setGiftResponse(giftResponse = it)
    }

    viewModel.currentProductSelected.observe(viewLifecycleOwner){
       Log.d("PurchaseDATAAA","data Selected ${it}")
    }

        viewModel.errorMessage.observe(viewLifecycleOwner){
            if(it.isNotEmpty()) {
                giftCommonViewModel.setRestricted(false)
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.successMessage.observe(viewLifecycleOwner) { it ->
            if (it != null) {
                if (it.isNotEmpty()) {
                    showSuccessMessage(it)
                    viewModel.setLoader(false)
                    giftCommonViewModel.setRestricted(false)
                }
            }
            
            viewModel.maxRetryReached.observe(viewLifecycleOwner){ it ->
                if(it){
                    giftCommonViewModel.setRestricted(it)

                }
            }
        }

        viewModel.hasLocalData.observe(viewLifecycleOwner) {
            Log.d("PurchaseDATAAA","data in local Selected ${it}")
            if (it) {
                popUpLocalData()
            }
        }
        viewModel.processInProgress.observe(viewLifecycleOwner) {
            processInProgress = it
        }
        viewModel.retryBuyFlax.observe(viewLifecycleOwner) {
            if (it) {
                popUpLocalData()
            }
        }

        viewModel.maxRetryReached.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.buy_flax_max_retry), Toast.LENGTH_SHORT).show()
            }
        }
    viewModel.purchaseItem.observe(viewLifecycleOwner){ purchaseType ->
        Log.w("PurchaseType",""+purchaseType)
    }

    viewModel.calculatedNewValues.observe(viewLifecycleOwner) {
        setRechargeCoinAndFlixCardViewTitle(
            isCoinView = if(action!!.first) { action?.second == true } else { args.isBuyCoin },
            calculatedValues = it
        )
    }

    }

    private fun setRechargeCoinAndFlixCardViewTitle(isCoinView: Boolean, calculatedValues: BuyCoinsViewModel.CalculatedCoinAndFlixValues?) {
        val coinAmountInHundreds = calculatedValues?.coinAmountInHundreds
        val calculatedNewCoinAmount = calculatedValues?.calculatedCoinAmount
        val calculatedNewFlixAmount = calculatedValues?.calculatedFlixAmount
        val flixBalance = calculatedValues?.currentFlixAmount
        val coinBalance = calculatedValues?.currentCoinAmount

        binding.apply {
            linearLayoutBuyFlixCoinTitle.visibility = customActionBar.appbar.visibility
            textFlixBalance.text = resources.getString(R.string.flix_balance, flixBalance.formatDecimalWithRemoveTrailingZeros())
            textCoinBalance.text = resources.getString(R.string.coins_balance, coinBalance.formatDecimalWithRemoveTrailingZeros())
            if (isCoinView) {
                textExchangeFlixCoin.text = resources.getString(
                    R.string.exchange_your_flix_to_coins,
                    flixBalance.formatDecimalWithRemoveTrailingZeros(),
                    calculatedNewCoinAmount.formatDecimalWithRemoveTrailingZeros()
                )
                textExchangeFlixCoin.setTextColor(
                    ContextCompat.getColor(requireContext(), if ((flixBalance ?: 0.0) == 0.0) R.color.textColorAlwaysDarkHint else R.color.colorPrimary)
                )
                imageViewArrowRight.setColorFilter(
                    ContextCompat.getColor(requireContext(), if ((flixBalance ?: 0.0) == 0.0) R.color.textColorAlwaysDarkHint else R.color.colorPrimary)
                )
                linearLayoutExchangeFlixToCoins.setOnClickListener {
                    flixBalance?.takeIf { it != 0.0 }?.let {
                        findNavController().navigateSafe(
                            BuyCoinsFragmentDirections.actionGlobalConvertCoinToFlaxFragment(
                                convertType = GiftConversion.FLAX_TO_COIN,
                                availableFlix = it.formatDecimalWithRemoveTrailingZeros()
                            )
                        )
                    }
                }
            } else {
                textExchangeFlixCoin.text = resources.getString(
                    R.string.exchange_your_coins_to_flix,
                    coinAmountInHundreds.formatDecimalWithRemoveTrailingZeros(),
                    calculatedNewFlixAmount.formatDecimalWithRemoveTrailingZeros()
                )
                textExchangeFlixCoin.setTextColor(
                    ContextCompat.getColor(requireContext(), if ((coinAmountInHundreds ?: 0.0) == 0.0) R.color.textColorAlwaysDarkHint else R.color.colorPrimary)
                )
                imageViewArrowRight.setColorFilter(
                    ContextCompat.getColor(requireContext(), if ((coinAmountInHundreds ?: 0.0) == 0.0) R.color.textColorAlwaysDarkHint else R.color.colorPrimary)
                )
                linearLayoutExchangeFlixToCoins.setOnClickListener {
                    coinAmountInHundreds?.takeIf { it != 0.0 }?.let {
                        findNavController().navigateSafe(
                            BuyCoinsFragmentDirections.actionGlobalConvertCoinToFlaxFragment(
                                convertType = GiftConversion.COIN_TO_FLAX,
                                availableCoins = it.formatDecimalWithRemoveTrailingZeros()
                            )
                        )
                    }
                }
            }
        }
    }

    override fun InAppClickListener(item: BuyCoinsViewModel.FlaxRatesAndProductDetails) {
        Log.d("PurchaseDATAAA","data class Selected ${item}")

//        viewModel.setCurrentProduct(item)
        Log.d("PurchaseDATAAA","processInProgress ${processInProgress}")
        if (processInProgress) return
        giftCommonViewModel.setRestricted(true)
            val productDetailsParamsList = listOf(
                BillingFlowParams.ProductDetailsParams.newBuilder().setProductDetails(item.productDetail).build()
            )
            val user = viewModel.user

            val userJsonEncoded = AuthUtil.encodeToBase64(
                Gson().toJson(
                    SubscriptionProfile(
                        userId = user.id, timezone = DateTimeUtils.getTimeZoneFromEpochTime(System.currentTimeMillis()), paymentFor = 0
                    )
                )
            )
            val billingFlowParams =
                BillingFlowParams.newBuilder().setProductDetailsParamsList(productDetailsParamsList).setObfuscatedAccountId(BuildConfig.BUILD_TYPE).setObfuscatedProfileId(userJsonEncoded).build()

//            viewModel.setSelectedProductDetails(item.productDetails!!)
            item.productDetail.oneTimePurchaseOfferDetails.let {
                it?.let { it1 ->
                    if (processInProgress) return
                    else {
                        Log.d("PurchaseDATAAA", " data oneTimePurchaseOfferDetails ${it1}")
                        viewModel.setPricingPhase(it1)
                    }
                }
            }
            viewModel.setIsProcessInProgress(true)

            billingClient.launchBillingFlow(requireActivity(), billingFlowParams)
        }

    override fun itemClickListener(item : FlaxRatesAndProductDetails, position: Int) {
        viewModel.setCurrentSelectedItem(selectedItem = item)
        adapter.updateSelectedItem(newSelectedItem = item, position = position)
    }

    private fun startPaymetProcess(item: FlaxRatesAndProductDetails) {
        Log.d("PurchaseDATAAA","data class Selected ${item}")
//        viewModel.setCurrentProduct(item)
        Log.d("PurchaseDATAAA","processInProgress ${processInProgress}")
        if (processInProgress) return
        giftCommonViewModel.setRestricted(true)
        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder().setProductDetails(item.productDetail).build()
        )
        val user = viewModel.user
        val userJsonEncoded = AuthUtil.encodeToBase64(
            Gson().toJson(
                SubscriptionProfile(
                    userId = user.id, timezone = DateTimeUtils.getTimeZoneFromEpochTime(System.currentTimeMillis()), paymentFor = 0
                )
            )
        )
        val billingFlowParams =
            BillingFlowParams.newBuilder().setProductDetailsParamsList(productDetailsParamsList).setObfuscatedAccountId(BuildConfig.BUILD_TYPE).setObfuscatedProfileId(userJsonEncoded).build()
//            viewModel.setSelectedProductDetails(item.productDetails!!)
        item.productDetail.oneTimePurchaseOfferDetails.let {
            it?.let { it1 ->
                if (processInProgress) return
                else {
                    Log.d("PurchaseDATAAA", " data oneTimePurchaseOfferDetails ${it1}")
                    viewModel.setPricingPhase(it1)
                }
            }
        }
        viewModel.setIsProcessInProgress(true)
        billingClient.launchBillingFlow(requireActivity(), billingFlowParams)
    }

    private  fun  handlePurchase(purchase: Purchase) {
        val purchaseToken = purchase.purchaseToken
        val consumeParams = ConsumeParams.newBuilder().setPurchaseToken(purchaseToken).build()

        val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchaseToken).build()

        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            billingClient.acknowledgePurchase(acknowledgePurchaseParams) {
                if (it.responseCode == BillingClient.BillingResponseCode.OK) {
                    billingClient.consumeAsync(consumeParams) { billingResult, outToken ->
                        lifecycleScope.launch {
                            withContext(Dispatchers.IO) {
                                if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                                    // Consume successful, handle accordingly
                                    viewModel.setPurchaseData(purchase)

                                        if (viewModel.selectedProduct.value != null) {
                                            viewModel.setLoader(false)
                                        }
                                    viewModel.setIsProcessInProgress(true)
                                } else {
                                    giftCommonViewModel.setRestricted(false)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        billingClient.endConnection()
    }

    private fun showSuccessMessage(flaxName: String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<ItemBuyFlaxSuccessBinding>(layoutInflater, R.layout.item_buy_flax_success, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            cancelOnTouchOutside
            view.textFlaxAmount.text =if(viewModel.purchaseItem.value==PurchaseItem.BUY_COIN) getString(R.string.common_coins, flaxName) else getString(R.string.gift_flax_1, flaxName)


        }
    }
    private fun popUpLocalData() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPurchaseRetryBinding>(layoutInflater, R.layout.layout_purchase_retry, null, false)
            view.viewModel = viewModel
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)

            val messageSpan = resources.getString(R.string.buy_flax_retry).highlightOccurrences(getString(R.string.common_retry)) {
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorPrimary))
            }
            view.nickNameTitle.text = messageSpan
            view.actionConfirm.setOnClickListener {
                viewModel.loadPendingRequestToServer()
                dismiss()
            }
            viewModel.hasLocalData.observe(viewLifecycleOwner) {
                if (!it) {
                    dismiss()
                }
            }
        }
    }

    private fun showAPILoader() {

        apiLoader = showLoader(message = getString(R.string.buy_coin_confirmation_header), content = getString(R.string.buy_coin_user_action_confirmation))
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

}