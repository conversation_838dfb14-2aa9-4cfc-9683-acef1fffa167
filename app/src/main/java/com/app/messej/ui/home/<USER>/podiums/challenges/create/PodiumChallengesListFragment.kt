package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPodiumChallengesListBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumChallengesListFragment : Fragment() {

    private lateinit var binding: FragmentPodiumChallengesListBinding

    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_challenges_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()

        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = getString(R.string.podium_challenges_list_title)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        activity?.actionBar?.setDisplayHomeAsUpEnabled(true)
        activity?.actionBar?.setHomeButtonEnabled(true)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        binding.giftChallenge.visibility = if(viewModel.isGoldenUser) View.GONE else View.VISIBLE

        binding.likeChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.LIKES)
        }
        binding.giftChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.GIFTS)
        }
        binding.flagChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.FLAGS)
        }
        binding.confourChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.CONFOUR)
        }
        binding.penaltyChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.PENALTY)
        }

        binding.boxChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.BOXES)
        }

        binding.knowledgeRaceChallengeLayout.setOnClickListener {
            showChallengeFacilitatorListFragment(ChallengeType.KNOWLEDGE)
        }

        binding.btnDetailLike.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_LIKE_DETAILS)
        }
        binding.btnDetailGift.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_GIFT_DETAILS)
        }
        binding.btnDetailFlag.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_FLAG_DETAILS)
        }
        binding.btnDetailConfour.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_CONFOUR_DETAILS)
        }
        binding.btnDetailPenalty.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_PENALTY_KICK_DETAILS)
        }
        binding.btnDetailBox.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_BOX_DETAILS)
        }
        binding.btnDetailKnowledgeRace.setOnClickListener {
            loadPodiumChallengeDetailsBottomSheetFragment(DocumentType.PODIUM_CHALLENGE_KNOWLEDGE_RACE_DETAILS)
        }
    }

    private fun loadPodiumChallengeDetailsBottomSheetFragment(documentType: DocumentType) {
        val action = PodiumChallengesListFragmentDirections.actionCreateChallengeLoadingFragmentToPodiumChallengeDetailsBottomSheetFragment(documentType)
        findNavController().navigateSafe(action)
    }

    private fun showChallengeFacilitatorListFragment(type: ChallengeType) {
        viewModel.setChallengeType(type)
        val action = PodiumChallengesListFragmentDirections.actionPodiumChallengeListFragmentToChallengeFacilitatorListFragment()
        findNavController().navigateSafe(action)

    }

}