package com.app.messej.ui.utils

import android.util.Log
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.LayoutSettingsBottomSheetHeaderFreeBinding
import com.app.messej.databinding.LayoutSettingsBottomSheetHeaderPremiumBinding
import com.app.messej.ui.utils.EnumUtils.displayText

sealed class DrawerHeaderHolder {
    abstract val binding: ViewBinding
    abstract var profile: CurrentUser.Profile?
    abstract var account: AccountDetailsResponse?

    class FreeHeader(override val binding: LayoutSettingsBottomSheetHeaderFreeBinding, private val listener: ClickListener, private val citizenship: UserCitizenship?): DrawerHeaderHolder() {

        interface ClickListener {
            fun toProfile()
            fun toUpgrade()
            fun dismissDialog()
            fun toFollowers()
            fun toFollowing()
            fun toFlaxBalance()
//            fun toDears()
//            fun toFans()
//            fun toLikers()
//            fun toStars()
        }

        init {
            binding.profileHolder.setOnClickListener {
                listener.toProfile()
            }
            binding.upgradeButton.setOnClickListener {
                listener.toUpgrade()
            }
            binding.closeButton.setOnClickListener {
                listener.dismissDialog()
            }
            binding.followersButton.setOnClickListener {
                listener.toFollowers()
            }
            binding.followingButton.setOnClickListener {
                listener.toFollowing()
            }
            binding.layoutFlixBalance.cardView.setOnClickListener {
                listener.toFlaxBalance()
            }

            citizenship?.setSettingsWheelCitizenshipTextAndBackground(textView = binding.textCitizenship)

//            binding.headerDearsHolder.setOnClickListener {
//                listener.toDears()
//            }
//            binding.headerFansHolder.setOnClickListener {
//                listener.toFans()
//            }
//            binding.headerLikersHolder.setOnClickListener {
//                listener.toLikers()
//            }
//            binding.headerStarsHolder.setOnClickListener {
//                listener.toStars()
//            }
        }

        override var profile: CurrentUser.Profile?
            get() = binding.profile
            set(value) {
                binding.profile = value
            }

        override var account: AccountDetailsResponse?
            get() = binding.account
            set(value) {
                binding.account = value
            }
    }

    class PremiumHeader(override val binding: LayoutSettingsBottomSheetHeaderPremiumBinding, private val listener: ClickListener, private val citizenship: UserCitizenship?): DrawerHeaderHolder() {


        interface ClickListener {
            fun toMyETribe()
            fun toProfile()
            fun dismissDialog()
            fun toFollowers()
            fun toFollowing()
            fun toStatements()
        }

        init {
            binding.eTribeButton.setOnClickListener {
                listener.toMyETribe()
            }
            binding.profileHolder.setOnClickListener {
                listener.toProfile()
            }
            binding.closeButton.setOnClickListener {
                listener.dismissDialog()
            }
            binding.followersButton.setOnClickListener {
                listener.toFollowers()
            }
            binding.followingButton.setOnClickListener {
                listener.toFollowing()
            }
            binding.layoutFlixBalance.cardView.setOnClickListener {
                listener.toStatements()
            }
            citizenship?.setSettingsWheelCitizenshipTextAndBackground(textView = binding.textCitizenship)
        }

        override var profile: CurrentUser.Profile?
            get() = binding.profile
            set(value) {
                binding.profile = value
                Log.d("PremiumHeader", "Profile set to: $value")
            }

        override var account: AccountDetailsResponse?
            get() = binding.account
            set(value) {
                binding.account = value
            }
    }
}

private fun UserCitizenship.setSettingsWheelCitizenshipTextAndBackground(textView: AppCompatTextView) {
    val (backgroundColor, textColor) = when(this) {
        UserCitizenship.VISITOR -> Pair(R.color.colorAlwaysLightSurfaceSecondaryDarker, R.color.colorAlwaysDarkSurfaceSecondaryDark)
        UserCitizenship.RESIDENT -> Pair(R.color.colorPodiumSpeakerResident, R.color.colorAlwaysDarkSurfaceSecondaryDark)
        UserCitizenship.CITIZEN -> Pair(R.color.colorPodiumSpeakerCitizen, R.color.colorPrimary)
        UserCitizenship.OFFICER -> Pair(R.color.colorPodiumSpeakerOfficer, R.color.colorPrimary)
        UserCitizenship.AMBASSADOR -> Pair(R.color.colorPodiumSpeakerAmbassador, R.color.colorPrimary)
        UserCitizenship.MINISTER-> Pair(R.color.colorAlwaysLightPrimaryDarkest, R.color.white)
        UserCitizenship.PRESIDENT-> Pair(R.color.colorSecondaryDark, R.color.colorPrimary)
        UserCitizenship.GOLDEN-> Pair(R.color.colorSecondaryDark, R.color.colorFlashatGolden)
    }
    textView.apply {
        text = context.getString(displayText())
        setTextColor(context.getColor(textColor))
        if (this@setSettingsWheelCitizenshipTextAndBackground == UserCitizenship.PRESIDENT) {
            background = ContextCompat.getDrawable(context, R.drawable.bg_citizenship_president_right)
        } else {
            background.setTint(ContextCompat.getColor(context, backgroundColor))
        }
    }
}
