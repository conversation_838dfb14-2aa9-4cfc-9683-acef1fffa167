package com.app.messej.ui.home.settings.rating

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import com.app.messej.R
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.ListErrorItemView
import com.kennyc.view.MultiStateView

@Composable
fun PerformanceRatingScreen(
    viewModel: RatingViewModel,
    onNavigateToRestoreRating: () -> Unit
) {
    val accountDetails by viewModel.accountDetails.observeAsState()
    val ratingDetails by viewModel.ratingDetails.observeAsState()
    val viewState by viewModel.viewState.observeAsState()

    if (viewState == MultiStateView.ViewState.LOADING) {
        ComposeShimmerListLayout(
            modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.activity_margin))
                .padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            itemCount = 10
        ) { brush ->
            PerformanceRatingShimmerItem(brush = brush)
        }
        return
    }

    if (viewState == MultiStateView.ViewState.ERROR) {
        ListErrorItemView(onRetry = viewModel::getRatingDetails)
        return
    }

    LazyColumn(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
        contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.activity_margin))
    ) {
        item {
            PerformanceRatingRestoreButtonView(
                rating = accountDetails?.flaxRatePercentage,
                onClick = onNavigateToRestoreRating
            )
        }
        item {
            Text(
                modifier = Modifier.padding(top = dimensionResource(id = R.dimen.activity_margin)),
                text = stringResource(id = R.string.performance_rating_activity_status_title),
                color = colorResource(id = R.color.textColorPrimary)
            )
        }

        items(ratingDetails?.size ?: 0) {
            val item = ratingDetails?.get(it) ?: return@items
            PerformanceRatingSingleItem(
                isTaskCompleted = item.second,
                text = stringResource(id = item.first)
            )
        }
    }
}