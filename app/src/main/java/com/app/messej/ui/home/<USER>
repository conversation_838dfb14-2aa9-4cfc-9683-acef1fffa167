package com.app.messej.ui.home

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.ConnectivityObserver
import com.app.messej.NetworkConnectivityObserver
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.AppLocalSettings
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.eTribe.ETribeSuperStarMessageResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.profile.BirthdayUser
import com.app.messej.data.model.api.profile.UpdateUserLocation
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.enums.BiometricLockTimeout
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.NightModeSetting
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ETribeRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.worker.ChatMessageWorker
import com.app.messej.data.repository.worker.FlashUploadWorker
import com.app.messej.data.repository.worker.PostatUploadWorker
import com.app.messej.data.socket.AbstractSocketRepository
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.socket.PodiumSocketRepository
import com.app.messej.data.socket.SocketEvent
import com.app.messej.data.socket.repository.FlaxEventRepository
import com.app.messej.data.socket.repository.PresidentEventRepository
import com.app.messej.data.socket.repository.ProfileEventRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.LogCatReader
import com.app.messej.data.utils.ResultOf
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.time.Duration
import java.time.LocalDate
import kotlin.math.roundToInt


class CommonHomeViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo: AccountRepository = AccountRepository(application)
    private val huddleRepo = HuddlesRepository(application)
    private val datastore: FlashatDatastore = FlashatDatastore()
    private val profileRepo = ProfileRepository(application)
    private val settingsRepo = SettingsRepository(application)
    private val businessRepo = BusinessRepository(application)
    private val socketRepo = ChatSocketRepository
    private val podiumSocketRepo = PodiumSocketRepository
    private val chatRepo = ChatRepository(application)
    private val flashRepo = FlashRepository(application)
    private val flaxTransferEventRepo = FlaxEventRepository
    private val presidentEventRepository = PresidentEventRepository
    private val profileEventRepo = ProfileEventRepository
    private val eTribeRepo = ETribeRepository(application)

    val flaxTransfer = flaxTransferEventRepo.flaxTransferPayLoad.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val onNewpPresidentCrowned = presidentEventRepository.presidentPayLoad.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val levelUpgradationEvent = profileEventRepo.levelUpgradationEvent.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val citizenshipLevelUpgradeEvent = profileEventRepo.citizenshipLevelUpgradeEvent.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val adminAddedFlixCoisEvent = profileEventRepo.adminAddedFlixCoinEvent.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val eTribeSuperstarMessageEvent = profileEventRepo.eTribeSuperstarMessageEvent
    val eTribeSuperStarMessages = LiveEvent<List<ETribeSuperStarMessageResponse.SuperStarMessage>?>()

    val profile: LiveData<CurrentUser.Profile?> = accountRepo.userProfileFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val userFlow = accountRepo.userFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val user: CurrentUser?
        get() = if (accountRepo.loggedIn) accountRepo.user else null

    val isPremiumUser = userFlow.map {
        it?.profile?.premium ?: false
    }.distinctUntilChanged()

    //The value is assigned only if the user is logged in; otherwise, it will be null.
    var themeConfig: CurrentUser.ThemeConfig? = null

    val onSwitchTheme = LiveEvent<CurrentUser.ThemeConfig>()

    val onLoggedOut = LiveEvent<Boolean>()

    private val _appSettings = datastore.settingsFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = null
    )


    val appSettings: LiveData<AppLocalSettings?> = _appSettings.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    private val didShowProfileCompletionDialog = datastore.getProfileCompletionDialogLastShown().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).map {
        if (it != null) {
            return@map it.toLocalDate().isEqual(LocalDate.now())
        }
        return@map false
    }
    val accountVerifyStatus = datastore.getAccountVerifiedStatus().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    private val canShowProfileCompletionDialog = datastore.getCanShowProfileCompletionDialog().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val showProfileCompletionDialog: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            userFlow.value?.let {
                med.postValue(it.profileCompletePercentage < 100 && didShowProfileCompletionDialog.value == false && canShowProfileCompletionDialog.value == true)
            } ?: med.postValue(false)
        }
        med.addSource(userFlow) { update() }
        med.addSource(didShowProfileCompletionDialog) { update() }
        med.addSource(canShowProfileCompletionDialog) { update() }
        med
    }

    fun setPresidentIdFromSocket(presidentId: String) {
        viewModelScope.launch {
            datastore.saveCurrentPresidentId(presidentId)
        }
    }

    fun onProfileCompletionDialogShown() {
        viewModelScope.launch {
            datastore.setProfileCompletionDialogShown()
        }
    }

    fun onDontShowProfileCompletionDDialogAgain() {
        viewModelScope.launch {
            datastore.setDontShowProfileCompletionDialog()
        }
    }

    //upgrade banner
    val didDismissUpgradeBannerToday = datastore.getLastDismissedUpgradeBanner().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).map {
        if (it != null) {
            return@map it.toLocalDate().isEqual(LocalDate.now())
        }
        return@map false
    }

    fun onDismissUpgradeBanner() {
        viewModelScope.launch {
            datastore.setDismissedUpgradeBanner()
        }
    }

    val connectionStatus = NetworkConnectivityObserver(application).observe().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _pendingAppInvite = MutableLiveData<String?>(null)
    val pendingAppInvite: LiveData<String?> = _pendingAppInvite

    fun registerAppInviteCode(code: String) {
        _pendingAppInvite.postValue(code)
    }

    fun onAppInviteHandled() {
        _pendingAppInvite.postValue(null)
    }

    //TODO move to separate viewModel as code is Main activity Specific
    val ppAccountClicked = LiveEvent<Boolean>()
    val learnMoreClicked = LiveEvent<Boolean>()
    val navigateToDearsList = LiveEvent<Boolean>()
    val navigateDocumentPolicy = LiveEvent<Pair<DocumentType, Boolean>>()

    private var wasLoggedIn = false

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    data class FlaxRatePackage(
        val flaxRate: Int,
        val rateChange: Boolean?,
        val citizenship: UserCitizenship
    )

    val flaxRatePackage = _accountDetails.map {
        it ?: return@map null
//        return@map if (it.citizenship != UserCitizenship.VISITOR ) {
//            val inc = if (it.flaxRate == 100) null else it.isFlaxIncrement
//            FlaxRatePackage(it.flaxRate, inc,it.citizenship)
//        } else null
        val inc = if (it.flaxRatePercentage == 100) null else it.isFlaxIncrement
        return@map FlaxRatePackage(it.flaxRatePercentage?:0, inc,it.citizenship)
    }

    data class GiftPackage(
        val recievedGiftCount: String,
        val recievedGiftcoin: String,
        val totalRecievedPointsThisMonth: String,
        val isPremium: Boolean,
    )

    val giftPackage = accountDetails.map {
        it ?: return@map null
        return@map if (it.totalRecievedPointsThisMonth != null) {
            return@map GiftPackage(it.userGiftSum.toString(), it.totalGiftPoints?.roundToInt().toString(), it.totalRecievedPointsThisMonth?.roundToInt().toString(), it.isPremium ?: false)
        } else null

    }

    init {
        if(accountRepo.loggedIn) {
            themeConfig = accountRepo.user.themeConfig
        }
        viewModelScope.launch {
            accountRepo.userFlow.collect {
                it?.let { user ->
                    if (user.profileCheckpoint!= ProfileCheckpoint.PROFILE_CHECKPOINT_NONE) return@collect
                    if (themeConfig!=user.themeConfig) {
                        Log.w("THEME", "replacing theme config: $themeConfig with $user.themeConfig")
                        themeConfig = user.themeConfig
                        onSwitchTheme.postValue(user.themeConfig)
                    }
                }
                val id = if (it == null) "" else "${it.id}|${it.username}"
                Log.d("CHVM", "setting crashlytics user id: $id")
                Firebase.crashlytics.setUserId(id)
            }

        }

        viewModelScope.launch {
            accountRepo.loggedInFlow.collect { loggedIn ->
                if (!loggedIn) {
                    if (!wasLoggedIn) return@collect
                    onLoggedOut.postValue(true)
                } else {
                    wasLoggedIn = true
                }
            }
        }
        viewModelScope.launch {
            accountRepo.tokenUpdatedFLow.collect { tokens ->
                fun <T: SocketEvent>AbstractSocketRepository<T>.triggerRestart() {
                    stop()
                    tokens?.let {
                        restart()
                    }
                }
                socketRepo.triggerRestart()
                podiumSocketRepo.triggerRestart()
            }
        }
        viewModelScope.launch {
            socketRepo.connectionStateFlow.collect { connected ->
                Log.d("CMW", "socket is connected: $connected")
                if (connected) {
                    ChatMessageWorker.startIfNotRunning()
                    FlashUploadWorker.startIfNotRunning()
                    PostatUploadWorker.startIfNotRunning()
                } else chatRepo.resetSendActions()
            }
        }
        viewModelScope.launch {
            // periodically check if socket is connected. will connect of not connected
            try {
                while (true) {
                    if (!isActive) break
                    delay(5000)
                    socketRepo.start()
                    podiumSocketRepo.start()
                }
            } catch (e: Exception) {

            } finally {

            }
        }
        viewModelScope.launch {
            NetworkConnectivityObserver(application).observe().collect {
                if (it == ConnectivityObserver.ConnectionStatus.AVAILABLE) {
                    socketRepo.start()
                    podiumSocketRepo.start()
                }
            }
        }
        viewModelScope.launch {
            try {
                chatRepo.resetSendActionsAndMediaUploads()
                flashRepo.resetSendActionsAndMediaUploads()
                chatRepo.cleanupMediaFiles()
            } catch (e: Exception) {

            } finally {

            }
        }

    }

    fun loadInitialData() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                getETribeSuperStarMessages()
                profileRepo.refreshUserNickNames()
                profileRepo.refreshAccountDetails()
                settingsRepo.getPrivacyStatus()
                profileRepo.getVerifyAccountDetails()
                showBirthdayIfRequired()
                if(!hasLocationChanged){ syncLocation()}
                if (userFlow.value?.profile?.premium == true) {
                    businessRepo.getFlashAtActivityDetails()
                }
                socketRepo.start()
                podiumSocketRepo.start()
                AuthenticationRepository(getApplication()).registerFCMToken()
                profileRepo.getUnreadCounts {
                    onMaidanLive.postValue(it)
                }
            } catch (e: Exception) {
                Firebase.crashlytics.recordException(e)
            }
        }
    }

    private fun getETribeSuperStarMessages() {
        viewModelScope.launch(Dispatchers.IO){
            val result = eTribeRepo.getSuperStarMessageDetails()
            if (result is ResultOf.Success) {
                eTribeSuperStarMessages.postValue(result.value.members)
            }
        }
    }

    fun checkSocketConnection() {
        socketRepo.start()
        podiumSocketRepo.start()
    }


    val unreadPublicHuddles: LiveData<Int> = huddleRepo.getUnreadHuddles(HuddleType.PUBLIC)
    val unreadPrivateHuddles: LiveData<Int> = huddleRepo.getUnreadHuddles(HuddleType.PRIVATE)
    val unreadPrivateChats: LiveData<Int> = huddleRepo.getUnreadChats()
    val unreadBuddiesChats: LiveData<Int> = huddleRepo.getUnreadBuddiesChats()
    val unreadIntruderChats: LiveData<Int> = huddleRepo.getUnreadIntruderChats()

    val unreadNotifications: LiveData<Int?> = datastore.getNotificationCountFlow().asLiveData(Dispatchers.Main)
    val businessOperation: LiveData<BusinessOperation?> = businessRepo.getOperations()

    val unreadCounts: LiveData<UnreadItemsResponse> = datastore.getUnreadCountsFlow().filterNotNull().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val unreadPrivate: MediatorLiveData<Int> by lazy {
        val med = MediatorLiveData<Int>()
        fun totalUnread() {
            val huddles = unreadPrivateHuddles.value ?: 0
            val chats = unreadPrivateChats.value ?: 0
            med.postValue(huddles + chats)
        }
        med.addSource(unreadPrivateHuddles) { totalUnread() }
        med.addSource(unreadPrivateChats) { totalUnread() }
        med
    }

    val onMaidanLive = LiveEvent<String>()

//    fun countAppShare() {
//        viewModelScope.launch {
//            when (profileRepo.countAppShare()) {
//                is ResultOf.Success -> businessRepo.getBusinessOperations()
//                else -> {}
//            }
//        }
//    }

    fun setAppLockTime() {
        viewModelScope.launch {
            val settings = settingsRepo.getSettings().firstOrNull()
            settings?.biometricLockTimeout?.let {
                if (it != BiometricLockTimeout.IMMEDIATELY) {
                    settings.appLastActiveTime = DateTimeUtils.getZonedDateTimeNow()
                    datastore.saveSettings(settings)
                }
            }

        }
    }

    val checkBiometric = LiveEvent<Boolean>()
    fun checkBiometricTime() {
        viewModelScope.launch {
            val settings = _appSettings.firstOrNull() ?: return@launch
            if (settings.appLastActiveTime == null && settings.biometricLockTimeout == BiometricLockTimeout.IMMEDIATELY) checkBiometric.postValue(true)
            settings.appLastActiveTime?.let {
                val seconds = (DateTimeUtils.durationToNowFromPast(it)?.seconds ?: 0)
                when (settings.biometricLockTimeout) {
                    BiometricLockTimeout.AFTER_ONE_MINUTE -> {
                        if (seconds > Duration.ofSeconds(60).seconds) {
                            checkBiometric.postValue(true)
                        }
                    }

                    BiometricLockTimeout.AFTER_FIFTEEN_MINUTES -> {
                        if (seconds > Duration.ofMinutes(15).seconds) {
                            checkBiometric.postValue(true)
                        }
                    }

                    BiometricLockTimeout.AFTER_ONE_HOUR -> {
                        if (seconds > Duration.ofHours(1).seconds) {
                            checkBiometric.postValue(true)
                        }
                    }

                    BiometricLockTimeout.IMMEDIATELY -> {
                        checkBiometric.postValue(true)
                    }

                    else -> {}
                }
            }
        }
    }

    fun saveNightModeSetting(setting: NightModeSetting) {
        _appSettings.value?.appNightMode = setting
        viewModelScope.launch {
            val settings = settingsRepo.getSettings().firstOrNull() ?: return@launch
            settings.appNightMode = setting
            datastore.saveSettings(settings)
        }
    }

    val onUserBirthday= LiveEvent<UserBirthdayResponse>()
    val onLevelUpgrades = LiveEvent<Pair<UserBirthdayResponse,String?>>()


     private val _birthdaysList = MutableLiveData<List<BirthdayUser>>(null)
    val birthdaysList: LiveData<List<BirthdayUser>> = _birthdaysList

     fun showBirthdayIfRequired() {
        viewModelScope.launch(Dispatchers.IO) {
            //todo check if video  is shown today
            val lastBirthdayDate = datastore.getLastBirthdayDate().first()
            val parsedLastBirthday=DateTimeUtils.parseDate(lastBirthdayDate)
            val currentDate=LocalDate.now().toString()

                when (val result: ResultOf<UserBirthdayResponse> = profileRepo.getUserBirthday()) {
                    is ResultOf.Success -> {

                        if (lastBirthdayDate == null || lastBirthdayDate != currentDate) {
                            onUserBirthday.postValue(result.value)
                            _birthdaysList.postValue(result.value.otherUserBirthdays?.subList(0, result.value.otherUserBirthdays.size)) // it will post data from 1st index of list will avoid 0th index
                            datastore.saveBirthdayDate(LocalDate.now().toString())
                        }

                        onLevelUpgrades.postValue(Pair(result.value, lastBirthdayDate))
                        Log.d("PRESIDENT_ID", "CH${result.value.currentPresidentId.toString()}")
                        datastore.saveCurrentPresidentId(result.value.currentPresidentId?.toString()?:"")
                    }

                    is ResultOf.APIError -> {
                        Log.d("BIRTHDAY", result.error.message)
                    }

                    is ResultOf.Error -> {
                        Log.d("BIRTHDAY", result.exception.message.toString())
                    }
                }
        }
    }

    fun setPresidentNotification(response: UserBirthdayResponse) {
        onLevelUpgrades.postValue(Pair(response,""))
    }
    fun syncLocation(){
        viewModelScope.launch(Dispatchers.IO) {
            val lastSyncDate= datastore.getLastLocationDate().first()
            val currentDate=LocalDate.now().toString()
            Log.d("Loc", "${lastSyncDate} ")
            if (lastSyncDate==null ||lastSyncDate!=currentDate) {
                Log.d("Loc", "first sync ")
                val lat = accountRepo.getCountryCode()?.latitude
                val long = accountRepo.getCountryCode()?.longitude
                if (lat != null && long != null) {
                    Log.d("Loc", "lat: $lat, long: $long")
                    when (val result: ResultOf<String> = profileRepo.updateUserLocation(UpdateUserLocation(long = long, lat = lat))){
                        is ResultOf.Success -> {
                            datastore.syncLocationDate(LocalDate.now().toString())
                            profileRepo.getAccountDetails()
                        }
                        is ResultOf.APIError -> {
                            Log.d("Loc", result.error.message)
                        }
                        is ResultOf.Error -> {
                            Log.d("Loc", result.exception.message.toString())
                        }
                    }
                }
            }else{
                Log.d("Loc", "already sync")
            }
        }
    }

    val hasLocationChanged:Boolean
        get() = user?.locationChanged ?: false


    fun sendCitizenshipViewedAcknowledgement(userId: Int,otherUserId: Int)  {
        ProfileEventRepository.sendCitizenshipViewedAcknowledgement(userId,otherUserId)
    }

    val loggingEnabled = LogCatReader.loggingEnabledFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    fun updateAccount(){
        viewModelScope.launch(Dispatchers.IO) {
            profileRepo.getAccountDetails()
        }
    }
}
