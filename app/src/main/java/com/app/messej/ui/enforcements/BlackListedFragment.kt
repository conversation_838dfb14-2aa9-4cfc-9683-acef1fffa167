package com.app.messej.ui.enforcements

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.databinding.FragmentBlackListedBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BlackListedFragment : Fragment() {

    private lateinit var binding: FragmentBlackListedBinding
    private val viewModel: EnforcementsViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_black_listed, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        handleOnBackPressed()
        binding.btnPayFine.setOnClickListener {
            findNavController().navigateSafe(
                BlackListedFragmentDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_BLACKLISTED)
            )
        }
    }

    private fun handleOnBackPressed() {
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Back action blocked here
            }
        })
    }

    private fun observe() {
        viewModel.enforcementsLiveData.observe(viewLifecycleOwner) {
            if (it?.enforcementsStatus?.blacklisted != true) findNavController().navigateUp()
        }
    }

}