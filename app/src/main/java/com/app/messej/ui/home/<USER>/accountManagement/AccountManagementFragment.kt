package com.app.messej.ui.home.settings.accountManagement

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentAccountManagementBinding
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.premium.SubscribeGoldenBottomSheetViewModel
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class AccountManagementFragment : Fragment() {

    private lateinit var binding: FragmentAccountManagementBinding
    private val viewModel: AccountManagementViewModel by viewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()
    private val goldenSubscriptionViewModel: SubscribeGoldenBottomSheetViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_account_management, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.apply {
            text = getString(R.string.bottom_sheet_account_management)
            isAllCaps = true
        }

    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        setupClickListeners()
        goldenSubscriptionViewModel.getData()
        binding.apply {
            isVisitorOrResident = viewModel.isVisitorOrResident
            isPresident = viewModel.isPresident

            premiumStatus.description = if (viewModel.user?.profile?.premium == true) {
                val expirationDate = viewModel.user?.profile?.formattedSubscriptionExpiration.takeIf { !it.isNullOrEmpty() }
                expirationDate?.let { getString(R.string.account_management_premium_status_expires_date, expirationDate) }
            } else {
                getString(R.string.settings_sub_title_subscription)
            }

            location.root.isVisible = viewModel.isLocationVisible
            //Hiding switch account for now.
            switchAccount.root.isVisible = false
        }
    }

    private fun observe() {
        viewModel.accountDetails.observe(viewLifecycleOwner) {
            Log.d("AccountManagement", "${it?.flaxRatePercentage}")
        }
        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            binding.premiumStatus.root.isClickable = it
        }
        subscriptionStatusViewModel.isActive.observe(viewLifecycleOwner) {
            Log.d("AccountManagement", "is active -> $it")
        }

        goldenSubscriptionViewModel.upgradeUserLevel.observe(viewLifecycleOwner){ data->
            Log.d("AccountManagement", "is golden active -> $data")
//           binding.goldenStatus.description = if(data.isGoldenExpired==false){
//                    getString(R.string.account_management_premium_status_expires_date, data.expireTime)
//            }else{
//                getString(R.string.settings_sub_title_golden_level)
//            }
       }
    }

    private fun setupClickListeners() {
        binding.apply {
            profile.clickableLayout.setOnClickListener {
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionGlobalProfileFragment())
            }
            yourTribe.clickableLayout.setOnClickListener {
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionGlobalMyETribeFragment())
            }
            levels.clickableLayout.setOnClickListener {
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionAccountManagementToLevelsFragment())
            }
            premiumStatus.clickableLayout.setOnClickListener {
                upgradeToPremium()
            }
//            goldenStatus.clickableLayout.setOnClickListener {
//
//                val currentFlix = viewModel.accountDetails.value?.currentFlix?.toInt() ?: 0
//                val requiredFlix = goldenSubscriptionViewModel.upgradeUserLevel.value?.userUpgradeLevelMapping?.golden ?: 0
//
//                if ((currentFlix < requiredFlix)&&goldenSubscriptionViewModel.upgradeUserLevel.value?.isGoldenExpired==null) {
//                    showToast(getString(R.string.upgrade_golden_insuffcient_balance) )
//                    return@setOnClickListener
//                }
//                upgradeToGolden(goldenSubscriptionViewModel.upgradeUserLevel.value?.isGoldenExpired)
//            }
            rating.clickableLayout.setOnClickListener {
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionAccountManagementToPerformanceRatingFragment())
            }
            inviteFriend.clickableLayout.setOnClickListener {
                shareReferralLink()
            }
            location.clickableLayout.setOnClickListener {
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionAccountManagementToChangeUserLocationFragment())
            }
            restoreYourRating.clickableLayout.setOnClickListener {
                if (viewModel.accountDetails.value?.flaxRatePercentage == 100) {
                    showToast(message = R.string.account_management_rating_100_percentage)
                    return@setOnClickListener
                }
                findNavController().navigateSafe(AccountManagementFragmentDirections.actionGlobalRestoreRatingFragment())
            }
        }
    }

    private fun upgradeToGolden(goldenExpired: Boolean?) {
        if (goldenExpired == null) {
            findNavController().navigateSafe(
                AccountManagementFragmentDirections.actionAccountManagementFragmentToUpgradeGoldenFragment()
            )
        }else {
            findNavController().navigateSafe(
                AccountManagementFragmentDirections.actionAccountManagementFragmentToAlreadySubscribedGoldenFragment()
            )
        }
    }

    private fun shareReferralLink() {
        val user = viewModel.user?: return
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, getString(R.string.title_operations_share_message, user.username, user.profile.referralLink)
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

    private fun upgradeToPremium() {
        if (subscriptionStatusViewModel.subscriptionLoaded.value==false) return
        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        AccountManagementFragmentDirections.actionAccountManagementToAlreadySubscribedFragment(false)
                    )
                }
                else -> {
                    findNavController().navigateSafe(
                        AccountManagementFragmentDirections.actionGlobalUpgradePremiumFragment()
                    )
                }
            }
        }
    }

}