package com.app.messej.ui.chat

import android.app.Application
import android.content.Intent
import android.media.MediaRecorder
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.annotation.CallSuper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.MainApplication
import com.app.messej.data.RuleConstants
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.MediaPlayerInfo.Companion.LIST_POS_RECORDING
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.VideoEditInfo
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.ChatTextColor.Companion.orDefault
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.worker.ChatMessageWorker
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.utils.TextFormatUtils
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

abstract class BaseChatViewModel(application: Application) : BaseChatDisplayViewModel(application), TextFormatUtils.TextFormattingListener {

    override fun playMedia(message: AbstractChatMessageWithMedia, pos: Int) {
        if (audioRecording.value == true) return
        if (showAudioPreview.value == true) return
        super.playMedia(message, pos)
    }

    // Chat Interactions

    val chatTextInput = MutableLiveData("")

    private val _chatTextColor = MutableLiveData(ChatTextColor.DEFAULT)
    val chatTextColor: LiveData<ChatTextColor> = _chatTextColor

    override fun getSelectedColor() = chatTextColor.value.orDefault()
    override fun onSelectColor(color: ChatTextColor) {
        _chatTextColor.postValue(color)
    }

    override fun canInteract(): Boolean {
        return enableChatInteraction.value==true
    }

    private var imageCaptureTempFile: File? = null
    private var videoCaptureTempFile: File? = null
    val chatMedia = MutableLiveData<TempMedia?>(null)

    val showAudioPreview = chatMedia.map {
        return@map it?.mediaType == MediaType.AUDIO
    }

    val chatMediaImageOrVideo = chatMedia.map {
        return@map if (it?.mediaType == MediaType.IMAGE || it?.mediaType == MediaType.VIDEO) it else null
    }

    val chatMediaImage = chatMedia.map {
        return@map if (it?.mediaType == MediaType.IMAGE) it else null
    }

    val chatMediaVideo = chatMedia.map {
        return@map if (it?.mediaType == MediaType.VIDEO) it else null
    }

    val chatMediaAudio = chatMedia.map {
        return@map if (it?.mediaType == MediaType.AUDIO) it else null
    }

    val chatMediaDocument = chatMedia.map {
        return@map if (it?.mediaType == MediaType.DOCUMENT) it else null
    }

    val onAddChatMedia = LiveEvent<MediaType>()

    val chatLocation = MutableLiveData<AttachLocation?>(null)

    val onMessageCreated = LiveEvent<String>()

    val showSendAction: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(true)
        fun update() {
            med.postValue(chatTextInput.value.orEmpty().isNotBlank() && _audioRecording.value == false)
        }
        med.addSource(chatTextInput) { update() }
        med.addSource(_audioRecording) { update() }
        med.distinctUntilChanged()
    }

    open val enableTextFormatting: Boolean
        get() = user.premium

    private val chatTyping = MutableLiveData<Boolean>(false)

    interface TypingListener {
        fun onTyping(typing: Boolean)
    }

    abstract val typingListener: TypingListener

    var videoDurationLimit: Int = RuleConstants.getDefaultVideoDurationLimit(user.premium)

    init {
        viewModelScope.launch {
            chatTextInput.asFlow().debounce(500L).collect { text ->
                text ?: return@collect
                if (text.isNotBlank()) {
                    if (chatTyping.value == false) {
                        chatTyping.value = true
//                        huddleEventRepo.announceChatTyping(huddleID.value!!, true)
                        typingListener.onTyping(true)
                    }
                    viewModelScope.launch {
                        delay(1000)
                        val current = chatTextInput.value ?: ""
                        if (current == text || current.isBlank()) {
                            // text hasn't changed in 2 seconds
                            chatTyping.value = false
                            typingListener.onTyping(false)
                        }
                    }
                } else {
                    if (chatTyping.value == true) {
                        chatTyping.value = false
                        typingListener.onTyping(false)
                    }
                }
            }
        }
        viewModelScope.launch {
            profileRepo.refreshAccountDetails()
        }
        viewModelScope.launch {
            Log.w("CVIDEO", "collect account details")
            accountRepo.getAccountDetailsFlow().collect {
                it?.videoDurationLimit ?: return@collect
                val limit = it.videoDurationLimit.toInt()
                Log.w("CVIDEO", "videoDurationLimit: $limit")
                videoDurationLimit = limit
            }
        }
    }

    private val _chatMediaVideoProgress = MutableLiveData<Int?>(null)
    val chatMediaVideoProgress: LiveData<Int?> = _chatMediaVideoProgress
    private var chatVideoOngoingJob: Job? = null

    val videoIsEncoding = _chatMediaVideoProgress.map {
        it != null
    }.distinctUntilChanged()

    protected val _messageSending = MutableLiveData(false)
    val messageSending: LiveData<Boolean> = _messageSending

    protected open val canInteractWithChat: LiveData<Boolean> = MutableLiveData(true)

    val enableChatInteraction: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            Log.w("ECI", "update: ${canInteractWithChat.value} ${_messageSending.value} ${videoIsEncoding.value}",)
            med.postValue(canInteractWithChat.value == true && _messageSending.value != true && videoIsEncoding.value != true && chatSelectionMode.value != true)
        }
        med.addSource(chatSelectionMode) { update() }
        med.addSource(canInteractWithChat) { update() }
        med.addSource(_messageSending) { update() }
        med.addSource(videoIsEncoding) { update() }
        med
    }

    private suspend fun processVideo() = withContext(Dispatchers.IO) {
        val media = chatMedia.value ?: throw Exception("No media to process")
        chatVideoOngoingJob?.let {
            chatVideoOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        _chatMediaVideoProgress.postValue(-1)
        Firebase.crashlytics.log("Starting Video compress")
        chatVideoOngoingJob = launch {
            try {
                val processed = chatRepo.processVideo(media, object : VideoEncoderUtil.MediaProcessingListener {
                    override fun onProgress(progress: Int) {
                        Log.d("ENCODE", "Progress: $progress%")
                        _chatMediaVideoProgress.postValue(progress)
                    }

                    override fun onProcessingFinished(success: Boolean) {
                        Log.d("ENCODE", "Encode Done ($success) as per VM")
                        _chatMediaVideoProgress.postValue(null)
                    }
                })
                chatVideoOngoingJob = null
                Firebase.crashlytics.log("processed ${media.path} to ${processed.path}")
                withContext(Dispatchers.Main) {
                    chatMedia.value = processed
                }
            } catch (e: Throwable) {
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                chatVideoOngoingJob = null
                _chatMediaVideoProgress.postValue(null)
                throw Exception("Video processing cancelled")
            }
        }
    }

    /**
     * Prepares a message for sending by executing the following steps:
     * 1. Stop all media playback
     * 2. Encode video if present
     * 3. Sanitize inputs
     * 4. Calls [sendMessage] that will trigger the actual sending based on the specific implementation
     * 5. Cleans up values and triggers a background sync if  [sendMessage] returned an object
     * 6. Restores the values if returned null
     */
    @CallSuper
    open fun prepareAndSendMessage() {
        stopMediaPlayback(true)
        _messageSending.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                if (chatMedia.value !is TempMedia.SavedMedia && chatMedia.value?.mediaType == MediaType.VIDEO) {
                    Firebase.crashlytics.log("calling processVideo")
                    processVideo()
                    Firebase.crashlytics.log("video processed. now sending")
                    Log.d("ENCODE", "prepareAndSendMessage: video processed. Sending message")
                }

                var message = chatTextInput.value ?: ""
                val color = chatTextColor.value.orDefault()

                val media = chatMedia.value
                val reply = chatReplyTo.value
                val loc = chatLocation.value
                Log.w("PASM", "prepareAndSendMessage: text: $message | loc: $loc | reply: ${reply?.messageId} | media: ${media?.mediaType}" )
                // Tried clearing chat media to prevent crashes due to actions while message is being sent. But that caused
                // issues with media in the UI disappearing. so commenting this for now and handled the crash in the source
//                clearChatTextAndMedia(false)
//                clearReplyTo()
                if (message.isBlank() && media == null && loc==null) return@launch

                media?.let {
                    if (it.mediaType == MediaType.AUDIO) {
                        message = ""
                        Log.d("Duration", "Duration:" + MediaUtils.getDuration(it.file))
                    } else if (it is TempMedia.StickerMedia) {
                        message = ""
                    } else {}
                }
                val msg = sendMessage(message, media, reply, loc,color)
                if (msg != null) {
                    onMessageCreated.postValue(msg.messageId)
                    clearChatTextAndMedia(false)
                    clearReplyTo()
                    _scrollToMessage.postValue(msg.messageId)
                    triggerMessageSync()
                } else {
                    //restore values as the sending did not succeed

                    withContext(Dispatchers.Main) {
                        if (media is TempMedia.StickerMedia) return@withContext
                        chatTextInput.value = message
                        chatMedia.value = media
                        chatReplyTo.value = reply
                    }
                }
            } catch (e: Exception) {
                Log.d("ENCODE", "prepareAndSendMessage: error: ${e.message}")
            }
            _messageSending.postValue(false)
        }
    }

    protected suspend fun triggerMessageSync() = ChatMessageWorker.startIfNotRunning()

    /**
     * Handles the creation of the chat message and its specific implementation. Called by [prepareAndSendMessage]
     *
     * @param message: the text in the input
     * @param media: any media files if attached
     * @param replyTo: replyTo Objects if present
     *
     * @return and [AbstractChatMessage] object of the created message. return null if failed for some reason
     */
    protected abstract suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?,color: ChatTextColor?): AbstractChatMessage?

    abstract fun onTriggerUpload(msg: AbstractChatMessageWithMedia)

    suspend fun getImageUriForCapture(): Uri = withContext(Dispatchers.IO) {
        val file = chatRepo.createTempImageFile()
        imageCaptureTempFile = file
        chatRepo.getUriForFile(file)
    }

    suspend fun getVideoUriForCapture(): Uri = withContext(Dispatchers.IO) {
        val file = chatRepo.createTempVideoFile()
        videoCaptureTempFile = file
        chatRepo.getUriForFile(file)
    }

    fun addCapturedImage() {
        viewModelScope.launch(Dispatchers.IO) {
            clearChatMedia()
            imageCaptureTempFile?.let { file ->
                imageCaptureTempFile = null
                chatMedia.postValue(TempMedia.of(file, MediaType.IMAGE))
                onAddChatMedia.postValue(MediaType.IMAGE)
            }
        }
    }

    fun addCapturedVideo() {
            clearChatMedia()
            videoCaptureTempFile?.let { file ->
                videoCaptureTempFile = null
                chatMedia.postValue(TempMedia.of(file, MediaType.VIDEO))
                onAddChatMedia.postValue(MediaType.VIDEO)
                checkVideoLimits()
            }
    }

    private fun getMediaType(uri: Uri): String? {
        val contentResolver = getApplication<MainApplication>().contentResolver
        return contentResolver.getType(uri)
    }

    fun addMedia(uri: Uri, triggerCrop: Boolean = false) {
        val contentResolver = getApplication<MainApplication>().contentResolver
        val type = getMediaType(uri)?: return
        /*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
        try {
            contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        } catch (_: Exception) {
        }
        if (type.startsWith("image")) addImage(uri, triggerCrop)
        if (type.startsWith("video")) addVideo(uri)
    }

    fun addDocument(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val contentResolver = getApplication<MainApplication>().contentResolver/*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
            try {
                contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            } catch (_: Exception) {
            }
            clearChatMedia()
            chatMedia.postValue(TempMedia.of(uri, MediaType.DOCUMENT, contentResolver))
            onAddChatMedia.postValue(MediaType.DOCUMENT)
        }
    }

    fun addImage(uri: Uri, triggerCrop: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            clearChatMedia()
            val file = chatRepo.storeImageUriToTempFile(uri)
            chatMedia.postValue(TempMedia.of(file, MediaType.IMAGE))
            onAddChatMedia.postValue(MediaType.IMAGE)
        }
    }

    fun addSticker(sticker: Sticker){
        if (chatReplyTo.value==null) return
        chatMedia.value = TempMedia.StickerMedia(sticker.mediaMeta)
        prepareAndSendMessage()
    }

    fun addVideo(uri: Uri) {
        clearChatMedia()
        chatMedia.postValue(TempMedia.of(uri, MediaType.VIDEO))
        onAddChatMedia.postValue(MediaType.VIDEO)
        checkVideoLimits()
    }

    val onVideoRequiresTrimming = LiveEvent<TempMedia>()

    private fun checkVideoLimits() {
        // TODO need to apply default trim here
    }

    fun applyVideoEdit(editInfo: VideoEditInfo) {
        chatMediaVideo.value?.let {
            chatMedia.postValue(it.apply {
                videoEditInfo = editInfo
            })
        }
    }

    fun attachLocation(latitude: Double, longitude: Double, address: String? = null) {
        viewModelScope.launch(Dispatchers.Main) {
            chatLocation.value = AttachLocation(latitude,longitude, address)
            prepareAndSendMessage()
        }
    }

    val onTriggerCrop = LiveEvent<Pair<Uri, Uri>>()
    val onTriggerImageEdit = LiveEvent<Pair<Uri, Uri>>()
    fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = chatMedia.value ?: return@launch
            if (media.mediaType != MediaType.IMAGE) return@launch
            val src = chatRepo.getUriForFile(media.file)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())
            onTriggerCrop.postValue(Pair(src, dest))
        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    fun clearChatMedia(deleteFiles: Boolean = true) {
        stopMediaPlayback()
        Log.w("ENCODE", "clearing chat media $chatVideoOngoingJob")
        chatVideoOngoingJob?.let {
            try {
                Log.w("ENCODE", "stopping encode job")
                it.cancel()
            } catch (e: Exception) {
                null
            }
            chatVideoOngoingJob = null
        }
        _chatMediaVideoProgress.postValue(null)
        chatMedia.value?.let {
            if (deleteFiles && it !is TempMedia.SavedMedia && it !is TempMedia.StickerMedia) {
                viewModelScope.launch(Dispatchers.IO) {
                    Log.w("HPVD", "deleting media: $it",)
                    chatRepo.deleteFile(it.path)
                }
            }
        }
        Log.w("ENCODE", "clearing chat media $chatVideoOngoingJob")
        chatMedia.postValue(null)
    }

    fun clearChatSticker() {
        if (chatMedia.value is TempMedia.StickerMedia) {
            chatMedia.postValue(null)
        }
    }

    fun clearChatLocation() {
        chatLocation.postValue(null)
    }

    fun clearChatText() {
        chatTextInput.postValue("")
        _chatTextColor.postValue(ChatTextColor.DEFAULT)
    }

    fun clearChatTextAndMedia(deleteFiles: Boolean = true) {
        clearChatText()
        clearChatMedia(deleteFiles)
        clearChatLocation()
    }

    private var recordingTimer: Job? = null

    private fun startRecordingTimer() {
        stopRecordingTimer()
        recordingTimer = viewModelScope.launch {
            try {
                var seconds = 0
                _audioRecordingTimer.postValue(seconds)
                while (true) {
                    delay(1000)
                    _audioRecordingTimer.postValue(++seconds)
                    val limit = voiceRecordLimit
                    Log.w("BCVM", "recording limit: $limit, current: $seconds")
                    if (limit != null && seconds >= limit) {
                        onAudioRecordingLimitReached.postValue(true)
                        finishAudioRecording()
                    }
                }
            } finally {
                Log.w("BCVM", "recording timer cancelled")
            }
        }
    }

    var voiceRecordLimit: Int? = null

    private fun stopRecordingTimer() {
        recordingTimer?.apply {
            cancel()
            recordingTimer = null
            _audioRecordingTimer.postValue(0)
        }
    }

    private val _audioRecording = MutableLiveData(false)
    val audioRecording: LiveData<Boolean> = _audioRecording

    private val _audioRecordingTimer = MutableLiveData(0)
    val audioRecordingTimer = _audioRecordingTimer.map {
        DateTimeUtils.formatSeconds(it.toLong())
    }

    val onAudioRecordingLimitReached = LiveEvent<Boolean>()

    protected val _audioRecordingLocked = MutableLiveData(false)
    val audioRecordingLocked: LiveData<Boolean> = _audioRecordingLocked

    private var audioRecorder: MediaRecorder? = null

    private var audioCaptureTempFile: File? = null

    fun startAudioRecording() {
        _audioRecording.value = true
        clearChatText()
        stopMediaPlayback()
        _audioRecordingLocked.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.createTempAudioFile()
            audioCaptureTempFile = file
            val recorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) MediaRecorder(getApplication()) else MediaRecorder()
            recorder.apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS)
                val bitDepth = 16
                val sampleRate = 44100
                val bitRate = sampleRate * bitDepth

                setAudioEncodingBitRate(bitRate)
                setAudioSamplingRate(sampleRate)
                setOutputFile(file)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                try {
                    prepare()
                    start()
                } catch (e: Exception) {
                    Log.e("PHCVM", "prepare() failed")
                }
            }
            audioRecorder = recorder
            startRecordingTimer()
        }
    }

    fun onLockAudioRecording() {
        _audioRecordingLocked.postValue(true)
    }

    private suspend fun finishRecording(): TempMedia? = withContext(Dispatchers.Main) {
        if (_audioRecording.value == false) return@withContext null
        _audioRecording.value = false
        _audioRecordingLocked.postValue(false)
        stopRecordingTimer()
        return@withContext withContext(Dispatchers.IO) {
            try {
                audioRecorder?.apply {
                    stop()
                    release()
                }
            } catch (_: Exception) {
                return@withContext null
            }
            var media: TempMedia? = null
            return@withContext audioCaptureTempFile?.let { file ->
                audioCaptureTempFile = null
                withContext(Dispatchers.Main) {
                    media = TempMedia.of(file, MediaType.AUDIO)
                    chatMedia.value = media
                    onAddChatMedia.postValue(MediaType.AUDIO)
                    media
                }
            }
            media
        }
    }

    val onAudioLessThanASecond = LiveEvent<Boolean>()

    open fun finishAudioRecording() {
        if (_audioRecording.value == false) return
        val locked = _audioRecordingLocked.value ?: false
        Log.w("REC", "finishAudioRecording: $locked")
        viewModelScope.launch {
            val media = finishRecording() ?: return@launch
            clearChatText()
            if (MediaUtils.getDuration(media.file, true) < 1000) {
                clearChatMedia()
                onAudioLessThanASecond.postValue(true)
            } else if (locked) {
                playPauseRecording(false)
            } else {
                prepareAndSendMessage()
            }
        }

    }

    /**
     * funtion to setup the mediaplayer and start playing audio
     * @param play: pass false to just setup without playing audio
     */
    fun playPauseRecording(play: Boolean = true) {
        if (mediaPLayerLocked || _messageSending.value == true) return
        val media = chatMedia.value ?: return
        if (media.mediaType != MediaType.AUDIO) return
        viewModelScope.launch(Dispatchers.IO) {
            val meta = chatRepo.generateMediaMeta(media)
            withContext(Dispatchers.Main) {
                setupMedia(MediaPlayerInfo.from(media, meta, LIST_POS_RECORDING), play)
            }
        }
    }

    fun cancelAudioRecording() {
        Log.d("BCVM", "cancelAudioRecording: gooo")
        if (_audioRecording.value == false) return
        Log.d("BCVM", "cancelAudioRecording: is true")
        _audioRecording.value = false
        stopRecordingTimer()
        viewModelScope.launch {
            stopMediaPlayback()
            try {
                audioRecorder?.apply {
                    stop()
                    release()
                }
            } catch (_: Exception) {
            }
            audioCaptureTempFile?.apply {
                chatRepo.deleteFile(this)
            }
            audioCaptureTempFile = null
            audioRecorder = null
        }
    }

    override fun onCleared() {
        super.onCleared()
        audioRecorder?.release()
        audioRecorder = null
    }

    // Selection mode and actions

    val chatReplyTo = MutableLiveData<ReplyTo?>(null)
    val isReplyMyOwnMessage = chatReplyTo.map { it?.senderId == user.id }

    val onAddChatReplyTo = LiveEvent<Boolean>()

    open val canReplyToSelection: LiveData<Boolean> by lazy {
        _selectedChats.map { (enableChatInteraction.value == true || chatSelectionMode.value == true) && it.size == 1 && !it[0].reported }
    }
    open val canReportSelection: LiveData<Boolean> by lazy {
        _selectedChats.map { enableChatInteraction.value == true && it.size == 1 && !it[0].reported && it[0].sender != user.id }
    }

    abstract val deleteTimeout: StateFlow<Long?>

    open val canDeleteSelectionForEveryone: LiveData<Boolean> by lazy {
        _selectedChats.map {
            val timeout = deleteTimeout.value
            Log.w("BCVM", "delete timeout: $timeout")
            return@map if (timeout == null) {
                true
            } else {
                it.all { ch -> (DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds ?: 0) < timeout }
            }
        }
    }

    open fun canDeleteMessageForEveryOne(msg: AbstractChatMessageWithMedia): Boolean {
        //Check the given message, which can be deleted for everyone
        msg.apply {
            val timeout = deleteTimeout.value
            Log.w("BCVM", "delete timeout: $timeout")
            return if (timeout == null) {
                true
            } else {
                (DateTimeUtils.durationToNowFromPast(message.parsedCreatedTime)?.seconds ?: 0) < timeout
            }
        }

    }

    fun replyToItem(msg: AbstractChatMessage) {
        val sender = getSenderForReply(msg) ?: return
        chatReplyTo.value = ReplyTo.create(msg, sender)
        onAddChatReplyTo.postValue(true)
    }

    abstract fun likeItem(item: AbstractChatMessage)

    fun replyToSelection() {
        if (_selectedChatsList.size > 0) {
            val msg = _selectedChatsList[0]
            replyToItem(msg)
        }
        exitSelectionMode()
    }

    fun replyToSelection(msg: AbstractChatMessage) {
        replyToItem(msg)
        exitSelectionMode()
    }

    fun clearReplyTo() {
        chatReplyTo.postValue(null)
        onAddChatReplyTo.postValue(false)
        clearChatSticker()
    }

    fun editImage() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = chatMedia.value ?: return@launch
            if (media.mediaType != MediaType.IMAGE) return@launch
            val src = chatRepo.getUriForFile(media.file)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())
            onTriggerImageEdit.postValue(Pair(src, dest))
        }
    }

    fun handleExternalShare(mediaType: MediaType?, fileUri: Uri?, message: String?) {
        when (mediaType) {
            MediaType.DOCUMENT, MediaType.AUDIO -> fileUri?.let { addDocument(it) }
            MediaType.IMAGE, MediaType.VIDEO -> fileUri?.let { addMedia(it) }
            else -> {}
        }
        chatTextInput.postValue(message)
    }
}