package com.app.messej.ui.home.settings.accountManagement

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.CurrentUser.Companion.orDefault
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers

class AccountManagementViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo: AccountRepository = AccountRepository(application)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val user: CurrentUser?
        get() = accountRepo.user

    val isVisitorOrResident: Boolean
        get() = accountRepo.isVisitor == true || accountRepo.isResident == true
    val isPresident: Boolean
        get() = accountRepo.isPresident == true

    val isLocationVisible : Boolean
        get() = user?.let {
        it.userEmpowerment.orDefault().canChangeLocation ||
        it.profile.allowChangeLocation == true ||
        it.citizenship ==UserCitizenship.PRESIDENT ||
        it.citizenship==UserCitizenship.MINISTER
    } ?: false
}