package com.app.messej.ui.home.settings.privacy

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser.Companion.orDefault
import com.app.messej.data.model.api.CountryFlagResponse
import com.app.messej.data.model.api.settings.AboutPrivacy
import com.app.messej.data.model.api.settings.LastSeenPrivacies
import com.app.messej.data.model.api.settings.PrivacySettingsResponse
import com.app.messej.data.model.api.settings.ProfilePhotoPrivacy
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PrivacyViewModel (application: Application): AndroidViewModel(application) {

    private val _isPrivacyApiSuccess = MutableLiveData(false)
    val isPrivacyApiSuccess: LiveData<Boolean> = _isPrivacyApiSuccess

    private val _isPrivacyApiFailed = MutableLiveData(false)
    val isPrivacyApiFailed: LiveData<Boolean> = _isPrivacyApiFailed

    private val _privacyApiFailedMessage = MutableLiveData<String?>(null)
    val privacyApiFailedMessage: LiveData<String?> = _privacyApiFailedMessage

    private val _isPrivacyApiLoading = MutableLiveData(false)
    private val isPrivacyApiLoading: LiveData<Boolean> = _isPrivacyApiLoading

    private val _isCountryFlagLoading = MutableLiveData(false)
    private val isCountryFlagLoading: LiveData<Boolean> = _isCountryFlagLoading

    private val _aboutPrivacy = MutableLiveData<AboutPrivacy?>(null)
    val aboutPrivacy: LiveData<AboutPrivacy?> = _aboutPrivacy

    private val _lastSeenPrivacy = MutableLiveData<LastSeenPrivacies.LastSeenPrivacy?>(null)
    val lastSeenPrivacy: LiveData<LastSeenPrivacies.LastSeenPrivacy?> = _lastSeenPrivacy

    private val _onlineStatusPrivacy = MutableLiveData<LastSeenPrivacies.HideStatusPrivacy?>(null)
    val onlineStatusPrivacy: LiveData<LastSeenPrivacies.HideStatusPrivacy?> = _onlineStatusPrivacy

    private val _profilePhotoPrivacy = MutableLiveData<ProfilePhotoPrivacy?>(null)
    val profilePhotoPrivacy: LiveData<ProfilePhotoPrivacy?> = _profilePhotoPrivacy

    private val _blockedStarCount = MutableLiveData<Int>(null)
    val blockedStarCount: LiveData<Int> = _blockedStarCount

    private val _blockedBroadcastCount = MutableLiveData<Int>(null)
    val blockedBroadcastCount: LiveData<Int> = _blockedBroadcastCount

    private val _blockedMessageCount = MutableLiveData<Int>(null)
    val blockedMessageCount: LiveData<Int> = _blockedMessageCount

    private val _blockedHuddleCount = MutableLiveData<Int>(null)
    val blockedHuddleCount: LiveData<Int> = _blockedHuddleCount

    private val _countyFlagUpdateMessage = MutableLiveData<String>(null)
    val countyFlagUpdateMessage: LiveData<String> = _countyFlagUpdateMessage

    private val _hasToDisplayCountry = MutableLiveData<Boolean?>(null)
    val hasToDisplayCountry: LiveData<Boolean?> = _hasToDisplayCountry

    private val settingsRepo = SettingsRepository(application)
    private val accountRepository = AccountRepository(application)

    val userAccount = accountRepository.userFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val isPricacyFlagVisible = userAccount.map {
        it?.userEmpowerment.orDefault().canHideFlag || it?.profile?.allowHideCountryFlag == true || it?.citizenship ==UserCitizenship.PRESIDENT || it?.citizenship==UserCitizenship.MINISTER || it?.citizenship==UserCitizenship.AMBASSADOR ||it?.citizenship==UserCitizenship.GOLDEN
    }.distinctUntilChanged()

    val privacyPodiumVisible = userAccount.map {
        it?.citizenship in listOf(UserCitizenship.AMBASSADOR, UserCitizenship.MINISTER, UserCitizenship.PRESIDENT,UserCitizenship.GOLDEN)
    }.distinctUntilChanged()

    val isPrivacyDataLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            if (_isCountryFlagLoading.value == false && _isPrivacyApiLoading.value == false) {
                med.postValue(false)
            } else {
                med.postValue(true)
            }
        }
        med.addSource(isCountryFlagLoading) { update() }
        med.addSource(isPrivacyApiLoading) { update() }
        med
    }

    init {
        getCountryFlagDetails()
    }

    private fun getCountryFlagDetails() {
        viewModelScope.launch {
            _isCountryFlagLoading.postValue(true)

            when (val result: ResultOf<CountryFlagResponse> = settingsRepo.getCountryFlagDetails()) {
                is ResultOf.Success -> {
                    _isCountryFlagLoading.postValue(false)
                    _hasToDisplayCountry.postValue(result.value.displayCountryFlag)

                }

                is ResultOf.APIError -> {
                    _isCountryFlagLoading.postValue(false)
                    _privacyApiFailedMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                    // TODO show some feedback
                    _isCountryFlagLoading.postValue(false)
                    _privacyApiFailedMessage.postValue(result.exception.message)
                }
            }
        }
    }

    fun getPrivacyDetails() {

        viewModelScope.launch(Dispatchers.IO) {

            _isPrivacyApiLoading.postValue(true)

            when (val result: ResultOf<PrivacySettingsResponse> = settingsRepo.getPrivacyStatus()) {
                is ResultOf.Success -> {
                    _isPrivacyApiLoading.postValue(false)
                    _isPrivacyApiSuccess.postValue(true)
                    _lastSeenPrivacy.postValue(result.value.lastSeenPrivacies?.lastSeen!!)
                    _onlineStatusPrivacy.postValue(result.value.lastSeenPrivacies?.hideStatus!!)
                    _aboutPrivacy.postValue(result.value.aboutPrivacy!!)
                    _profilePhotoPrivacy.postValue(result.value.profilePhotoPrivacy!!)
                    _blockedBroadcastCount.postValue(result.value.totalHiddenUsers!!)
                    _blockedStarCount.postValue(result.value.totalBlockedUsers!!)
                    _blockedMessageCount.postValue(result.value.totalBlockedChats!!)
                    _blockedHuddleCount.postValue(result.value.blockedHuddles!!)

                }

                is ResultOf.APIError -> {
                    _isPrivacyApiLoading.postValue(false)
                    _isPrivacyApiFailed.postValue(true)
                    _privacyApiFailedMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                    // TODO show some feedback
                    _isPrivacyApiLoading.postValue(false)
                    _isPrivacyApiFailed.postValue(true)
                    _privacyApiFailedMessage.postValue(result.exception.message)
                }
            }
        }
    }

    fun handleCountryFlagUpdate(checked: Boolean) {
        updateCountryFlagDetails(checked)
    }

    val onCountryFlagUpdated = LiveEvent<Boolean>()

    private fun updateCountryFlagDetails(checked: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<CountryFlagResponse> = settingsRepo.updateCountryFlagDetails(checked)) {
                is ResultOf.Success -> {
                    _hasToDisplayCountry.postValue(result.value.displayCountryFlag)
                    onCountryFlagUpdated.postValue(checked)
                }

                is ResultOf.APIError -> {
                    _privacyApiFailedMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

}

