package com.app.messej.ui.home.settings.restoreRating

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import com.app.messej.R
import com.app.messej.ui.composeComponents.CustomAlertDialog
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.CustomLinearProgressIndicator
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.HorizontalDashedLine
import com.app.messej.ui.composeComponents.ListErrorItemView
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.settings.levels.CitizenshipLevelView
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.DataFormatHelper.formatDecimals
import com.kennyc.view.MultiStateView

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RestoreRatingScreen(
    viewModel: RestoreRatingViewModel,
    onRedirectToBuyFlax: () -> Unit
) {
    val scrollState = rememberScrollState()
    val restoreRatingDetails by viewModel.restoreRatingDetails.observeAsState()
    val viewState by viewModel.viewState.observeAsState()
    val alertType by viewModel.alertType.observeAsState()

    if (viewState == MultiStateView.ViewState.ERROR) {
        ListErrorItemView(
            modifier = Modifier.fillMaxSize(),
            onRetry = viewModel::getRestoreRating
        )
        return
    }

    alertType?.let { type ->
        when(type) {
            RestoreRatingViewModel.RestoreRatingAlertType.RestoreRatingSufficientBalance -> {
                CustomAlertDialog(
                    description = stringResource(
                        id = R.string.restore_rating_purchase_confirmation,
                        formatDecimals(restoreRatingDetails?.restoratingFlix)
                    ),
                    positiveButtonText = stringResource(R.string.common_proceed),
                    negativeButtonText = stringResource(R.string.common_cancel),
                    onNegativeButtonClick = { viewModel.setAlertType(null) },
                    onPositiveButtonClick = { viewModel.purchaseRate() }
                )
            }
            RestoreRatingViewModel.RestoreRatingAlertType.RestoreRatingSufficientEffectiveBalance -> {
                CustomAlertDialog(
                    description = stringResource(
                        id = R.string.restore_effective_flix_confirmation,
                        formatDecimals(restoreRatingDetails?.flixBalance),
                        formatDecimals(restoreRatingDetails?.coinAmount)
                    ),
                    positiveButtonText = stringResource(R.string.common_proceed),
                    negativeButtonText = stringResource(R.string.common_cancel),
                    onNegativeButtonClick = { viewModel.setAlertType(null) },
                    onPositiveButtonClick = { viewModel.purchaseRate() }
                )
            }
            RestoreRatingViewModel.RestoreRatingAlertType.InsufficientFlixBalance -> {
                CustomAlertDialog(
                    description = stringResource(
                        id = R.string.restore_rating_error,
                        formatDecimals(restoreRatingDetails?.flixBalance),
                        formatDecimals(restoreRatingDetails?.restoratingFlix),
                        formatDecimals(restoreRatingDetails?.requiredFlix)
                    ),
                    positiveButtonText = stringResource(id = R.string.podium_buy_camera_buy_action),
                    onNegativeButtonClick = { viewModel.setAlertType(null) },
                    onPositiveButtonClick = {
                        viewModel.setAlertType(type = null)
                        onRedirectToBuyFlax()
                    }
                )
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .verticalScroll(state = scrollState)
    ) {
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))

        if (viewState == MultiStateView.ViewState.LOADING) {
            CustomLinearProgressIndicator()
            return@Column
        }

        RestoreRatingFlixCoinDetailBackgroundView {
            RestoreRatingTitleDescriptionView(
                title = R.string.restore_rating_your_rating_title,
                value = restoreRatingDetails?.convertedRating ?: ""
            )
            HorizontalDashedLine(modifier = Modifier.padding(vertical = dimensionResource(id = R.dimen.element_spacing)))
            RestoreRatingTitleDescriptionView(
                title = R.string.restore_rating_cost_to_restore_rating_title,
                isFlix = true, value = restoreRatingDetails?.restoratingFlix.formatDecimalWithRemoveTrailingZeros()
            )
        }

        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.element_spacing))
        RestoreRatingFlixCoinDetailBackgroundView {
            RestoreRatingTitleDescriptionView(
                title = R.string.restore_rating_your_flix_balance_title,
                isFlix = true, value = restoreRatingDetails?.flixBalance.formatDecimalWithRemoveTrailingZeros()
            )
            HorizontalDashedLine(modifier = Modifier.padding(vertical = dimensionResource(id = R.dimen.element_spacing)))
            RestoreRatingTitleDescriptionView(
                title = R.string.restore_rating_your_coins_balance_title,
                value = stringResource(id = R.string.restore_rating_coins, restoreRatingDetails?.coinBalance.formatDecimalWithRemoveTrailingZeros())
            )
            HorizontalDashedLine(modifier = Modifier.padding(vertical = dimensionResource(id = R.dimen.element_spacing)))
            RestoreRatingTitleDescriptionView(
                title = R.string.restore_rating_effective_flix_title,
                isFlix = true, value = restoreRatingDetails?.effectiveBalance.formatDecimalWithRemoveTrailingZeros()
            )
        }

        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        Text(
            text = stringResource(id = R.string.restore_rating_pay_flix_to_restore_title),
            style = FlashatComposeTypography.defaultType.subtitle2,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.line_spacing))
        Text(
            text = stringResource(id = R.string.restore_rating_description),
            style = FlashatComposeTypography.defaultType.caption,
            color = colorResource(id = R.color.textColorPrimary)
        )

        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        FlowRow(
            maxItemsInEachRow = 2,
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
            horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
        ) {
            repeat(times = viewModel.citizenShipAndDescription.size) {
                val item = viewModel.citizenShipAndDescription[it]
                CitizenshipLevelView(
                    modifier = Modifier
                        .fillMaxRowHeight()
                        .fillMaxWidth()
                        .weight(weight = 1F),
                    citizenship = item.first,
                    description = stringResource(id = item.second)
                )
            }
        }

        if (restoreRatingDetails?.enableRestoreButton != true) {
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
            Text(
                text = stringResource(id = R.string.restore_rating_inSufficient_balance),
                style = FlashatComposeTypography.defaultType.caption,
                color = colorResource(id = R.color.colorError)
            )
        }

        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        CustomLargeRoundButton(
            text = R.string.performance_restore_rating,
            isEnabled = restoreRatingDetails?.enableRestoreButton == true,
            onClick = {
                viewModel.setAlertType(
                    type = if (restoreRatingDetails?.sufficientBalance == true) RestoreRatingViewModel.RestoreRatingAlertType.RestoreRatingSufficientBalance
                    else if (restoreRatingDetails?.sufficientEffectiveBalance == true) RestoreRatingViewModel.RestoreRatingAlertType.RestoreRatingSufficientEffectiveBalance
                    else RestoreRatingViewModel.RestoreRatingAlertType.InsufficientFlixBalance
                )
            }
        )
        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
    }
}