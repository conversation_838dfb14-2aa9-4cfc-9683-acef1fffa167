package com.app.messej.ui.home.common

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.RuleConstants
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

abstract class BaseHuddleListViewModel(application: Application) : AndroidViewModel(application) {

    protected val accountRepo = AccountRepository(application)
    protected val huddleRepo = HuddlesRepository(application)
    protected val eventRepo = HuddleChatEventRepository
    protected val profileRepo = ProfileRepository(application)

    protected val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    protected var huddleType: MutableLiveData<HuddleType?> = MutableLiveData(null)

    val user: CurrentUser get() = accountRepo.user

    val isVisitor: Boolean
        get()= user.citizenship.isVisitor
    val isResident: Boolean
        get()= user.citizenship.isResident
    val isCitizen: Boolean
        get()= user.citizenship.isCitizen

    // Selection Mode
    private val _huddleSelectionMode = MutableLiveData(false)
    val huddleSelectionMode: LiveData<Boolean> = _huddleSelectionMode.distinctUntilChanged()

    protected var _selectedHuddleList: MutableList<PublicHuddle> = mutableListOf()
    protected val _selectedHuddles = MutableLiveData< List < PublicHuddle > >(listOf())
    val selectedHuddles: LiveData< List < Int > > = _selectedHuddles.map { return@map it.map { huddle-> huddle.id }}

    val actionIsPin = _selectedHuddles.map {
        it.all { huddle -> !huddle.pinned }
    }

    val pinActionIsVisible = _selectedHuddles.map {
        it.all { huddle -> !huddle.isTribe }
    }
    val actionIsMute = _selectedHuddles.map {
        it.all { huddle -> !huddle.muted }
    }

    val onItemChange = LiveEvent<Int>()

    fun enterSelectionMode(huddle: PublicHuddle, pos: Int) {
        if (_huddleSelectionMode.value==false) {
            Log.d("BHLVM", "enterSelectionMode: ")
            _huddleSelectionMode.value = true
            _selectedHuddleList = mutableListOf()
        }
        selectHuddle(huddle,pos)
    }

    fun exitSelectionMode() {
        Log.d("BHLVM", "exitSelectionMode: ")
        _huddleSelectionMode.value = false
        _selectedHuddleList = mutableListOf()
        _selectedHuddles.value = _selectedHuddleList.toList()
    }

    fun selectHuddle(huddle: PublicHuddle, pos: Int): Boolean {
        if(_huddleSelectionMode.value == false) return false
        _selectedHuddleList.apply {
            if(!removeIf { huddle.id==it.id }) {
                add(huddle)
            }
            _selectedHuddles.value = toList()
        }
        if(_selectedHuddleList.isEmpty()) {
            exitSelectionMode()
        }
//        onItemChange.postValue(pos)
        return true
    }

    val onPinLimitExceed = LiveEvent<Int>()
    val onPinMuteAction = LiveEvent<HuddleAction>()

    fun togglePin() {
        val list = _selectedHuddleList.filter {
            it.pinned != actionIsPin.value
        }.map { it.id }
        val action = if (actionIsPin.value!!) HuddleAction.PIN else HuddleAction.UNPIN
        doHuddleAction(list, action)
    }

    fun toggleMute() {
        val list = _selectedHuddleList.filter {
            it.muted != actionIsMute.value
        }.map { it.id }
        val action = if (actionIsMute.value!!) HuddleAction.MUTE else HuddleAction.UNMUTE
        doHuddleAction(list, action)
    }

    protected open fun canPinHuddles(ids: List<Int>, type: HuddleType): Boolean {
       return huddleRepo.canPinHuddles(ids, type)
    }

    abstract suspend fun performHuddleAction(ids: List<Int>, action: HuddleAction, type: HuddleType, involvement: HuddleInvolvement?=null): ResultOf<String>

    val onHuddleActionError = LiveEvent<String>()

    private fun doHuddleAction(list: List<Int>, action: HuddleAction) {
        _actionLoading.postValue(true)
        val type = huddleType.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            if (action==HuddleAction.PIN) {
                if(!canPinHuddles(list,type)) {
                    onPinLimitExceed.postValue(RuleConstants.MAX_PINNED_HUDDLES)
                    _actionLoading.postValue(false)
                    return@launch
                }
            }
            when (val result = performHuddleAction(list, action, type)) {
                is ResultOf.Success -> {
                    onPinMuteAction.postValue(action)
                    withContext(Dispatchers.Main) {
                        exitSelectionMode()
                    }
                }
                is ResultOf.APIError -> {
                    if (result.code==400) {
                        onHuddleActionError.postValue(result.error.message)
                    }
                }
                is ResultOf.Error -> {
                }
            }
            _actionLoading.postValue(false)
        }
    }
}