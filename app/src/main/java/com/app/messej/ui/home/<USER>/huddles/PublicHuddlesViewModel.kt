package com.app.messej.ui.home.publictab.huddles

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.insertHeaderItem
import androidx.paging.insertSeparators
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.huddles.HuddleEligibilityResponse
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.common.BaseHuddleListViewModel
import com.app.messej.ui.home.publictab.huddles.PublicHuddlesAdapter.HuddleUIModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PublicHuddlesViewModel(application: Application) : BaseHuddleListViewModel(application) {

    private val _currentTab = MutableLiveData<HuddleTab?>(null)
    val currentTab: LiveData<HuddleTab?> = _currentTab.distinctUntilChanged()

    val showCreateHuddleFab = LiveEvent<Boolean>()
    private val tabItemCounts = mutableMapOf<HuddleTab, Int>()
    fun setTabDataCount(tab: HuddleTab, count: Int) {
        tabItemCounts[tab] = count
        refreshFabVisibility()
    }

    private fun refreshFabVisibility() {
        var show = false
        currentTab.value?.let { tab ->
            show = tab==HuddleTab.TAB_MINE && (tabItemCounts[tab]?:0)>0
        }
        Log.w("FAB", "refreshFabVisibility: $show | ${currentTab.value} | $tabItemCounts" )
        showCreateHuddleFab.postValue(show)
    }

    private val currentTabInvolvement: HuddleInvolvement?
        get() {
            return when (currentTab.value) {
                HuddleTab.TAB_MINE -> HuddleInvolvement.MANAGER
                HuddleTab.TAB_ADMIN -> HuddleInvolvement.ADMIN
                HuddleTab.TAB_JOINED -> HuddleInvolvement.PARTICIPANT
                else -> null
            }
        }

    fun setCurrentTab(tab: HuddleTab, skipIfSet: Boolean = false) {
        Log.w("PHVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
        refreshFabVisibility()
    }

    init {
        getHuddlesRequestCount()
        getHuddleMyPostSummary()
        huddleType.postValue(HuddleType.PUBLIC)
    }

    private val _huddleMineList = huddleRepo.getPublicMineHuddlesPager().liveData.cachedIn(viewModelScope)

    private val _huddleMyPostSummary = MutableLiveData<PublicHuddle?>(null)

    fun getHuddleMyPostSummary() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.getMyPostSummary()) {
                is ResultOf.Success -> {
                    _huddleMyPostSummary.postValue(result.value)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val huddleMineList: MediatorLiveData<PagingData<HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<HuddleUIModel>?>(null)
        fun updateHuddleList() {
            _dataLoading.value = false
            var list: PagingData<HuddleUIModel>? = _huddleMineList.value?.map { item ->
                HuddleUIModel.LocalHuddleUIModel(item,_selectedHuddleList.find { return@find it.id==item.id }!=null)
            }
            list = list?.insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
                if (before == null && after == null) {
                    HuddleUIModel.MyHuddlesEmptyModel
                } else null
            }
            _huddleMyPostSummary.value?.also {
                list = list?.insertHeaderItem(
                    item = HuddleUIModel.MyPostsUIModel(it), terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE
                )
            }?: run {
                list = list?.insertHeaderItem(
                    item = HuddleUIModel.MyPostsEmptyUIModel, terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE
                )
            }

            huddleMineList.postValue(list)
        }
        med.addSource(_huddleMineList) { updateHuddleList() }
        med.addSource(_huddleMyPostSummary) { updateHuddleList() }
        med.addSource(_selectedHuddles) { updateHuddleList() }
        med
    }

    private val _huddleAdminList = huddleRepo.getPublicAdminHuddlesPager().liveData.cachedIn(viewModelScope)

    val huddleAdminList: MediatorLiveData<PagingData<HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<HuddleUIModel>?>(null)
        fun updateHuddleList() {
            val list: PagingData<HuddleUIModel>? = _huddleAdminList.value?.map { item ->
                HuddleUIModel.LocalHuddleUIModel(item,_selectedHuddleList.find { return@find it.id==item.id }!=null)
            }
            huddleAdminList.postValue(list)
        }
        med.addSource(_huddleAdminList) { updateHuddleList() }
        med.addSource(_selectedHuddles) { updateHuddleList() }
        med
    }


    private val _huddleJoinedList = huddleRepo.getPublicJoinedHuddlesPager().liveData.cachedIn(viewModelScope)

    val huddleJoinedList: MediatorLiveData<PagingData<HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<HuddleUIModel>?>(null)
        fun updateHuddleList() {
            _dataLoading.value = false
            val list: PagingData<HuddleUIModel>? = _huddleJoinedList.value?.map { item ->
                HuddleUIModel.LocalHuddleUIModel(item,_selectedHuddleList.find { return@find it.id==item.id }!=null)
            }
            huddleJoinedList.postValue(list)
        }
        med.addSource(_huddleJoinedList) { updateHuddleList() }
        med.addSource(_selectedHuddles) { updateHuddleList() }
        med
    }

    val huddleSuggestionList = huddleRepo.getHuddlesSuggestions("").liveData.map {
        it.map<SuggestedHuddle,HuddleUIModel> { item ->
            HuddleUIModel.SuggestedHuddleUIModel(item)
        }
    }.cachedIn(viewModelScope)

    val huddleRequests =  huddleRepo.getHuddlesInvitations().liveData.map {
        it.map<PublicHuddle,HuddleUIModel> { item ->
            HuddleUIModel.LocalHuddleUIModel(item)
        }
    }.cachedIn(viewModelScope)

    private val _huddleRequestCount = MutableLiveData<Int?>()
    val huddleRequestCount: LiveData<Int?> = _huddleRequestCount

    fun getHuddlesRequestCount() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.getHuddleRequestAndInvitesCount(true)) {
                is ResultOf.Success -> {
                    _huddleRequestCount.postValue(result.value)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    override fun canPinHuddles(ids: List<Int>, type: HuddleType): Boolean {
        return huddleRepo.canPinHuddles(ids, type, currentTabInvolvement )
    }

    override suspend fun performHuddleAction(ids: List<Int>, action: HuddleAction, type: HuddleType, involvement: HuddleInvolvement?): ResultOf<String> {
        return huddleRepo.performHuddleAction(ids, action, type, currentTabInvolvement)
    }

     val huddleEligibility = LiveEvent<Boolean?>()

    fun checkHuddleCreationLimit(){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HuddleEligibilityResponse> = huddleRepo.getHuddleCreateEligible()) {
                is ResultOf.Success -> {
                    huddleEligibility.postValue(result.value.isEligible)
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }


    val showSuccess = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()
    val inSufficientBalance = LiveEvent<Boolean>()

    fun purchaseCreateHuddle() {
        viewModelScope.launch(Dispatchers.IO) {
                when (val result: ResultOf<APIResponse<String>> = huddleRepo.purchaseHuddleCreation()) {
                    is ResultOf.Success -> {
                        showSuccess.postValue(result.value.message)
                        profileRepo.getAccountDetails()
                    }

                    is ResultOf.APIError -> {
                        if (result.code == 400) inSufficientBalance.postValue(true)
                        else errorMessage.postValue(result.error.message)

                    }

                    is ResultOf.Error -> {
                        errorMessage.postValue(result.toString())
                    }
                }


        }
    }
}