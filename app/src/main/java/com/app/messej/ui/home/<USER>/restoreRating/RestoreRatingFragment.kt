package com.app.messej.ui.home.settings.restoreRating

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentRestoreRatingBinding
import com.app.messej.databinding.LayoutPodiumCameraDisabledBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class RestoreRatingFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentRestoreRatingBinding
    private val viewModel: RestoreRatingViewModel by viewModels()
    private var apiLoader : MaterialDialog? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_restore_rating, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.apply {
            title = getString(R.string.restore_rating_header).uppercase()
            setNavigationOnClickListener { findNavController().popBackStack() }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun observe() {
        viewModel.onRestoreRatingSuccess.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        viewModel.isApiLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }
        viewModel.onError.observe(viewLifecycleOwner) {
            showToast(message = it)
        }
    }

    private fun setup() {
        binding.composeView.setContent {
            RestoreRatingScreen(
                viewModel = viewModel,
                onRedirectToBuyFlax = {
                    findNavController().navigateSafe(RestoreRatingFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = false))
                }
            )
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_restore_rating, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when(menuItem.itemId) {
            R.id.menu_restore_rating_info -> {
                restoreRatingInfo(resources.getString(R.string.restore_rating_note))
            }
        }
        return true
    }

    private fun restoreRatingInfo(header: String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = header
            val header = view.layoutHeader
            header.setPadding(header.paddingLeft, header.paddingTop, header.paddingRight,2)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun showAPILoader() {
        apiLoader = showLoader()
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

}