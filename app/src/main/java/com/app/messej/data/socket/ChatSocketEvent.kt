package com.app.messej.data.socket

enum class ChatSocketEvent(override val key: String): SocketEvent {
    // Public Huddles
    RX_TX_PUBLIC_HUDDLE_CHAT_MESSAGE("huddle_public_chat"),
    RX_TX_PRIVATE_GROUP_CHAT_MESSAGE("huddle_chat"),
    RX_TX_HUDDLE_CHAT_TYPING("huddle_chat_typing"),
    RX_HUDDLE_CHAT_USER_COUNT("huddle_user_count_update"),
    TX_HUDDLE_CHAT_READ_ALL("chat_read_all"),
    TX_HUDDLE_CHAT_READ("huddle_chat_read"),
    RX_HUDDLE_CHAT_DELIVERED_ALL("huddle_chat_delivered_all"),
    TX_HUDDLE_CHAT_ENTER("huddle_room"),
    RX_TX_HUDDLE_CHAT_LIKE("like_huddle_message"),
    RX_TX_HUDDLE_CHAT_DELETE("huddle_chat_delete"),
    RX_TX_HUDDLE_CHAT_GOT_BLOCKED("huddle_user_blocked_status"),
    RX_HUDDLE_UPDATE("huddle_update"),
    RX_HUDDLE_DELETE("huddle_delete"),
    RX_HUDDLE_ADMIN_STATUS_UPDATE("huddle_admin_status_update"),
    RX_HUDDLE_MEMBER_REMOVED("huddle_member_removed"),
    TX_HUDDLE_DELIVERY_STATUS("huddle_chat_delivery_status"),

    RX_TX_BROADCAST("broadcast"),
    RX_TX_BROADCAST_STAR("starred_broadcast"),
    TX_BROADCAST_UNSTAR_ALL("unstar_all_broadcast"),
    RX_TX_BROADCAST_LIKE("like-broadcast"),
    RX_TX_BROADCAST_DELETE("delete_broadcast"),
    TX_BROADCAST_READ("broadcast_read"),

    RX_TX_PRIVATE_CHAT_MESSAGE("private_chat"),
    RX_TX_PRIVATE_CHAT_MESSAGE_RESTRICTED("private_chat_request"),
    RX_TX_PRIVATE_CHAT_ONLINE_STATUS("online-status"),
    RX_TX_PRIVATE_CHAT_BLOCK("chat_block"),
    RX_TX_PRIVATE_CHAT_DELETE("chat_delete"),

    RX_TX_PRIVATE_CHAT_READ_STATUS("chat_read_status"),
    RX_TX_PRIVATE_CHAT_READ_ALL("chat_read_all"),
    RX_TX_PRIVATE_CHAT_DELIVERY_STATUS("chat_delivery_status"),
    RX_PRIVATE_CHAT_DELIVERED_ALL("chat_delivered_all"),
    RX_PRIVATE_CHAT_THREAD_DELETE("chat_thread_delete"),
    RX_TX_PRIVATE_CHAT_IGNORE("chat_ignore"),
    RX_PRIVATE_CHAT_ROOM_UPDATE("chat_room_update"),

    RX_PROFILE_UPDATE("user_profile_update"),
    RX_BLOCK_ACCOUNT("block_account"),
    RX_USER_EMPOWERMENT_UPDATE("user_empowerment_update"),
    RX_COUNTRY_FLAG_UPDATE("location_country_flag"),
    RX_FLASH_USER_BLOCKED("user_functionality_blocker"),

    RX_NOTIFICATION_COUNT_STATUS("notification_count"),

    RX_TX_HIDE_UNHIDE_SUPERSTAR("hide-superstar"),
    RX_TX_MUTE_UNMUTE_SUPERSTAR("mute-broadcast"),
    RX_HUDDLE_POLL_DELIVERED_ALL("poll_answered"),
    RX_FLAX_UPDATE("flax_update"),
    RX_GIFT_RECEIVE("user_gift_transfer"),
    RX_FLAX_TRANSFER("flax_transfer"),
    RX_HUDDLE_GIFT("huddle_post_gift_count"),
    RX_TX_HUDDLE_VOICE_CHAT("audio_playback_initiated"),
    RX_CHALLENGE_CONTRIBUTOR_APPOINT("appoint_challenge_contributor"), //when the facilitator sends contributor request (not applicable for all challenges)
    RX_SUBSCRIPTION_UPDATE("subscription_update"),
    RX_USER_LEVEL_UPGRADE("user_level_upgrade"),
    RX_NEW_PRESIDENT_CROWNED("new_president_crowned"),
    RX_MAIDAN_SUPPORTER_INVITE("maidan_supporter_invite"),
    RX_MAIDAN_COUNT_UPDATE("maidan_count_update"),
    RX_TX_CITIZENSHIP_LEVEL_UPGRADE("user_citizenship_upgrade"),

    RX_ANNOUNCEMENT("promobar_announcements"),
    RX_USER_ENFORCEMENTS("user_enforcements_updated"),
    PENDING_FINE_UPDATES("pending_fines_update"),
    E_TRIBE_SUPER_STAR_MESSAGE("e_tribe"),
    CITIZENSHIP_VIEW_ACKNOWLEDGEMENT("citizenship_view_acknowledgement"),
    ADMIN_ADDED_FLIX_COINS("pp_update"),
    ;

    companion object {
        private val map = ChatSocketEvent.entries.associateBy(ChatSocketEvent::key)
        fun from(key: String) = map[key]
    }
}