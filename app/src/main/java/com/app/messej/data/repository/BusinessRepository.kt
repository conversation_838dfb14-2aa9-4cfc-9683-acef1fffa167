package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.BuyFlaxRates
import com.app.messej.data.model.OtpRequestResponse
import com.app.messej.data.model.PaymentRequest
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.AppReviewResponse
import com.app.messej.data.model.api.DealsBeneficiaryListResponse
import com.app.messej.data.model.api.DealsBuyFlaxRatesResponse
import com.app.messej.data.model.api.DealsTransferPurposeResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.PayOutUserDetailSubmitResponse
import com.app.messej.data.model.api.PaymentMethodFormResponse
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.data.model.api.PayoutRequest
import com.app.messej.data.model.api.RatingStatisticsResponse
import com.app.messej.data.model.api.SentFlaxRequest
import com.app.messej.data.model.api.UpgradeUserLevelRequest
import com.app.messej.data.model.api.UpgradeUserLevelResponse
import com.app.messej.data.model.api.UserDetailResponse
import com.app.messej.data.model.api.UserDetailpayoutResponse
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.api.business.BusinessPayoutRequest
import com.app.messej.data.model.api.business.BusinessVerifyOtp
import com.app.messej.data.model.api.business.BusinessVerifyRequest
import com.app.messej.data.model.api.business.FinalPaymentResponse
import com.app.messej.data.model.api.business.GetRestoreRatingResponse
import com.app.messej.data.model.api.business.PayoutEligibility
import com.app.messej.data.model.api.business.PayoutHistory
import com.app.messej.data.model.api.business.SendFlaxResponse
import com.app.messej.data.model.api.business.SubmittedPaymentFormResponse
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessPayoutHistory
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.model.enums.FlixPurchaseTypesTab
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.model.enums.TaskOneEmptyMode
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.data.repository.mediators.PayoutHistoryListRemoteMediator
import com.app.messej.data.repository.mediators.TransferHistoryRemoteMediator
import com.app.messej.data.repository.pagingSources.DealsBuyFlaxPurchaseHistoryDataSource
import com.app.messej.data.repository.pagingSources.PayoutHistoryDataSource
import com.app.messej.data.repository.pagingSources.TransactionHistoryRemoteDataSource
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.AuthUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.google.gson.Gson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File

class BusinessRepository(context: Application) {

    private var database = FlashatDatabase.getInstance(context)
    private var dao = database.getBusinessDao()

    suspend fun getBusinessStatements(): ResultOf<BusinessStatement> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBusinessStatements()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                dao.insertStatements(result.value)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getBusinessActivityDetails(operation: BusinessOperation): ResultOf<BusinessActivityStatus> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBusinessActivityDetails()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                val activityDetails = result.value
                dao.apply {
                    operation.apply {
                        huddlesCount = activityDetails.huddles?.currentHuddlesCount
                        newHuddlePercentage = activityDetails.huddles?.huddlePercentage
                        requirementTargets?.huddlesTarget = activityDetails.huddles?.requiredHuddleCount
                        fans = activityDetails.customers?.currentFans
                        newFansPercentage = activityDetails.customers?.customersPercentage?.toInt()
                        requirementTargets?.fansTarget = activityDetails.customers?.requiredFans
                    }
                    insertOperations(operation)
                    insertActivityStatus(result.value)

                }

            }


            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getPayoutHistory(): Pager<Int, PayoutHistory> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PayoutHistoryDataSource(APIServiceGenerator.createService(BusinessAPIService::class.java)) })
    }

//    suspend fun getBusinessOperations(): ResultOf<BusinessOperation> {
//        return try {
//            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBusinessOperations()
//            val result = APIUtil.handleResponse(resp)
//            if (result is ResultOf.Success) {/*  dao.insertOperations(result.value)*/
//                ResultOf.Success(result)
//            }
//            result
//        } catch (e: Exception) {
//
//            ResultOf.Error(Exception(e))
//        }
//    }

    suspend fun getFlashAtActivityDetails(): ResultOf<BusinessActivityStatus> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBusinessActivityDetails()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                dao.insertActivityStatus(result.value)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }


    suspend fun getBusinessTaskOne(): ResultOf<BusinessTaskOne> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBusinessTaskOne()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                dao.insertBusinessTaskOne(result.value)
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getTaskOneDetails() = dao.getBusinessTaskOne()

    fun getStatements() = dao.getStatements()
    suspend fun verifyPaypalId(
        userMobile: String,
        userEmail: String,
        userAbout: String,
        enteredMobile: String,
        enteredEmail: String,
        enteredAbout: String,
        resend: Boolean,
        countryCode: String,
        mode: TaskOneEmptyMode,
    ): ResultOf<APIResponse<VerifyResponse>> {
        return try {

            val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(enteredMobile)
            val formattedCountryCode = UserInfoUtil.addPlusToCountryCode(countryCode)

            val updatedBusinessVerifyRequest = when (mode) {
                TaskOneEmptyMode.EMPTY_EMAIL -> BusinessVerifyRequest(
                    paypalId = userEmail, email = userEmail.ifEmpty { enteredEmail }, countryCode = formattedCountryCode, identity = AuthUtil.encryptAES(enteredEmail), resend = resend
                )

                TaskOneEmptyMode.EMPTY_MOBILE -> BusinessVerifyRequest(
                    phone = userMobile.ifEmpty { enteredMobile.replace(" ", "") },
                    countryCode = formattedCountryCode,
                    identity = AuthUtil.encryptAES("$formattedCountryCode $phoneNumberFormatted"),
                    resend = resend
                )

                TaskOneEmptyMode.EMPTY_ABOUT -> BusinessVerifyRequest(paypalId = userEmail, about = userAbout.ifEmpty { enteredAbout })

                TaskOneEmptyMode.EMPTY_EMAIL_AND_ABOUT -> BusinessVerifyRequest(paypalId = userEmail,
                                                                                email = userEmail.ifEmpty { enteredEmail },
                                                                                about = userAbout.ifEmpty { enteredAbout },
                                                                                identity = AuthUtil.encryptAES(enteredEmail),
                                                                                resend = resend
                )

                TaskOneEmptyMode.EMPTY_MOBILE_AND_ABOUT -> BusinessVerifyRequest(paypalId = userEmail,
                                                                                 about = userAbout.ifEmpty { enteredAbout },
                                                                                 phone = userMobile.ifEmpty { enteredMobile.replace(" ", "") },
                                                                                 countryCode = formattedCountryCode,
                                                                                 identity = AuthUtil.encryptAES("$formattedCountryCode $phoneNumberFormatted"),
                                                                                 resend = resend
                )

                else -> throw Exception("Wrong method called")
            }

            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).verifyPaypalId(updatedBusinessVerifyRequest)
            val result = APIUtil.handleResponseWithMessage(resp)

            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {
//            Firebase.crashlytics.recordException(e)
            if (e.message.equals(Constants.ERROR_RESPONSE_BODY_MISSING)) {
                ResultOf.APIError(ErrorResponse(Constants.ERROR_RESPONSE_BODY_MISSING), 400)
            } else {
                ResultOf.Error(Exception(e))
            }

        }
    }

    suspend fun verifyOtp(otp: String, paypalId: String): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).verifyOtp(BusinessVerifyOtp(paypalId = paypalId, otp = otp.toInt()))
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getOperations() = dao.getOperations()

    fun getFlashAtActivity() = dao.getActivityStatus()

    @OptIn(ExperimentalPagingApi::class)
    fun getPayoutHistoryListPager(): Pager<Int, BusinessPayoutHistory> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = PayoutHistoryListRemoteMediator(database, APIServiceGenerator.createService(BusinessAPIService::class.java)),
                     pagingSourceFactory = { database.getBusinessDao().PayoutHistoryListPagingSource() })
    }

    suspend fun payoutRequest(points: Double): ResultOf<PayoutRequest> {
        return try {
            val req = BusinessPayoutRequest(
                requestedPointsForReview = points
            )
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).payoutRequest(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun resendRequest(paypalId: String): ResultOf<APIResponse<VerifyResponse>> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).resendOTP(BusinessVerifyRequest(paypalId = paypalId, resend = true))
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body() ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }

    }

    suspend fun uploadAppReviewImage(file: File?): ResultOf<AppReviewResponse> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).uploadAppReviewImage(imageBody)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun payOutEligibility(): ResultOf<PayoutEligibility> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getPayOutEligibility()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun verifyBusinessOtp(mode: OTPRequestMode, phone: String, countryCode: String, email: String, otp: String): ResultOf<VerifyOTPResponse> {
        return try {

            val response = when (mode) {
                OTPRequestMode.VERIFY_EMAIL -> {

                    val req = VerifyOTPRequest(
                        email = email, otp = otp.toInt()
                    )
                    APIServiceGenerator.createService(BusinessAPIService::class.java, false).verifyBusinessEmailOTP(req)
                }

                OTPRequestMode.VERIFY_MOBILE -> {
                    val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phone)

                    val req = VerifyOTPRequest(
                        countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), phone = phoneNumberFormatted, otp = otp.toInt()
                    )
                    APIServiceGenerator.createService(BusinessAPIService::class.java, true).verifyBusinessMobileOTP(req)

                }

                else -> throw Exception("Wrong method called")
            }
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
//            Firebase.crashlytics.recordException(e)
            ResultOf.Error(Exception(e))
        }

    }


    /** Deals Sent flax*/
    suspend fun sentFlax(receiverId: Int, purposeId: Int, flax: Double): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).postDealsSentFlax(
                SentFlaxRequest(
                    receiverId = receiverId, purposeId = purposeId, flax = flax
                ), true
            )
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent flax: ", e)
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun getFlaxSentDetails(receiverId: Int, purposeId: Int, flax: Double): ResultOf<SendFlaxResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getFlaxSentDetails(
                SentFlaxRequest(
                    receiverId = receiverId, purposeId = purposeId, flax = flax
                ), false
            )
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent flax: ", e)
            ResultOf.Error(Exception(e))
        }
    }


    /** Deals Sent flax Purpose list*/
    suspend fun getDealsTransferPurpose(): ResultOf<DealsTransferPurposeResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getDealsTransferPurpose()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    /** Deals Sent flax Beneficiary list*/
    suspend fun getBeneficiaryList(
        keyword: String,
        page: String? = null,
        sort: String = "created_on",
        sortOrder: String = "desc",
        statusFilter: String = "active",
        membershipFilter: String? = "Premium",
    ): ResultOf<DealsBeneficiaryListResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBeneficiaryList(sort, sortOrder, page, statusFilter, "50", keyword, membershipFilter)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getDealsTransferListPager(transferFilter: String): Pager<Int, DealsTransferHistory> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = TransferHistoryRemoteMediator("", database, APIServiceGenerator.createService(BusinessAPIService::class.java), transferFilter),
                     pagingSourceFactory = {

                         if (transferFilter.isNullOrEmpty()) {
                             database.getTransferHistoryDao().transferHistoryListPagingSource()
                         } else {
                             database.getTransferHistoryDao().transferHistoryListFilterPagingSource(transferFilter)
                         }

                     })
    }


    fun getDealsTransferListSearchPager(keyword: String, transferFilter: String): Pager<Int, DealsTransferHistory> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = {
                         TransactionHistoryRemoteDataSource(
                             APIServiceGenerator.createService(BusinessAPIService::class.java),
                             searchKeyWord = keyword,
                             transferFilter = transferFilter
                         )
                     })
    }


    /** Deals Buy Flax Rates*/
    suspend fun getDealsBuyCoinRates(): ResultOf<DealsBuyFlaxRatesResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getCoinRates("android")
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    /** Deals Buy Coin Rates*/
    suspend fun getDealsBuyFlixRates(): ResultOf<DealsBuyFlaxRatesResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getFlixRates("android")
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    /** Deals Buy Flax purchase History*/
    fun getDealsBuyFlaxPurchaseHistoryPager(purchaseItem: PurchaseItem,flixType: FlixPurchaseTypesTab?): Pager<Int, BuyFlaxRates> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { DealsBuyFlaxPurchaseHistoryDataSource(APIServiceGenerator.createService(BusinessAPIService::class.java), purchaseItem = purchaseItem, flixTabType = flixType) })
    }

    suspend fun getPayoutUserInfo(): ResultOf<UserDetailResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getPayoutUserInfo()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun submitPayoutUserDetails(
        idNumber: String,
        pincode: String,
        phoneNo: String,
        address: String,
        countryCode: String,
        countryCodeIso: String,
        city: String,
        lname: String,
        fname: String,
        persist: Boolean,
        file: File?,
        dbId: Int?,
        payoutId: Int?,
    ): ResultOf<PayOutUserDetailSubmitResponse> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }

            val request = UserDetailpayoutResponse(
                idNumber = idNumber,
                pincode = pincode,
                phone = phoneNo,
                address = address,
                countryCode = countryCode,
                countryCodeIso = countryCodeIso,
                city = city,
                lname = lname,
                fname = fname,
                persist = persist,
                id = dbId,
                payoutRequestId = payoutId
            )

            val dataBody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).submitPayoutUserDetails(imageBody, dataBody)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "submitPayoutUserDetails: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //otp
    suspend fun requestOTP(phone: String, country_code: String): ResultOf<OtpRequestResponse> {
        return try {
            val req = VerifyOTPRequest(phone, country_code)
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).requestOTP(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent otp: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //confirm otp
    suspend fun confirmOTP(phone: String, country_code: String, otp: Int): ResultOf<String> {
        return try {
            val req = VerifyOTPRequest(phone = phone, countryCode = country_code, otp = otp)
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).confirmOTP(req)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent otp: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //get payment methods
    suspend fun getPaymentMethods(): ResultOf<PaymentMethodResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getPaymentMethods()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent otp: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //get payment method forms
    suspend fun getPaymentMethodForm(paymentMethodId: String): ResultOf<PaymentMethodFormResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getPaymentForm(paymentMethodId)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent otp: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //submit payment method form
    suspend fun submitPaymentMethodForm(file: File?, data: List<PaymentRequest.Input>, paymentMethodId: Int?, isPersist: Boolean, id: Int?, payoutId: Int): ResultOf<FinalPaymentResponse> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData(id.toString(), file.name, requestFile)
            }
            val request = PaymentRequest(data, paymentMethodId!!, isPersist, payoutId)
            val databody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).submitPaymentForm(imageBody, databody)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    //get already submitted payment method details

    suspend fun getPaymentMethodDetails(paymentMethodId: String): ResultOf<SubmittedPaymentFormResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getSubmittedPaymentForm(paymentMethodId)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sent otp: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    //reject payout request
    suspend fun rejectPayoutRequest(id: String): ResultOf<String> {
        return try {
            val requestBody = mapOf("status" to "Cancelled")
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).rejectPayoutRequest(id, requestBody)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }
    fun getCount(): LiveData<Int> {
        return database.getTransferHistoryDao().getCount()
    }
    suspend fun getRestoreRatingDetails(): ResultOf<GetRestoreRatingResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getRestoreRatingDetails()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception restore rating: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun postRestoreRating(requiredRating:String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).submitRestoreRating(requiredRating)
            val result =    APIUtil.handleResponseWithoutResult(response)
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "restore rating exception: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getRatingStatistics() : ResultOf<RatingStatisticsResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getRatingStatistics()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getUserLevelUpgrade() : ResultOf<UpgradeUserLevelResponse> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).getUserLevelUpgrade()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun upgradeUserLevel(req: UpgradeUserLevelRequest) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(BusinessAPIService::class.java).upgradeUserLevel(req)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "upgrade user level exception: ", e)
            ResultOf.Error(Exception(e))
        }
    }

}






