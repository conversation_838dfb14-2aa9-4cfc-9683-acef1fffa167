package com.app.messej.data.api

import com.app.messej.data.model.OtpRequestResponse
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.AppReviewResponse
import com.app.messej.data.model.api.DealsBeneficiaryListResponse
import com.app.messej.data.model.api.DealsBuyFlaxRatesResponse
import com.app.messej.data.model.api.DealsTransferHistoryResponse
import com.app.messej.data.model.api.DealsTransferPurposeResponse
import com.app.messej.data.model.api.FlaxPurchaseHistoryResponse
import com.app.messej.data.model.api.PayOutUserDetailSubmitResponse
import com.app.messej.data.model.api.PaymentMethodFormResponse
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.data.model.api.PayoutHistoryResponse
import com.app.messej.data.model.api.PayoutRequest
import com.app.messej.data.model.api.RatingStatisticsResponse
import com.app.messej.data.model.api.SentFlaxRequest
import com.app.messej.data.model.api.UpgradeUserLevelRequest
import com.app.messej.data.model.api.UpgradeUserLevelResponse
import com.app.messej.data.model.api.UserDetailResponse
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.api.business.BusinessPayoutRequest
import com.app.messej.data.model.api.business.BusinessVerifyOtp
import com.app.messej.data.model.api.business.BusinessVerifyRequest
import com.app.messej.data.model.api.business.FinalPaymentResponse
import com.app.messej.data.model.api.business.FlaxPayOutHistoryResponse
import com.app.messej.data.model.api.business.GetRestoreRatingResponse
import com.app.messej.data.model.api.business.PayoutEligibility
import com.app.messej.data.model.api.business.SendFlaxResponse
import com.app.messej.data.model.api.business.SubmittedPaymentFormResponse
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.model.enums.FlixPurchaseTypesTab
import com.app.messej.data.model.status.BusinessActivityStatus
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface BusinessAPIService {

    @GET("/user/homeapp/pp_statement")
    suspend fun getBusinessStatements(): Response<APIResponse<BusinessStatement>>

    @GET("/user/flashatactivity")
    suspend fun getBusinessActivityDetails(): Response<APIResponse<BusinessActivityStatus>>

    @GET("/user/homeapp/operations/task23")
    suspend fun getBusinessOperations(): Response<APIResponse<BusinessOperation>>


    @GET("/user/homeapp/operations/task1")
    @Headers("Accept: application/json")
    suspend fun getBusinessTaskOne(): Response<APIResponse<BusinessTaskOne>>

    @PUT("/user/homeapp/operations/task1")
    @Headers("Accept: application/json")
    suspend fun verifyPaypalId(@Body verifyRequest: BusinessVerifyRequest): Response<APIResponse<VerifyResponse>>

    @POST("/user/homeapp/operations/payoutcredentials/confirmpaypal")
    @Headers("Accept: application/json")
    suspend fun verifyOtp(@Body verifyOtp: BusinessVerifyOtp): Response<APIResponse<Unit>>

    @GET("/user/homeapp/operations/payout_request")
    @Headers("Accept: application/json")
    suspend fun getPayoutHistoryList(
        @Query("page") page: Int
    ): Response<PayoutHistoryResponse>

    @POST("/user/homeapp/operations/payout_request")
    suspend fun payoutRequest(@Body request: BusinessPayoutRequest): Response<APIResponse<PayoutRequest>>

    @POST("/user/homeapp/operations/payoutcredentials/verifypaypal")
    suspend fun resendOTP(@Body verifyRequest: BusinessVerifyRequest): Response<APIResponse<VerifyResponse>>

    @Multipart
    @POST("/user/app_review/upload")
    suspend fun uploadAppReviewImage(@Part file: MultipartBody.Part?):Response<APIResponse<AppReviewResponse>>

    @GET("/user/homeapp/operations/payout_eligibility")
    @Headers("Accept: application/json")
    suspend fun getPayOutEligibility(): Response<APIResponse<PayoutEligibility>>

    @POST("/user/verify/phone")
    @Headers("Accept: application/json")
    suspend fun verifyBusinessMobileOTP(@Body req: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    @POST("/user/verify/email")
    @Headers("Accept: application/json")
    suspend fun verifyBusinessEmailOTP(@Body req: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    /**Deals Sent flax*/
    @POST("/user/homeapp/operations/flaxtransfer")
    @Headers("Accept: application/json")
    suspend fun getFlaxSentDetails(@Body req: SentFlaxRequest,
                                   @Query("send") send: Boolean): Response<APIResponse<SendFlaxResponse>>

    @POST("/user/homeapp/operations/flaxtransfer")
    @Headers("Accept: application/json")
    suspend fun postDealsSentFlax(@Body req: SentFlaxRequest,
                                  @Query("send") send: Boolean): Response<APIResponse<Unit>>

    /**Deals Transaction list*/
    @GET("/user/homeapp/operations/flaxtransfer")
    @Headers("Accept: application/json")
    suspend fun getTransferHistory(
        @Query("keyword") keyword: String,
        @Query("page") page: Int,
        @Query("page_count") pageCount: Int,
        @Query("transfer_filter") transferFilter: String,
    ): Response<APIResponse<DealsTransferHistoryResponse>>

    /**Deals Sent flax Purposes list*/
    @GET("/user/homeapp/operations/transferpurposes")
    @Headers("Accept: application/json")
    suspend fun getDealsTransferPurpose(): Response<APIResponse<DealsTransferPurposeResponse>>

    /**Deals Sent flax beneficiary list*/
    @GET("/user/homeapp/operations/beneficiarylist")
    @Headers("Accept: application/json")
    suspend fun getBeneficiaryList(
        @Query("sort") sort: String? = null,
        @Query("sort_order") sortOrder: String? = null,
        @Query("page") page: String? = null,
        @Query("status_filter[]") statusFilter: String? = null,
        @Query("page_count") pageCount: String? = null,
        @Query("keyword") keyword: String,
        @Query("membership_filter[]") membershipFilter: String? = null,
        @Query("maidan_invite") maidanInvite: Int? = null,
        @Query("prize") prize: Int? = null,
    ): Response<APIResponse<DealsBeneficiaryListResponse>>


    /** Deals Buy Coin*/
    @GET("/user/coins/buycoins")
    @Headers("Accept: application/json")
    suspend fun getCoinRates(@Query("type") type:String): Response<APIResponse<DealsBuyFlaxRatesResponse>>

    /** Deals Buy Flix*/
    @GET("/user/flax/buyflaxrates")
    @Headers("Accept: application/json")
    suspend fun getFlixRates(@Query("type") type:String): Response<APIResponse<DealsBuyFlaxRatesResponse>>

    /** Deals Buy Coin purchase History*/
    @GET("/user/coins/purchasehistory")
    @Headers("Accept: application/json")
    suspend fun getCoinPurchaseHistory(@Query("page") page: Int, @Query("limit") limit: Int,@Query("purchased_for")purchasedFor:FlixPurchaseTypesTab): Response<APIResponse<FlaxPurchaseHistoryResponse>>

    /** Deals Buy Flix purchase History*/
    @GET("/user/flax/purchased_history")
    @Headers("Accept: application/json")
    suspend fun getFlixPurchaseHistory(@Query("page") page: Int, @Query("limit") limit: Int,@Query("purchased_for") purchasedFor:FlixPurchaseTypesTab): Response<APIResponse<FlaxPurchaseHistoryResponse>>

    /** Payout user info*/
    @GET("/user/flax/sellflax/userdetails")
    @Headers("Accept: application/json")
    suspend fun getPayoutUserInfo(): Response<APIResponse<UserDetailResponse>>

    @Multipart
    @POST("/user/flax/sellflax/userdetails")
    @Headers("Accept: application/json")
    suspend fun submitPayoutUserDetails(@Part file: MultipartBody.Part?, @Part data: MultipartBody.Part): Response<APIResponse<PayOutUserDetailSubmitResponse>>

    /** Payment Methods **/
    @GET("/user/flax/sellflax/listpaymentmethods")
    @Headers("Accept: application/json")
    suspend fun getPaymentMethods(): Response<APIResponse<PaymentMethodResponse>>


    @GET("/user/homeapp/operations/payouthistory")
    @Headers("Accept: application/json")
    suspend fun getPayOutHistory(): Response<APIResponse<FlaxPayOutHistoryResponse>>

    //otp
    @POST("/user/flax/sellflax/verifyphone")
    @Headers("Accept: application/json")
    suspend fun requestOTP(@Body req: VerifyOTPRequest): Response<APIResponse<OtpRequestResponse>>

    //verify otp
    @POST("/user/flax/sellflax/confirmotp")
    @Headers("Accept: application/json")
    suspend fun confirmOTP(@Body req: VerifyOTPRequest): Response<APIResponse<Unit>>

    //get payment form get method
    @GET("/user/flax/sellflax/getpaymentform")
    @Headers("Accept: application/json")
    suspend fun getPaymentForm(@Query("payment_method_id")  paymentMethodId: String): Response<APIResponse<PaymentMethodFormResponse>>


    //submit payment form post method multipart form 2 parameters
    @Multipart
    @POST("/user/flax/sellflax/userpaymentdetails")
    @Headers("Accept: application/json")
    suspend fun submitPaymentForm(@Part file: MultipartBody.Part?, @Part data: MultipartBody.Part): Response<APIResponse<FinalPaymentResponse>>

    //load submitted payment form detail using GET
    @GET("/user/flax/sellflax/userpaymentdetails")
    @Headers("Accept: application/json")
    suspend fun getSubmittedPaymentForm(@Query("payment_method_id") paymentMethodId: String): Response<APIResponse<SubmittedPaymentFormResponse>>

    @PUT("/user/payoutrequest/{payoutRequestId}")
    @Headers("Accept: application/json")
    suspend fun rejectPayoutRequest(@Path("payoutRequestId") payoutRequestId: String,  @Body body: Map<String, String>) : Response<APIResponse<Unit>>

    @GET("/user/flax/restorerating")
    @Headers("Accept: application/json")
    suspend fun getRestoreRatingDetails(): Response<APIResponse<GetRestoreRatingResponse>>

    @POST("/user/flax/restorerating")
    @Headers("Accept: application/json")
    suspend fun submitRestoreRating(@Query("flix")requiredFlix:String) : Response<APIResponse<Unit>>

    @GET("/user/rating-statistics")
    @Headers("Accept: application/json")
    suspend fun getRatingStatistics() : Response<APIResponse<RatingStatisticsResponse>>

    @GET("/user/user-level-upgrade")
    @Headers("Accept: application/json")
    suspend fun getUserLevelUpgrade(): Response<APIResponse<UpgradeUserLevelResponse>>

    @POST("/user/user-level-upgrade")
    @Headers("Accept: application/json")
    suspend fun upgradeUserLevel(@Body req : UpgradeUserLevelRequest) : Response<APIResponse<Unit>>
}
