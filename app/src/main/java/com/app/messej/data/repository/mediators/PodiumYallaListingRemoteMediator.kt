package com.app.messej.data.repository.mediators

import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PodiumYallaListingRemoteMediator(
    private val podiumId: String,
    private val database: FlashatDatabase,
    private val networkService: PodiumAPIService,
)  : RemoteMediator<Int, YallaGuysChallenge>() {

    private val remoteKeyDao = database.getRemotePagingDao()
    private val dao = database.getYallaGuysDao()
    private val tableKey = "${EntityDescriptions.TABLE_YALLA_LIVE}-main"

    override suspend fun load(loadType: LoadType, state: PagingState<Int, YallaGuysChallenge>): MediatorResult {

        return try {
            val loadKey = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {

                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }

                    if (remoteKey.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }
            val response = networkService.getYallaChallengesList(podiumId ,page = loadKey)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            database.withTransaction {

                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAll()
                }

                val nextPage = if (result.hasNext) result.currentPage.plus(1) else result.currentPage

                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, nextPage.toString())
                )
                result.challenges.apply {
                    dao.insert(this)
                }
            }
            val ended = !result.hasNext || result.challenges.isEmpty() || result.currentPage>=result.totalPages
            MediatorResult.Success(endOfPaginationReached = ended)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}
