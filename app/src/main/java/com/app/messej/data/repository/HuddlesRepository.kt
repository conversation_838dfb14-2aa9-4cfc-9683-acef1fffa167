package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.liveData
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.RuleConstants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.api.PollsAPIService
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.PostCommentPayload
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.huddles.AcceptChatRequest
import com.app.messej.data.model.api.huddles.AddRemovedUserRequest
import com.app.messej.data.model.api.huddles.AddRemovedUserResponse
import com.app.messej.data.model.api.huddles.AdminInviteRequest
import com.app.messej.data.model.api.huddles.BlockUnblockHuddleUserRequest
import com.app.messej.data.model.api.huddles.BlockUnblockHuddleUserResponse
import com.app.messej.data.model.api.huddles.CreateHuddleRequest
import com.app.messej.data.model.api.huddles.HuddleActionRequest
import com.app.messej.data.model.api.huddles.HuddleActionResponse
import com.app.messej.data.model.api.huddles.HuddleBanRequest
import com.app.messej.data.model.api.huddles.HuddleCancelActionRequest
import com.app.messej.data.model.api.huddles.HuddleCancelInvitationRequest
import com.app.messej.data.model.api.huddles.HuddleCategoriesResponse
import com.app.messej.data.model.api.huddles.HuddleEligibilityResponse
import com.app.messej.data.model.api.huddles.HuddleForSale
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.api.huddles.HuddleInvitationsResponse
import com.app.messej.data.model.api.huddles.HuddleLanguage
import com.app.messej.data.model.api.huddles.HuddleMentionableUsersResponse
import com.app.messej.data.model.api.huddles.HuddleMuteRequest
import com.app.messej.data.model.api.huddles.HuddlePinRequest
import com.app.messej.data.model.api.huddles.HuddleRequestsAdminActionRequest
import com.app.messej.data.model.api.huddles.HuddleRequestsResponse
import com.app.messej.data.model.api.huddles.HuddleSuggestionResponse
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.api.huddles.PinHuddlePostReponse
import com.app.messej.data.model.api.huddles.PinHuddlePostRequest
import com.app.messej.data.model.api.huddles.PrivateChatUserInfo
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.data.model.api.huddles.PurchaseHuddleRequest
import com.app.messej.data.model.api.huddles.RemoveUserRequest
import com.app.messej.data.model.api.huddles.ReportedByResponse
import com.app.messej.data.model.api.huddles.SellHuddleRequest
import com.app.messej.data.model.api.huddles.SellHuddleResponse
import com.app.messej.data.model.api.huddles.SendInvitationsRequest
import com.app.messej.data.model.api.huddles.UnblockHuddleRequest
import com.app.messej.data.model.api.huddles.UnblockedHuddleResponse
import com.app.messej.data.model.api.huddles.UpdateHuddleRequest
import com.app.messej.data.model.api.poll.CreatePollRequest
import com.app.messej.data.model.api.poll.CreatePollResponse
import com.app.messej.data.model.entity.BlockedHuddle
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.HuddlePrivacySettings
import com.app.messej.data.model.entity.HuddlePrivacyStatus
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.data.model.entity.HuddleReportedMessageWithMedia
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.PrivacySetting
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.PublicHuddleInterventions
import com.app.messej.data.model.entity.PublicHuddleWithInterventions
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleActionType
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.ParticipantsSearchType
import com.app.messej.data.model.enums.PrivateMessageSuggestionType
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.repository.mediators.HuddleChatMessageRemoteMediator
import com.app.messej.data.repository.mediators.HuddleMyPostsRemoteMediator
import com.app.messej.data.repository.mediators.HuddlePostCommentRemoteMediator
import com.app.messej.data.repository.mediators.HuddleReportedCommentRemoteMediator
import com.app.messej.data.repository.mediators.HuddleReportedMessageRemoteMediator
import com.app.messej.data.repository.mediators.PrivateChatMessageRemoteMediator
import com.app.messej.data.repository.mediators.PrivateChatsRemoteMediator
import com.app.messej.data.repository.mediators.PublicHuddlesRemoteMediator
import com.app.messej.data.repository.mediators.SellHuddleListRemoteMediator
import com.app.messej.data.repository.pagingSources.BlockedHuddleDataSource
import com.app.messej.data.repository.pagingSources.HuddleInnerSearchDataSource
import com.app.messej.data.repository.pagingSources.HuddleInvitesDataSource
import com.app.messej.data.repository.pagingSources.HuddleParticipantsDataSource
import com.app.messej.data.repository.pagingSources.HuddleRequestInvitesDataSource
import com.app.messej.data.repository.pagingSources.HuddleRequestsDataSource
import com.app.messej.data.repository.pagingSources.HuddleSearchDataSource
import com.app.messej.data.repository.pagingSources.HuddleSuggestionDataSource
import com.app.messej.data.repository.pagingSources.PrivateMessageSearchDataSource
import com.app.messej.data.repository.pagingSources.PrivateMessageSuggestionDataSource
import com.app.messej.data.repository.pagingSources.ReportedParticipantsDataSource
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.util.UUID

class HuddlesRepository(private val context: Application) {

    private val accountRepo: AccountRepository = AccountRepository(context)

    private val db = FlashatDatabase.getInstance(context)

    /**
     * Get list of all huddles available for the user
     */
    @OptIn(ExperimentalPagingApi::class)
    private fun getPublicHuddlesPager(type: HuddleType, involvement: HuddleInvolvement? = null): Pager<Int, PublicHuddle> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = PublicHuddlesRemoteMediator("", type, involvement?:HuddleInvolvement.NONE, db, APIServiceGenerator.createService(ChatAPIService::class.java), accountRepo.user),
            pagingSourceFactory = { if (involvement==null) db.getHuddleDao().huddlesPagingSource(type) else db.getHuddleDao().huddlesPagingSource(type, involvement) }
        )
    }
    fun getPrivateGroupsPager() = getPublicHuddlesPager(HuddleType.PRIVATE)
    fun getPublicMineHuddlesPager() = getPublicHuddlesPager(HuddleType.PUBLIC, HuddleInvolvement.MANAGER)
    fun getPublicAdminHuddlesPager() = getPublicHuddlesPager(HuddleType.PUBLIC, HuddleInvolvement.ADMIN)
    fun getPublicJoinedHuddlesPager() = getPublicHuddlesPager(HuddleType.PUBLIC, HuddleInvolvement.PARTICIPANT)

    fun getHuddlesSearchPager(type: HuddleType, keyword: String, tab: HuddleInvolvement? = null): Pager<Int, PublicHuddle> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = { HuddleSearchDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), type, keyword, tab) }
        )
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getPrivateChatsPager(type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE,
                             tabType: PrivateChat.PrivateMessageTabType? = null) : Pager<Int,PrivateChat> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = PrivateChatsRemoteMediator("",type, db,APIServiceGenerator.createService(ChatAPIService::class.java), tabType),
            pagingSourceFactory = {
                if (tabType == null) db.getPrivateChatDao().chatPagingSource(type)
                else db.getPrivateChatDao().chatPagingSource(type, tabType) }
        )
    }

    fun getPrivateChatsPendingPager() = getPrivateChatsPager(PrivateChat.ChatType.REQUEST)

    suspend fun getHuddleInfo(huddleId : Int) : ResultOf<PublicHuddle> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleInfo(huddleId)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                val huddle = result.value
                huddle.huddleType = if(huddle.isPrivate) HuddleType.PRIVATE else HuddleType.PUBLIC
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getHuddleMentionableUsers(huddleId: Int) : ResultOf<HuddleMentionableUsersResponse> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).getHuddleMentionableUsers(huddleId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getHuddleInterventions(huddleId : Int) : ResultOf<PublicHuddleInterventions> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleInterventions(huddleId)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.getHuddleDao().insertHuddleInterventions(result.value)
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getFullHuddleInfo(huddleId: Int): ResultOf<HuddleInfo> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getFullHuddleInfo(huddleId)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.withTransaction {
                        db.getHuddleDao().apply {
                            val huddle = getHuddle(huddleId) ?: return@apply
                            result.value.let {
                                huddle.privacy = it.privacy
                                huddle.userStatus = it.userStatus
                                huddle.empoweredUserBlocked = it.empoweredUserBlocked
                                updateHuddle(huddle)
                            }
                        }
                    }
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun saveHuddle(huddle: PublicHuddle) {
        huddle.detectInvolvement()
        db.getHuddleDao().insertHuddle(huddle)
    }

    suspend fun updateHuddleMessageLike(id: String, liked: Boolean) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getChatMessageDao().apply {
                val huddle = getHuddleChatMessage(id)?: return@withTransaction
                updateChat(huddle.copy(
                    liked = liked,
                    totalLikes = if (liked) huddle.totalLikes+1 else (huddle.totalLikes-1).coerceAtLeast(0)
                ))
            }
        }
    }

    fun getPublicHuddleFlow(id: Int): Flow<PublicHuddle?> = db.getHuddleDao().getHuddleFlow(id).distinctUntilChanged()
    fun getPublicHuddleWithInterventionsFlow(id: Int): Flow<PublicHuddleWithInterventions?> = db.getHuddleDao().getHuddleWithInterventionsFlow(id).distinctUntilChanged()
    fun getPublicHuddle(id: Int): PublicHuddle? = db.getHuddleDao().getHuddle(id)
    suspend fun updateHuddleName(id: Int, newName: String) = db.getHuddleDao().updateHuddleName(id, newName)

    @OptIn(ExperimentalPagingApi::class)
    fun getHuddleChatsPager(id: Int, type: HuddleType, enableMediator: Boolean = false) : Pager<Int,HuddleChatMessageWithMedia> {
        Log.d("HUREP", "getHuddleChatsPager: remoteMediator is enabled? $enableMediator")
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = if (enableMediator) HuddleChatMessageRemoteMediator(id, type, db, APIServiceGenerator.createService(ChatAPIService::class.java), accountRepo) else null,
            pagingSourceFactory = { db.getChatMessageDao().groupChatsPagingSource(HuddleChatMessage.prefixHuddleId(id)) }
        )
    }

    fun getHuddlesInnerSearchPager(huddleId: Int?, keyword: String): Pager<Int, HuddleChatMessageWithMedia> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = { HuddleInnerSearchDataSource(APIServiceGenerator.createService(ChatAPIService::class.java),keyword,huddleId,accountRepo) }
        )
    }
    fun canPinHuddles(ids: List<Int>,type: HuddleType,involvement:HuddleInvolvement?=null): Boolean {
        val pinned = if (involvement==null) db.getHuddleDao().getPinnedHuddles(type) else db.getHuddleDao().getPinnedHuddles(type,involvement)
        return (pinned.size+ids.size)<=RuleConstants.MAX_PINNED_HUDDLES
    }

    suspend fun performHuddleAction(ids: List<Int>, action: HuddleAction, type: HuddleType,involvement:HuddleInvolvement? = null): ResultOf<String> {
        return try {
            if (action== HuddleAction.PIN && !canPinHuddles(ids,type,involvement)) {
                return ResultOf.getError("total pinned count exceeds max allowed")
            }
            val tabKey = if(type==HuddleType.PRIVATE) "group"
            else when(involvement) {
                HuddleInvolvement.MANAGER -> "my_huddles"
                HuddleInvolvement.ADMIN -> "user_admin"
                HuddleInvolvement.PARTICIPANT -> "joined_huddles"
                else -> ""
            }
            val response = when(action) {
                HuddleAction.PIN,HuddleAction.UNPIN -> APIServiceGenerator.createService(ChatAPIService::class.java).pinHuddles(HuddlePinRequest(ids,action, tabKey))
                HuddleAction.MUTE,HuddleAction.UNMUTE -> APIServiceGenerator.createService(ChatAPIService::class.java).muteHuddles(HuddleMuteRequest(ids, action))
            }
            val result = APIUtil.handleResponseWithoutResult(response)
            if(result is ResultOf.Success) {
                db.withTransaction {
                    ids.forEach { id ->
                        when (action) {
                            HuddleAction.PIN -> db.getHuddleDao().setHuddlePinned(id, true)
                            HuddleAction.UNPIN -> db.getHuddleDao().setHuddlePinned(id, false)
                            HuddleAction.MUTE -> db.getHuddleDao().setHuddleMuted(id, true)
                            HuddleAction.UNMUTE -> db.getHuddleDao().setHuddleMuted(id, false)
                        }
                    }
                }
            } else {
                db.withTransaction {
                    ids.forEach { id ->
                        when (action) {
                            HuddleAction.PIN -> db.getHuddleDao().setHuddlePinned(id, false)
                            HuddleAction.UNPIN -> db.getHuddleDao().setHuddlePinned(id, true)
                            HuddleAction.MUTE -> db.getHuddleDao().setHuddleMuted(id, false)
                            HuddleAction.UNMUTE -> db.getHuddleDao().setHuddleMuted(id, true)
                        }
                    }
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    // Private Chats
    fun getPrivateChatLiveData(id: String): LiveData<PrivateChat?> = db.getPrivateChatDao().getPrivateChatLiveData(id)
    suspend fun getPrivateChat(id: String) = db.getPrivateChatDao().getPrivateChat(id)
    suspend fun getPrivateChatWithRoomInfo(id: String) = db.getPrivateChatDao().getPrivateChatWithRoomInfo(id)
    fun getPrivateChatWithRoomInfoLiveData(id: String) = db.getPrivateChatDao().getPrivateChatWithRoomInfoLiveData(id)

    suspend fun insertRoomInfo(info: PrivateChatRoomInfo) {
        //add a check to make sure localId is never null
        info.localRoomId = info.chatRoom.id
        db.getPrivateChatDao().insert(info)
    }

    suspend fun getPrivateChatInfo(receiver: Int) : ResultOf<PrivateChat> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getPrivateChat(receiver)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }
    suspend fun savePrivateChat(chat: PrivateChat) = withContext(Dispatchers.IO) {
        db.getPrivateChatDao().insert(chat)
    }
     suspend fun getPrivateChatInfoForReceiver(receiver: Int) : ResultOf<PrivateChatRoomInfo> {
         return try {
             val response = APIServiceGenerator.createService(ChatAPIService::class.java).getPrivateChatInfo(receiver)
             val result = APIUtil.handleResponse(response)
             if (result is ResultOf.Success) {
                 db.withTransaction {
                     val roomInfo = result.value
                     db.getPrivateChatDao().apply {
                         val chat = getPrivateChat(roomInfo.chatRoom.id)
                         chat?.let {
                             update(chat.apply {
                                 followedByMe = roomInfo.followedByMe
                             })
                         }
                     }
                 }
             }
             result
         } catch (e: Exception) {
             ResultOf.Error(Exception(e))
         }
     }
    suspend fun getPrivateChatReceiverInfo(receiver: Int) : ResultOf<PrivateChatUserInfo> {
            return try {
                val response = APIServiceGenerator.createService(ChatAPIService::class.java).getPrivateUserInfo(receiver)
                val result = APIUtil.handleResponse(response)
                result
            } catch (e: Exception) {
                ResultOf.Error(Exception(e))
            }
        }

    suspend fun getPrivateChatRoomId(receiver: Int) = withContext(Dispatchers.IO) {
        val sender = accountRepo.user.id
        return@withContext PrivateChatMessage.getChatRoomId(sender, receiver)
    }

    suspend fun deletePrivateChat(roomId: String) {
        withContext(Dispatchers.IO) {
            db.getPrivateChatDao().delete(roomId)
            //TODO delete messages and media if required
        }
    }

    private fun defaultPagingConfig(pageSize: Int = 50) = PagingConfig(
        pageSize = pageSize,
        enablePlaceholders = false,
        prefetchDistance = 2,
        initialLoadSize = pageSize
    )

    @OptIn(ExperimentalPagingApi::class)
    fun getPrivateChatsPager(id: String) : Pager<Int,PrivateChatMessageWithMedia> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = PrivateChatMessageRemoteMediator(id, db, APIServiceGenerator.createService(ChatAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().privateChatsPagingSource(id) }
        )
    }

    suspend fun getLastReadMessageByUnreadCount(chat: PrivateChat): PrivateChatMessage? {
        if(chat.unreadCount>0) {
            return db.getChatMessageDao().getPrivateMessageAfterPosition(chat.id,chat.unreadCount)
        }
        return null
    }

    suspend fun getOldestUnreadMessage(chat: PrivateChat): PrivateChatMessage? {
        return db.getChatMessageDao().getOldestUnreadMessage(chat.id)
    }

    suspend fun resetPrivateChatUnreadCount(id: String) = withContext(Dispatchers.IO) {
        db.getPrivateChatDao().updateUnreadCount(id,0)
    }

    fun getHuddleRequestsList(huddleId: Int, totalCountCallback: (Int) -> Unit): Pager<Int,HuddleRequestsResponse.HuddleRequest> {
        return Pager(config = defaultPagingConfig(),
                     pagingSourceFactory = { HuddleRequestsDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), db.getUserDao(), huddleId, totalCountCallback) })
    }


    fun getHuddleInvitesList(huddleId: Int, totalCountCallback: (Int) -> Unit): Pager<Int,HuddleInvitationsResponse.HuddleInvitation> {
        return Pager(config = defaultPagingConfig(),
                     pagingSourceFactory = { HuddleInvitesDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), db.getUserDao(), huddleId, totalCountCallback) })
    }

    fun getHuddlesReportedMessagesParticipants(huddleId: Int, reportedId: Int, reportType: ReportToManagerType): Pager<Int,ReportedByResponse.ReportedParticipant> {
        return Pager(config = defaultPagingConfig(),
                     pagingSourceFactory = { ReportedParticipantsDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), accountRepo, huddleId, reportedId, reportType) })
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getHuddlePostComments(huddleId: Int, postId: String): Pager<Int,PostCommentItem> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = HuddlePostCommentRemoteMediator(huddleId, postId, db, APIServiceGenerator.createService(ChatAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().getHuddlePostComments(postId) }
        )
    }

    suspend fun writeComment(huddleId: Int, postId: String, body: PostCommentPayload) : ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).writePostComment(huddleId, postId, body)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteComment(huddleId: Int, postId: String, commentId: String) : ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).deletePostComment(huddleId, postId, commentId, "one")
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deletePostSingleComment(commentId: String) = db.getChatMessageDao().deletePostSingleComment(commentId)

    fun getNewMessageSearchSuggestionsList(suggestionType: PrivateMessageSuggestionType = PrivateMessageSuggestionType.TWO_WAY_FOLLOWED, userType: String = "", searchKeyword: String?): LiveData<PagingData<PrivateMessagesSuggestionResponse.User>> {
        return if (searchKeyword.isNullOrEmpty()) {
            Pager(config = defaultPagingConfig(),
                  pagingSourceFactory = { PrivateMessageSuggestionDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), accountRepo, suggestionType) }).liveData
        } else {
            Pager(config = defaultPagingConfig(), pagingSourceFactory = {
                PrivateMessageSearchDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), keyword = searchKeyword)
            }).liveData
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getHuddleReportedMessagePager(huddleId: Int): Pager<Int, HuddleReportedMessageWithMedia> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = HuddleReportedMessageRemoteMediator(huddleId, HuddleType.PUBLIC, db, APIServiceGenerator.createService(ChatAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().huddleReportedMessagePagingSource(huddleId) }
        )
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getHuddleReportedCommentPager(huddleId: Int): Pager<Int, HuddleReportedComment> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = HuddleReportedCommentRemoteMediator(huddleId, HuddleType.PUBLIC, db, APIServiceGenerator.createService(ChatAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().huddleReportedCommentPagingSource(huddleId) }
        )
    }
    suspend fun getHorizontalHuddleSuggestions(): ResultOf<HuddleSuggestionResponse>{
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleSuggestion()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getHuddlesSuggestions(searchKeyWord: String?) : Pager<Int,SuggestedHuddle> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = { HuddleSuggestionDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), searchKeyWord) }
        )
    }

    suspend fun getMyPostSummary(): ResultOf<PublicHuddle?> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getMyPostsSummary()
            APIUtil.handleResponse(response).convertTo { success ->
                return@convertTo ResultOf.Success(success.value.huddles.getOrNull(0))
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getMyPostsPager(postCountCallback: (Int, Int) -> Unit) : Pager<Int,HuddleChatMessageWithMedia> {
        return Pager(
            config = defaultPagingConfig(),
            remoteMediator = HuddleMyPostsRemoteMediator(db, APIServiceGenerator.createService(ChatAPIService::class.java),accountRepo, postCountCallback),
            pagingSourceFactory = { db.getChatMessageDao().myPostsPagingSource(accountRepo.user.id, HuddleType.PUBLIC) }
        )
    }

    fun getHuddlesInvitations() : Pager<Int,PublicHuddle> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = {HuddleRequestInvitesDataSource(APIServiceGenerator.createService(ChatAPIService::class.java)) }
        )
    }

    suspend fun getHuddleRequestAndInvitesCount(count: Boolean): ResultOf<Int> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleRequestAndInvites(count)
            val result = APIUtil.handleResponse(response)
            result.convertTo { success ->
                ResultOf.Success(success.value.participantTotal?:0)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun handleHuddleAdminRequestActions(action: HuddleActionType, huddle: PublicHuddle): ResultOf<String> {
        return try {
            val req = HuddleActionRequest(action = action)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).handleHuddleAdminRequestAction(huddle.id, req)
            val result = APIUtil.handleResponseWithoutResult(response)
            when(result){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    when(action){
                        HuddleActionType.ACCEPT_HUDDLE_INVITE -> {
                            db.withTransaction {
                                huddle.userStatus = AbstractHuddle.HuddleUserStatus.INVITE_ACCEPTED
                                huddle.involvement = HuddleInvolvement.PARTICIPANT
                                db.getHuddleDao().insertHuddle(huddle)
                            }
                        }
                        HuddleActionType.DECLINE_HUDDLE_INVITE -> {
                            db.getHuddleDao().deleteHuddle(huddle.id)
                        }
                        HuddleActionType.BLOCK_HUDDLE_INVITE -> {
                            db.getHuddleDao().deleteHuddle(huddle.id)
                        }
                        else -> {}
                    }

                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun handleHuddleCancelAction(huddle: PublicHuddle, memberId: Int): ResultOf<String> {
            return try {
                val req = HuddleCancelActionRequest(
                    action = HuddleActionType.CANCELLED_HUDDLE_REQUEST,
                    memberId = memberId
                )
                val response = APIServiceGenerator.createService(ChatAPIService::class.java).handleHuddleCancelAction(huddle.id, req)
                val result = APIUtil.handleResponseWithoutResult(response)
                when(result){
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                    is ResultOf.Success -> {
                        db.getHuddleDao().deleteHuddle(huddle.id)
                    }
                }
                result
            } catch (e: Exception) {
                ResultOf.Error(Exception(e))
            }
        }

    suspend fun huddleInstantJoinAction(huddle: PublicHuddle): ResultOf<HuddleActionResponse> {
        return try {
            db.withTransaction {
                db.getHuddleDao().insertHuddle(huddle)
            }

            val response = APIServiceGenerator.createService(ChatAPIService::class.java).huddleInstantJoin(huddle.id)
            val result = APIUtil.handleResponse(response)
            when(result){
                is ResultOf.APIError -> {
                    db.getHuddleDao().deleteHuddle(huddle.id)
                }
                is ResultOf.Error -> {
                    db.getHuddleDao().deleteHuddle(huddle.id)
                }
                is ResultOf.Success -> {
                    val resultHuddle = huddle.copy(
                        role = result.value.role,
                        userStatus = result.value.userStatus!!
                    )
                    resultHuddle.involvement = HuddleInvolvement.PARTICIPANT
                    db.getHuddleDao().updateHuddle(resultHuddle)
                }
            }
            result
        } catch (e: Exception) {
            db.getHuddleDao().deleteHuddle(huddle.id)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun huddleRequestJoinAction(onlineHuddle: PublicHuddle): ResultOf<HuddleActionResponse> {
        return try {
            db.getHuddleDao().insertHuddle(onlineHuddle)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).huddleRequestJoin(onlineHuddle.id)
            val result = APIUtil.handleResponse(response)
            when(result){
                is ResultOf.APIError -> {
                    db.getHuddleDao().deleteHuddle(onlineHuddle.id)
                }
                is ResultOf.Error -> {
                    db.getHuddleDao().deleteHuddle(onlineHuddle.id)
                }
                is ResultOf.Success -> {
                    val huddle: PublicHuddle = onlineHuddle.copy(
                        role = result.value.role,
                        userStatus = result.value.userStatus!!
                    )
                    huddle.involvement = HuddleInvolvement.NONE
                    db.getHuddleDao().updateHuddle(huddle)
                }
            }
            Log.d("GCVM", "joinHuddle result: $result")
            result
        } catch (e: Exception) {
            db.getHuddleDao().deleteHuddle(onlineHuddle.id)
            Log.d("GCVM", "joinHuddle exception: $e")
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun respondToAdminInvite(huddle: PublicHuddle, action: HuddleActionType): ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).handleHuddleAdminInvite(huddle.id, HuddleActionRequest(action))
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.withTransaction {
                        db.getHuddleDao().apply {
                            val newHuddle = huddle.copy(
                                adminStatus = when(action) {
                                    HuddleActionType.ACCEPT_ADMIN_REQUEST -> AbstractHuddle.HuddleAdminStatus.ACCEPTED
                                    HuddleActionType.DECLINE_ADMIN_REQUEST -> AbstractHuddle.HuddleAdminStatus.DECLINED
                                    HuddleActionType.IGNORE_ADMIN_REQUEST -> AbstractHuddle.HuddleAdminStatus.IGNORED
                                    else -> huddle.adminStatus
                                }
                            ).apply {
                                involvement = HuddleInvolvement.PARTICIPANT
                            }
                            Log.d("HREP", "respondToAdminInvite: updating Huddle: $newHuddle")
                            updateHuddle(newHuddle)
                        }
                    }
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getHuddleCategories(huddleType: HuddleType?) : ResultOf<HuddleCategoriesResponse>{
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleCategories(huddleType)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun createHuddle(uuid: UUID, file: File?,name: String, bio: String?, category: Int?, requestToJoin: Boolean, isPrivate: Boolean,language:String):
            ResultOf<PublicHuddle>{
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file!=null){
            val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
            imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)}

            val request = CreateHuddleRequest(
                uuid = uuid.toString(),
                name = name, about = bio, categoryId = category, requestToJoin = requestToJoin,
                isPrivate = isPrivate, language=language
            )

            val dataBody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).createHuddle(imageBody, dataBody)
            val result = APIUtil.handleResponse(response)
            when(result){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    val huddle = result.value
                    huddle.huddleType = if(isPrivate) HuddleType.PRIVATE else HuddleType.PUBLIC
                    huddle.involvement = HuddleInvolvement.MANAGER
                    huddle.managerPremium = accountRepo.isPremiumUser
                    db.getHuddleDao().insertHuddle(huddle)
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createHuddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteHuddle(huddleId: Int): ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).deleteHuddle(huddleId)
            val result = APIUtil.handleDeleteResponse(response)
            when (result) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    db.getHuddleDao().deleteHuddle(huddleId)
                    db.withTransaction {
                        db.getChatMessageDao().deleteAllHuddleChatMessages(HuddleChatMessage.prefixHuddleId(huddleId))
                    }
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "deleteHuddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun compressImage(file: File): File = MediaUtils.compressImageToTempFile(context, file.absolutePath)

    suspend fun updateHuddle(huddleId: Int,
                             file: File? = null,
                             name: String? = null,
                             bio: String? = null,
                             isPrivate: Boolean? = null,
                             category: Int? = null,
                             language: String?=null,
                             requestToJoin: Boolean? = null,
                             isRemoveGroupPhoto:Boolean?=null,
                             participantShare: Boolean? = null,
                             setProfilePicAsDP:Boolean=false
    ): ResultOf<PublicHuddle> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }
            val request = UpdateHuddleRequest(
                name = name, about = bio, categoryId = category, requestToJoin = requestToJoin, participantShare = participantShare, isPrivate = isPrivate, isRemove = isRemoveGroupPhoto
           , huddleLanguage = language, useUserProfilePhoto = setProfilePicAsDP)
            val dataBody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).updateHuddle(huddleId, imageBody, dataBody)
            val result = APIUtil.handleResponse(response)
            when (result) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    val newHuddle = result.value
                    db.withTransaction {
                        val huddle = db.getHuddleDao().getHuddle(huddleId)?: return@withTransaction
                        db.getHuddleDao().updateHuddle(huddle.copy(
                            name = newHuddle.name,
                            category = newHuddle.category,
                            about = newHuddle.about,
                            thumbnail = newHuddle.thumbnail,
                            groupPhoto = newHuddle.groupPhoto,
                            requestToJoin = newHuddle.requestToJoin,
                            timeCreated =newHuddle.timeCreated,
                            timeUpdated = newHuddle.timeUpdated
                        ))
                    }
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "updateHuddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getHuddleParticipantsList(huddleId: Int, searchKeyword: String = "", searchTypeList: List<ParticipantsSearchType>): Pager<Int, Participant> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = {
                HuddleParticipantsDataSource(
                    APIServiceGenerator.createService(ProfileAPIService::class.java), huddleId, searchKeyword, searchTypeList
                )
            }
        )
    }

    suspend fun leaveHuddle(huddleId: Int): ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).leaveHuddle(huddleId)
            val result = APIUtil.handleResponseWithoutResult(response)
            when (result) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    db.getHuddleDao().deleteHuddle(huddleId)
                }
            }
            result.convertTo { ResultOf.Success(Unit) }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "deleteHuddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getUnreadCount(huddle: PublicHuddle): Int = withContext(Dispatchers.IO) {
        return@withContext if(huddle.huddleType == HuddleType.PRIVATE) {
            huddle.unreadCount
        } else {
            if (huddle.lastMessage?.sender==accountRepo.user.id) return@withContext 0
            val lastRead = huddle.lastReadMessage?: return@withContext 0
            val msg = db.getChatMessageDao().getHuddleChatMessageWithMedia(lastRead)
            msg?: return@withContext 0
            var notMineUnread = db.getChatMessageDao().countChatsAfterExceptForSender(msg.message.createdTime, HuddleChatMessage.prefixHuddleId(huddle.id),accountRepo.user.id)
            if (notMineUnread==0) return@withContext 0
            val unread = db.getChatMessageDao().countChatsAfter(msg.message.createdTime, HuddleChatMessage.prefixHuddleId(huddle.id))
            Log.d("CREP", "getUnreadCount: $unread")
            unread
        }
    }

    suspend fun getHuddleChatWithMedia(id: String) = db.getChatMessageDao().getHuddleChatMessageWithMedia(id)
    fun getHuddleChatWithMediaLiveData(id: String) = db.getChatMessageDao().getHuddleChatMessageWithMediaLiveData(id)

    fun getUnreadHuddles(type: HuddleType = HuddleType.PUBLIC): LiveData<Int> = db.getHuddleDao().countUnreadHuddles(type)

    fun getUnreadChats(): LiveData<Int> = db.getPrivateChatDao().countUnreadChats()

    fun getUnreadBuddiesChats(): LiveData<Int> = db.getPrivateChatDao().countUnreadChatsForTabs(tabType = PrivateChat.PrivateMessageTabType.BUDDIES)

    fun getUnreadIntruderChats(): LiveData<Int> = db.getPrivateChatDao().countUnreadChatsForTabs(tabType = PrivateChat.PrivateMessageTabType.INTRUDER)

    suspend fun getUnreadCount(id: Int): Int = withContext(Dispatchers.IO) {
        val huddle = db.getHuddleDao().getHuddle(id)?: return@withContext 0
        return@withContext getUnreadCount(huddle)
    }

    suspend fun resetUnreadCount(id: Int) = withContext(Dispatchers.IO) {
        var huddle = db.getHuddleDao().getHuddle(id)?: return@withContext
        huddle = huddle.copy(
            unreadCount = 0,
            lastReadMessage = huddle.lastMessage?.messageId
        )
        db.getHuddleDao().updateHuddle(huddle)
    }

    suspend fun sendInvitations(huddleId: Int, dearsList: ArrayList<Int>, fansList: ArrayList<Int>, likersList: ArrayList<Int>): ResultOf<String> {
        return try {
            val request = SendInvitationsRequest(likers = likersList, fans = fansList, dears = dearsList)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).sendInvitations(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendInvitations: ", e)
//            ResultOf.Error(Exception(e))
            ResultOf.Success("")
        }
    }

    fun getBlockedHuddleListPager(): Pager<Int,BlockedHuddle> {
        return Pager(
            config = defaultPagingConfig(),
            pagingSourceFactory = { BlockedHuddleDataSource(APIServiceGenerator.createService(ChatAPIService::class.java))}
        )
    }

    suspend fun unBlockHuddle(id: Int,userId:Int): ResultOf<UnblockedHuddleResponse> {
        return try {
            val req = UnblockHuddleRequest(
                action = HuddleActionType.UNBLOCK_HUDDLE_INVITE.toString(),
                blockUserIds = listOf(userId)
            )
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).unBlockHuddle(id,req)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun blockOrUnblockHuddleUser(huddleId: Int, action: Participant.ParticipantsActionTypes, userList: ArrayList<Int>): ResultOf<BlockUnblockHuddleUserResponse> {
        return try {
            val request = BlockUnblockHuddleUserRequest(
                action = action, userIds = userList
            )
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).blockOrUnblockUsers(huddleId, request)
            APIUtil.handleResponse(response)
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun blockHuddleJoinRequest(huddleId: Int, memberId: Int): ResultOf<String> {
        return try {
            val request = HuddleCancelActionRequest(
                memberId = memberId,
                action = HuddleActionType.BLOCK_HUDDLE_REQUEST
            )
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).blockHuddleJoinRequest(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun huddleRequestsAdminAction(huddleId: Int, action: HuddleRequestsAdminActionRequest.RequestsAdminAction, memberId: Int) :ResultOf<String>{
        return try{
            val request = HuddleRequestsAdminActionRequest(
                action = action, memberId = memberId
            )
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).huddleRequestAdminAction(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelHuddleInvitation(huddleId: Int, action: HuddleActionType = HuddleActionType.CANCEL_HUDDLE_INVITE, memberIds: ArrayList<Int>) : ResultOf<String>{
        return try{
            val request = HuddleCancelInvitationRequest(
                action = action, memberId = memberIds
            )
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).cancelHuddleInvitation(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun removeHuddleUser(huddleId: Int, userId: Int): ResultOf<String> {
        return try {
            val request = RemoveUserRequest(userId)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).removeUser(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "removeHuddleUser: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun dismissAdmin(huddleId: Int, userId: Int): ResultOf<String> {
        return try {
            val request = RemoveUserRequest(userId)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).dismissAdmin(huddleId, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "dismissAdmin: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun pinUnpinHuddlePost(huddleId: Int, pin: Boolean, messageId: String): ResultOf<PinHuddlePostReponse> {
        return try {
            val request = PinHuddlePostRequest(messageId, pin)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).pinHuddlePost(huddleId, request)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.withTransaction {
                        db.getChatMessageDao().apply {
                            if (pin) {
                                val currentPinnedPost = getHuddlePinnedPost(HuddleChatMessage.prefixHuddleId(result.value.huddleId))
                                currentPinnedPost?.let {
                                    val huddle = getHuddleChatMessage(currentPinnedPost.message.messageId)?: return@withTransaction
                                    updateChat(huddle.copy(
                                        pinned = false
                                    ))
                                }
                            }
                            val huddle = getHuddleChatMessage(result.value.messageId)?: return@withTransaction
                            updateChat(huddle.copy(
                                pinned = result.value.pin
                            ))
                        }
                    }
                }
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "dismissAdmin: ", e)
            Firebase.crashlytics.recordException(e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sendOrCancelAdminInvite(huddleId: Int, memberId: Int, cancel: Boolean = false): ResultOf<BlockUnblockHuddleUserResponse.User> {
        return try {

            val request = AdminInviteRequest(
                memberId = memberId, cancel = cancel
            )
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).adminInvite(huddleId, request)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                getHuddleInterventions(huddleId)
            }
            result

        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendCancelAdminInvite: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun privateChatRequestAcceptAction(roomId: String): ResultOf<String>{
        return try {
            val request = AcceptChatRequest(chatRoomId = roomId)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).privateChatRequestAcceptAction( request)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                db.getPrivateChatDao().apply {
                    val chat = getPrivateChat(roomId)?: return@apply
                    insert(chat.copy(type = PrivateChat.ChatType.PRIVATE))
                }
            }
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "privateChatRequestAcceptAction: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun privateChatRequestDeleteAction(roomId: String): ResultOf<String>{
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).privateChatRequestDeletedAction(roomId)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                deletePrivateChat(roomId)
            }
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "privateChatRequestDeleteAction: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteRestrictedUserChat(roomId: String): ResultOf<String>{
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).deleteRestrictedUserChat(roomId)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                deletePrivateChat(roomId)
            }
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "deleteRestrictedUserChat: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getHuddlePrivacy(huddleId : Int) : ResultOf<HuddlePrivacySettings> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddlePrivacySettings(huddleId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "privateChatRequestAcceptAction: ", e)
            ResultOf.Error(Exception(e))
        }
    }
    suspend fun privateChatIntruderDeleteAction(roomId: String): ResultOf<String>{
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).privateChatIntruderDeletedAction(roomId)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                deletePrivateChat(roomId)
            }
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "privateChatRequestAcceptAction: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateHuddlePrivacy(huddleId:Int,reply: HuddlePrivacyStatus, comment: HuddlePrivacyStatus, post: HuddlePrivacyStatus?) : ResultOf<HuddlePrivacySettings> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).updateHuddlePrivacy(huddleId,HuddlePrivacySettings(commentSettings = PrivacySetting(comment), huddleId=huddleId, postSettings = PrivacySetting(post), replySettings = PrivacySetting(reply)))
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getHuddlePinnedPostLiveData(huddleId: Int): LiveData<HuddleChatMessageWithMedia?> = db.getChatMessageDao().getHuddlePinnedPostLiveData(HuddleChatMessage.prefixHuddleId(huddleId))

    suspend fun banUnbanParticipant(huddleId: Int, userId: Int, status: Participant.ParticipantStatus, isBan:Boolean): ResultOf<String>{
        val banRequest: HuddleBanRequest? = when (status) {
            Participant.ParticipantStatus.COMMENT_BAN -> createHuddleBanRequest(isBan, userId, "comment")
            Participant.ParticipantStatus.POST_BAN -> createHuddleBanRequest(isBan, userId, "post")
            Participant.ParticipantStatus.REPLY_BAN -> createHuddleBanRequest(isBan, userId, "reply")
            else -> null
        }
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).banUnbanHuddleParticipant(huddleId,banRequest!!)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "banUnbanParticipantAction: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteReportedComment(huddleId: Int, postId: String?, commentId: String, senderBlocked: Boolean, actionBy: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).deleteReportedComment(huddleId, postId, commentId, senderBlocked, actionBy)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getLanguages(): ResultOf<List<HuddleLanguage>> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleLanguages()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Huddle Language Exception: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    private fun createHuddleBanRequest(isBan: Boolean, banUserId: Int, type: String): HuddleBanRequest {
        val action = if (isBan) "ban" else "unban"
        return HuddleBanRequest(action, listOf(banUserId), type)
    }

    suspend fun getHuddlePostLink(messageId: String, huddleId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getShareLink(huddleId, messageId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Huddle Language Exception: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun createPoll(createPollRequest: CreatePollRequest): ResultOf<APIResponse<CreatePollResponse>> {
        return try {
            val service = APIServiceGenerator.createService(PollsAPIService::class.java)
            val response =  service.postCreatePoll(createPollRequest)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception create poll: ", e)
            ResultOf.Error(Exception(e))
        }
    }





    suspend fun editPoll(createPollRequest: CreatePollRequest): ResultOf<APIResponse<CreatePollResponse>> {
        return try {
            val service = APIServiceGenerator.createService(PollsAPIService::class.java)
            val response =  service.editCreatePoll(createPollRequest)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception create poll: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPollById(pollId: Int): ResultOf<Poll> {
        return try {
            val resp = APIServiceGenerator.createService(PollsAPIService::class.java).getPollsById(pollId)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getRemovedDears(huddleId: Int): Pager<Int, Participant> {
        return Pager(config = defaultPagingConfig(),
                     pagingSourceFactory = {
                         HuddleParticipantsDataSource(
                             api = APIServiceGenerator.createService(ProfileAPIService::class.java),
                             huddleId = huddleId,
                             searchTypeList = emptyList(),
                             isTribe = true
                         )
                     })
    }


    suspend fun addRemovedUser(addRemovedUserRequest: AddRemovedUserRequest): ResultOf<APIResponse<AddRemovedUserResponse>> {
        return try {
            val service = APIServiceGenerator.createService(ChatAPIService::class.java)
            val response =  service.addRemovedUsers(addRemovedUserRequest)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception add removed user: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sellHuddle(huddleId: Int, flaxAmount: Double,isForSale:Boolean): ResultOf<APIResponse<SellHuddleResponse>> {
        return try {
            val request = if(isForSale) SellHuddleRequest(flaxAmount = flaxAmount,"edit") else SellHuddleRequest(flaxAmount = flaxAmount)
            val response = if(isForSale)
                APIServiceGenerator.createService(ChatAPIService::class.java).editSellHuddle(huddleId, request)
                else APIServiceGenerator.createService(ChatAPIService::class.java).sellHuddle(huddleId, request)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception sell huddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelSellHuddle(huddleId: Int, flaxAmount: Double): ResultOf<APIResponse<SellHuddleResponse>> {
        return try {
            val request = SellHuddleRequest(flaxAmount = flaxAmount, "cancel")
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).editSellHuddle(huddleId, request)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception cancel sell huddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun buyHuddle(huddleId: Int): ResultOf<APIResponse<String>> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).buyHuddle(huddleId)
            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception cancel sell huddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }


    @OptIn(ExperimentalPagingApi::class)
    fun getSellHuddlesListMediatorPager(): Pager<Int, HuddleForSale> {
        return Pager(config = defaultPagingConfig(),
                     remoteMediator = SellHuddleListRemoteMediator(db, APIServiceGenerator.createService(ChatAPIService::class.java)),
                     pagingSourceFactory = {
                         db.getSellHuddleListDao().sellHuddleListPagingSource()
                     })
    }

    fun getSelectedSellHuddle(huddleID: Int) = db.getSellHuddleListDao().getSelectedHuddle(huddleID)


    suspend fun deleteBuyHuddle(huddleID: Int) {
        db.getSellHuddleListDao().deleteBuyHuddleFromDB(huddleID)
    }

    suspend fun getHuddleCreateEligible(): ResultOf<HuddleEligibilityResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleCreateEligibility()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }


    suspend fun purchaseHuddleCreation(): ResultOf<APIResponse<String>> {
        return try {
            val request = PurchaseHuddleRequest(coins = 500)
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).purchaseCreateHuddle(request)
            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception purchase create huddle: ", e)
            ResultOf.Error(Exception(e))
        }
    }
}