package com.app.messej.data.repository

import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.api.PostatAPIServices
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.PostAtCommentPayload
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.data.model.PostatReplyCommentPayload
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.flash.UserFunctionalityBlockRequest
import com.app.messej.data.model.api.postat.BlockUserRequest
import com.app.messej.data.model.api.postat.CreatePostatRequest
import com.app.messej.data.model.api.postat.MusicData
import com.app.messej.data.model.api.postat.MusicFile
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.api.postat.PostatRuleLimitResponse
import com.app.messej.data.model.api.postat.PostatShareResponse
import com.app.messej.data.model.entity.LocalPostatMedia
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.PostatWithMedia
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.model.enums.Functionality
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.mediators.MyPostatRemoteMediator
import com.app.messej.data.repository.mediators.PostatCommentRemoteMediator
import com.app.messej.data.repository.mediators.PostatFeedRemoteMediator
import com.app.messej.data.repository.pagingSources.PostatAudioListDataSource
import com.app.messej.data.repository.pagingSources.PostatBlockedListDataSource
import com.app.messej.data.repository.pagingSources.PostatMediaPickerDataSource
import com.app.messej.data.repository.pagingSources.PostatMentionListDataSource
import com.app.messej.data.repository.pagingSources.UserFeedPostatDataSource
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.uri
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.github.f4b6a3.uuid.UuidCreator
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class PostatRepository(private var context: Context): BaseMediaUploadRepository(context) {

    private val datastore = FlashatDatastore()

    fun getPostatMediaListPager(): Pager<Int, PostatDeviceMedia> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false), pagingSourceFactory = { PostatMediaPickerDataSource(this) })
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getPostatFeedPager(tab: PostatTab) : Pager<Int, Postat.FeedPostat> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false, initialLoadSize = 50, prefetchDistance = 5),
            remoteMediator = PostatFeedRemoteMediator(tab, db, APIServiceGenerator.createService(PostatAPIService::class.java)),
            pagingSourceFactory = { db.getFeedPostatDao().getFeedPostatPager(tab) },
        )
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getMyPostatPager(type: Postat.PostatType? = null): Pager<Int, Postat> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false, initialLoadSize = 50, prefetchDistance = 5),
            remoteMediator = MyPostatRemoteMediator(db, APIServiceGenerator.createService(PostatAPIService::class.java)),
            pagingSourceFactory = { when(type) {
                null -> db.getMyPostatDao().getPostatPagingSource()
                else -> db.getMyPostatDao().getPostatPagingSourceByType(type)
            }}
        )
    }

    fun getUserFeedPostatPager(userId: Int) = Pager(
        config = PagingConfig(pageSize = 50, enablePlaceholders = false),
        pagingSourceFactory = { UserFeedPostatDataSource(APIServiceGenerator.createService(PostatAPIService::class.java), userId) }
    )



    @OptIn(ExperimentalPagingApi::class)
    fun getPostatComments(postatId: String, commentCountCallback: (Int) -> Unit): Pager<Int, PostCommentWithReplies> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = PostatCommentRemoteMediator(
                postatId = postatId,
                database = db,
                networkService = APIServiceGenerator.createService(PostatAPIServices::class.java),
                commentCountCallback = commentCountCallback
            ),
            pagingSourceFactory = { db.getPostCommentDao().getBaseCommentsWithRepliesPaging(postatId, CommentType.POSTAT) }
        )
    }




    suspend fun getPostatReplies(postatId: String, parentCommentId: String, page: Int, pageSize: Int = 20) {
         try {

            val response = APIServiceGenerator.createService(PostatAPIServices::class.java).getPostatReplies(
                postatId = postatId,
                page = page,
                commentId = parentCommentId,
                replies = 1
            )

            if (response.isSuccessful && response.code() == 200) {
                val result = response.body()?.result ?: return
                val apiReplies = result.data


                val replyEntities = apiReplies.map { basePostatReply ->
                    basePostatReply.copy(
                        commentId = basePostatReply.messageId,
                        basePostId = postatId,
                        parentCommentId = parentCommentId,
                        type = CommentType.POSTAT
                    )
                }



                replyEntities.forEach { it.sanitize() }
                db.getPostReplyDao().insertPostReplies(replyEntities)

                ResultOf.Success(replyEntities)
            } else {
                ResultOf.Error(Exception("error"))
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun deleteReplyById(replyId: String,type: CommentType) {
        db.getPostReplyDao().deleteSingleCommentReply(replyId, type)
    }


    suspend fun loadMedia(
        prevIndex: Int,
        pageSize: Int) : List<PostatDeviceMedia> = withContext(Dispatchers.IO) {
        val collectionUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Query all the device storage volumes instead of the primary only
            MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL)
        } else {
                MediaStore.Files.getContentUri("external")
        }

        val cursor: Cursor?
        val columnIndexId: Int
        val listOfAllImages = mutableListOf<PostatDeviceMedia>()
        val projection = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATE_TAKEN,
            MediaStore.Files.FileColumns.MEDIA_TYPE,
            MediaStore.Files.FileColumns.MIME_TYPE,
            MediaStore.Files.FileColumns.TITLE,
            MediaStore.Video.VideoColumns.DURATION)

        // Sort by date taken
        val orderBy = MediaStore.Files.FileColumns.DATE_TAKEN

        // Return only video and image metadata.
        val selection = (MediaStore.Files.FileColumns.MEDIA_TYPE + "="
                + MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE + " OR "
                + MediaStore.Files.FileColumns.MEDIA_TYPE + "="
                + MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO)
        try {
            cursor = context.contentResolver
                .query(
                    collectionUri,
                    projection,
                    selection,
                    null,
                    "$orderBy DESC")
            cursor?.use {
                if (prevIndex > 0 ) it.moveToPosition(prevIndex)
                columnIndexId = it.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
                val mimeTypeColumn = it.getColumnIndexOrThrow(MediaStore.Files.FileColumns.MIME_TYPE)
                while (it.moveToNext() && it.position < (prevIndex + pageSize)){
                    val contentUri = ContentUris.withAppendedId(collectionUri, it.getLong(columnIndexId))
                    val mimeType = it.getString(mimeTypeColumn)
                    val duration = it.getColumnIndex(MediaStore.Video.VideoColumns.DURATION).let { i ->
                        if(i<0) null else it.getString(i)
                    }
                    listOfAllImages.add(PostatDeviceMedia(uri = contentUri, mimeType = mimeType, videoDurationMillis = duration?.toLongOrNull()))
                }
                it.close()
            }
        } catch (e : Exception) {
            Log.e("TAG", "loadImagesFromStorage: ", e )
        }
        listOfAllImages
    }

    fun getPostatAudioListPager(searchTerm: String? = null): Pager<Int, MusicFile> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PostatAudioListDataSource(APIServiceGenerator.createService(PostatAPIService::class.java), searchTerm) })
    }

    fun getPostatMentionPager(searchTerm: String? = null): Pager<Int, PostatMentionedUser> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PostatMentionListDataSource(APIServiceGenerator.createService(PostatAPIService::class.java), searchTerm) })
    }
    fun playbackCookieFlow() = datastore.getPostatCookieFlow()

    suspend fun getPlaybackCookie() : ResultOf<VideoPlaybackCookie> {
        return try {
            val response = APIServiceGenerator.createService(PostatAPIService::class.java).getAudioPlaybackCookies()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                datastore.savePostatCookie(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deletePostatComment(postId: String, commentId: String): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIServices::class.java).deletePostatComment(postId, commentId)
            return when(val result = APIUtil.handleResponseWithoutResult(resp)) {
                is ResultOf.Success -> {
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun deletePostatReplyComment(commentId: String,replyId: String): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIServices::class.java).deletePostatReplyComment(commentId,replyId)
            return when(val result = APIUtil.handleResponseWithoutResult(resp)) {
                is ResultOf.Success -> {
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun writePostatComment(postId: String, commentRequest: PostAtCommentPayload): ResultOf<Unit> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIServices::class.java).writePostatComment(postId, commentRequest)
            val result = APIUtil.handleResponse(resp)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun writePostatReplyComment(postId: String, replyPayload: PostatReplyCommentPayload): ResultOf<Unit> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIServices::class.java).writePostatReplyComment(postId, replyPayload)
            val result = APIUtil.handleResponse(resp)

            // If successful, refresh the replies for this comment to prevent duplicates
            if (result is ResultOf.Success && replyPayload.replyTo != null) {
                // Clear existing replies for this comment before fetching fresh ones
                db.getPostReplyDao().deletePostReplies(replyPayload.replyTo, CommentType.POSTAT)
                // Fetch fresh replies from server
                getPostatReplies(postId, replyPayload.replyTo, page = 1)
            }

            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIService::class.java).getPostatUploadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun processVideo(med: PostatDeviceMedia, listener: VideoEncoderUtil.MediaProcessingListener, useOriginalAudio: Boolean = true): File {
        return VideoEncoderUtil.encodeVideoMedia3(med, listener, useOriginalAudio)
    }

    suspend fun createPostatFromDraft(medias: List<PostatMedia>, caption: String,
                                      enableComments: Boolean, hasMention: Boolean = false, mentionedUsers: List<Int>? = null,
                                      isOriginalAudio: Boolean, music: MusicFile? = null, draft: Boolean = false, postatId: String) :PostatWithMedia {

        val postat =  createPostat(medias, caption, enableComments, hasMention, mentionedUsers, isOriginalAudio, music, draft, postatId)
        db.withTransaction {
            Log.d("POSTREP", "createPostatForDraft: inserting postat: $postat")
            db.getMyPostatDao().insert(postat)
        }
        return db.getMyPostatDao().getLocalPostatWithMedia(postatId)!!
    }

    suspend fun createPostat(localMedias: MutableList<PostatDeviceMedia>, caption: String,
                             enableComments: Boolean, hasMention: Boolean = false, mentionedUsers: List<Int>? = null,
                             isOriginalAudio: Boolean, musicData: MusicFile? = null, draft: Boolean = false) :PostatWithMedia {

        val uuid = UuidCreator.getTimeBased().toString()
        val localMedia = localMedias.mapIndexed { index, med ->
            createPostatMedia(med.processedFile ?: throw Exception("media not processed"), uuid, med.mediaType, index) { name -> APIUtil.getPostatMediaUploadKey(name, accountRepo.user.id) }
        }
        val media = localMedia.map { PostatMedia.from(it) }
        val postat = createPostat(media, caption, enableComments, hasMention, mentionedUsers, isOriginalAudio, musicData, draft, uuid)
        Log.d("POSTREP", "createPostatForDraft: inserting postat: $postat")
        db.withTransaction {
            db.getMyPostatDao().insert(postat)
            localMedia.forEach {
                it.let {
                    db.getMyPostatDao().insert(it)
                }
            }
        }
        return db.getMyPostatDao().getLocalPostatWithMedia(uuid)!!
    }

    private fun createPostat(medias: List<PostatMedia>, caption: String,
                                                      enableComments: Boolean, hasMention: Boolean = false, mentionedUsers: List<Int>? = null,
                                                      isOriginalAudio: Boolean, musicData: MusicFile? = null, draft: Boolean = false, uuid: String): Postat {
        val postat = Postat(
            messageId = uuid,
            message = caption,
            hasMention = hasMention,
            mentionedUsers = mentionedUsers?: listOf(),
            isOriginalAudio = isOriginalAudio,
            musicData = MusicData.from(musicData),
            media = medias,
            turnOffComments = !enableComments,
            userId = accountRepo.user.id,
            senderDetails = SenderDetails.from(accountRepo.user),
            created = DateTimeUtils.getZonedDateTimeNowAsString(),
            type = if (draft) Postat.PostatType.DRAFT else Postat.PostatType.FINAL
        )

        postat.sendStatus = SendStatus.PENDING

        Log.w("POSTREP", "createPostat: inserting postat: $postat")
        return postat
    }


    private suspend fun createPostatMedia(file: File, uuid: String, mediaType: MediaType, index: Int, s3KeyProvider: (String) -> String): LocalPostatMedia {
        val file = MediaUtils.storePostatFile(context, file.path, uuid, mediaType, index)

        val mimeType = MediaUtils.getMIME(file)

        val s3Key = s3KeyProvider(file.name)

        val meta = MediaMeta(
            mediaType = mediaType,
            mediaName = file.name,
            mimeType = mimeType,
            s3Key = s3Key,
        ).apply {
            formattedSize = MediaUtils.getFileSize(file)
            if (mediaType == MediaType.VIDEO) {
                seconds = MediaUtils.getDuration(file)
            }
            val res = if (mediaType == MediaType.VIDEO) MediaUtils.getVideoResolution(file.uri) else MediaUtils.getImageResolution(file)
            mediaWidth = res.width.toString()
            mediaHeight = res.height.toString()
        }

        val media =  LocalPostatMedia(
            postatId = uuid,
            key = s3Key,
            path = file.absolutePath,
            meta = meta
        ).apply {
            uploadState = MediaUploadState.Pending
        }
        Firebase.crashlytics.log("stored flash (${media.postatId}) file to ${media.path}")
        return media
    }

    suspend fun getPendingPostat(): PostatWithMedia? {
        return db.withTransaction {
            return@withTransaction db.getMyPostatDao().getPostatByStatus(SendStatus.PENDING)
        }
    }

    suspend fun sendPostat(msg: PostatWithMedia, fromDraft: Boolean = false): ResultOf<Unit> {
        if (msg.mediaIsUploading) return ResultOf.getError("Media is Already uploading")
        if(msg.needsMediaUpload) {
            val result = uploadMedia(msg)
            if(result !is ResultOf.Success) return result
        }
        val myPostat = db.withTransaction {
            val fv = db.getMyPostatDao().getLocalPostat(msg.postat.messageId)
            return@withTransaction if (fv?.sendStatus == SendStatus.PENDING) {
                fv.sendStatus = SendStatus.SENDING
                db.getMyPostatDao().update(fv)
                fv
            } else null
        } ?: return ResultOf.getError("Post is already sending. exiting...")
        return try {
            val response = if (fromDraft) APIServiceGenerator.createService(PostatAPIService::class.java).updatePostat(myPostat.messageId, CreatePostatRequest.from(myPostat))
                else APIServiceGenerator.createService(PostatAPIService::class.java).createPostat(CreatePostatRequest.from(myPostat))
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getMyPostatDao().apply {
                        deletePostat(myPostat.messageId)
                        deletePostatMedia(myPostat.messageId)
                        insert(result.value.copy(
                            type = Postat.PostatType.FINAL,
                            senderDetails = myPostat.senderDetails
                        ))
                    }
                }
            } else {
                db.withTransaction {
                    myPostat.sendStatus = SendStatus.PENDING
                    db.getMyPostatDao().update(myPostat)
                }
            }
            result.convertTo { ResultOf.Success(Unit) }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPostat: ", e)
            db.withTransaction {
                myPostat.sendStatus = SendStatus.PENDING
                db.getMyPostatDao().update(myPostat)
            }
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun uploadMedia(msg: PostatWithMedia): ResultOf<Unit> = withContext(Dispatchers.IO) {
        if (!msg.needsMediaUpload) return@withContext ResultOf.getError("No media to upload")
        val media = msg.medias

        val result = media.mapIndexed { index, it ->
            if (!getMediaUploadLock(it.mediaId)) return@withContext ResultOf.getError("Media is already being uploaded")
            val result = prepareAndUploadMultipartMedia(it, index, media.size)
            if (result is ResultOf.Success) {
                it.uploadState = MediaUploadState.None
                db.getMyPostatDao().update(it)
            } else {
                it.uploadState = MediaUploadState.Pending
                db.getMyPostatDao().update(it)
            }
            result
        }

        result.last()
    }

    private suspend fun getMediaUploadLock(mediaId: Int): Boolean {
        return db.withTransaction {
            val media = db.getMyPostatDao().getLocalPostatMedia(mediaId)
            Log.d("FLASH", "getMediaUploadLock: current status is: ${media?.uploadState}")
            if (media?.uploadState == MediaUploadState.Pending) {
                media.uploadState = MediaUploadState.Uploading(0)
                db.getMyPostatDao().update(media)
                return@withTransaction true
            }
            return@withTransaction false
        }
    }

    private fun calculateProgress(index: Int, progress: Int = 100, size: Int) = ((100*index)+progress)/size

    private suspend fun prepareAndUploadMultipartMedia(media: LocalPostatMedia, index: Int, count: Int): ResultOf<Unit> {
        try {
            MediaUploadListener.post(media.postatId, calculateProgress(index, 0, count))
            performMultipartMediaUpload(media.s3UploadMedia).collect {
                when (val res = it) {
                    MultipartMediaUploadResult.Complete -> {

                    }
                    is MultipartMediaUploadResult.Error -> throw res.error
                    is MultipartMediaUploadResult.Progress -> {
                        Log.d("ENCODE", "posting ${res.percent} to MUL")
                        MediaUploadListener.post(media.postatId, calculateProgress(index, res.percent, count))
                    }
                }
            }
            MediaUploadListener.clear(media.postatId)
        } catch (e: Exception) {
            MediaUploadListener.clear(media.postatId)
            Log.e("ENCODE", "prepareAndUploadMultipartMedia:",e)
            return ResultOf.getError(e)
        }

        Log.d("ENCODE", "prepareAndUploadMultipartMedia: end of uploading Method")
        return ResultOf.Success(Unit)
    }

    //delete postat
    suspend fun deletePostat(postatId: String) : ResultOf<APIResponse<Unit>> {
        db.withTransaction {
            db.getMyPostatDao().deletePostat(postatId)
            db.getMyPostatDao().deletePostatMedia(postatId)
        }
        return try {
            val response = APIServiceGenerator.createService(PostatAPIService::class.java).deletePostatbyId(postatId)
            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    //hide postat
    suspend fun hidePostat(
        postatId: String,
    ): ResultOf<APIResponse<Unit>> {
        return try {
            val requestBody = mapOf("hide" to true)
            val response = APIServiceGenerator.createService(PostatAPIService::class.java)
                .updatePostatField(postatId,true, requestBody)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                db.getFeedPostatDao().deletePostat(postatId)
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getShareCode(postAtId: String): ResultOf<PostatShareResponse> {
        return try {
            val response = APIServiceGenerator.createService(PostatAPIService::class.java).getShareCode(postAtId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Huddle Language Exception: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getDraftPostat(id: String) = db.getMyPostatDao().getLocalPostatWithMedia(id)

    suspend fun getPostatMedia(id: String) = db.getMyPostatDao().getLocalPostatMediaByPostatId(id)

    //block/unblock user in postat
    suspend fun ignorePostatUser(userId: String, block: Boolean): ResultOf<String> {
        return try {
            val requestBody = BlockUserRequest(userId.toInt(), block)
            val response = APIServiceGenerator.createService(PostatAPIServices::class.java)
               .blockUser(requestBody)

            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    //list of blocked users
    fun getPostatBlockedListPager(): Pager<Int, PostatMentionedUser> {
    return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
        pagingSourceFactory = {
            PostatBlockedListDataSource(APIServiceGenerator.createService(PostatAPIServices::class.java))
        })
    }

    //Block user from posting on postat by empowerment
    suspend fun blockUserFromPostingOnPostat(userId: Int) : ResultOf<String> {
        return try {
            val request = UserFunctionalityBlockRequest(Functionality.POSTAT_POST, BlockUnblockAction.BLOCK)
            val response = APIServiceGenerator.createService(PostatAPIServices::class.java).blockUserFromPostingOnPostat(userId, request)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        }catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getCreatePostatEligible(userId: Int): ResultOf<PostatRuleLimitResponse> {
        return try {
            val resp = APIServiceGenerator.createService(PostatAPIService::class.java).getPostatCitizenshipRuleData(userId)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sendPostatCommentLike(request: PostatCommentLikePayload): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PostatAPIServices::class.java).postPostatCommentlike(request)
            val result = APIUtil.handleResponseWithoutResult(response)

            // Update local database on success
            if (result is ResultOf.Success) {
                updateLocalCommentLikeCount(request)
            }

            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun updateLocalCommentLikeCount(request: PostatCommentLikePayload) = withContext(Dispatchers.IO) {
        db.withTransaction {
            val increment = 1

            if (request.replyId != null) {
                db.getPostReplyDao().updateReplyLikeCount(request.replyId, CommentType.POSTAT, increment)
            } else if (request.id != null) {
                db.getPostCommentDao().updateCommentLikeCount(request.id, CommentType.POSTAT, increment)
            }
        }
    }
    suspend fun deletePostatCommentFromDB(commentId: String) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getPostCommentDao().deletePostCommentFromDB(commentId, CommentType.POSTAT)
        }
    }

    suspend fun deletePostatCommentReplyFromDB(commentId: String) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getPostReplyDao().deleteSingleCommentReply(commentId, CommentType.POSTAT)
        }
    }

}