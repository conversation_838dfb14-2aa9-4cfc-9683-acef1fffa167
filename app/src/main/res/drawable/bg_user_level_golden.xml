<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="414dp" android:viewportHeight="414" android:viewportWidth="393" android:width="393dp">
      
    <group>
            
        <clip-path android:pathData="M0,20C0,8.95 8.95,0 20,0H373C384.05,0 393,8.95 393,20V413.82H0V20Z"/>
            
        <path android:pathData="M393,-2H1V414H393V-2Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="-96.42" android:endY="411.52" android:startX="512" android:startY="371.41" android:type="linear">
                              
                    <item android:color="#FF6D1B95" android:offset="0"/>
                              
                    <item android:color="#FFFFD473" android:offset="0.7"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M-33.66,330.07C-76.29,330.07 -118.07,326.81 -158.69,320.57V417.9H552.33V67.65C419.73,226.83 206.64,330.07 -33.66,330.07Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="250.26" android:endY="453.87" android:startX="181.18" android:startY="197.04" android:type="linear">
                              
                    <item android:color="#FF848484" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:pathData="M119.92,407.25C21.23,407.25 -72.86,389.83 -158.69,358.28V417.91H552.33V281.08C431.42,360.37 281.84,407.26 119.92,407.26V407.25Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="552.32" android:endY="349.49" android:startX="-158.7" android:startY="349.49" android:type="linear">
                              
                    <item android:color="#FF848484" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#00000000" android:pathData="M552.33,281.07C431.42,360.36 281.84,407.25 119.91,407.25C21.22,407.25 -72.87,389.83 -158.69,358.27" android:strokeColor="#ffffff" android:strokeWidth="0.25"/>
            
        <path android:pathData="M-110.49,-2.45H-158.69V417.9H247.26C68.91,330.42 -63.66,178.5 -110.49,-2.45Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="-141.26" android:endY="384.52" android:startX="64.56" android:startY="192.88" android:type="linear">
                              
                    <item android:color="#FF848484" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#00000000" android:pathData="M247.26,417.91C68.91,330.42 -63.66,178.5 -110.49,-2.45" android:strokeColor="#ffffff" android:strokeWidth="0.5"/>
            
        <path android:pathData="M-158.69,257.59V417.91H111.62C8.14,382.96 -84.03,327.68 -158.69,257.59Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="111.62" android:endY="337.75" android:startX="-158.69" android:startY="337.75" android:type="linear">
                              
                    <item android:color="#FF848484" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#00000000" android:pathData="M-158.69,257.59V417.91H111.62C8.14,382.96 -84.03,327.68 -158.69,257.59Z" android:strokeColor="#ffffff" android:strokeWidth="0.25"/>
            
        <path android:pathData="M552.33,225.7V-2.45H332.93C389.63,85.18 464.24,162.54 552.33,225.69V225.7Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="588.8" android:endY="-19.09" android:startX="397.49" android:startY="141.41" android:type="linear">
                              
                    <item android:color="#FF848484" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#00000000" android:pathData="M332.94,-2.45C389.63,85.18 464.24,162.54 552.33,225.69" android:strokeColor="#ffffff" android:strokeWidth="0.5"/>
            
        <path android:fillAlpha="0.7" android:fillColor="#00000000" android:pathData="M100.36,-2.44C100.36,35.08 66.06,65.52 23.73,65.52C-18.61,65.52 -52.9,35.07 -52.9,-2.44" android:strokeAlpha="0.7" android:strokeColor="#ffffff" android:strokeLineCap="round" android:strokeWidth="0.25"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.33,122.08C398.85,255.68 188.5,338.07 -43.55,338.07C-82.59,338.07 -121.04,335.74 -158.69,331.22" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.25"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.33,138.17C399.52,266.09 193.14,344.64 -33.93,344.64C-76.34,344.64 -118,341.84 -158.69,336.45" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.28"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.32,154.26C400.18,276.5 197.77,351.2 -24.32,351.2C-70.07,351.2 -114.96,347.93 -158.71,341.66" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.31"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.33,170.35C400.86,286.92 202.41,357.76 -14.7,357.76C-63.79,357.76 -111.92,354.02 -158.69,346.89" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.33"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.32,186.45C401.53,297.33 207.04,364.33 -5.09,364.33C-57.54,364.33 -108.89,360.13 -158.71,352.12" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.36"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.33,202.54C402.2,307.75 211.68,370.89 4.54,370.89C-51.26,370.89 -105.83,366.23 -158.69,357.33" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.39"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.34,218.63C402.88,318.16 216.33,377.44 14.16,377.44C-44.99,377.44 -102.79,372.32 -158.69,362.55" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.42"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.34,234.73C403.54,328.58 220.96,384.02 23.77,384.02C-38.72,384.02 -99.75,378.43 -158.69,367.79" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.44"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.33,250.83C404.21,338.99 225.6,390.58 33.39,390.58C-32.46,390.58 -96.7,384.52 -158.69,373.02" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.47"/>
            
        <path android:fillAlpha="0.5" android:fillColor="#00000000" android:pathData="M552.34,266.92C404.89,349.4 230.24,397.14 43.01,397.14C-26.19,397.14 -93.67,390.63 -158.69,378.23" android:strokeAlpha="0.5" android:strokeColor="#ffffff" android:strokeWidth="0.5"/>
            
        <path android:fillAlpha="0.7" android:fillColor="#00000000" android:pathData="M309.55,417.9C160.84,315.05 60.59,166.39 35.09,-2.45" android:strokeAlpha="0.7" android:strokeColor="#ffffff" android:strokeLineCap="round" android:strokeWidth="0.25"/>
          
    </group>
    
</vector>
