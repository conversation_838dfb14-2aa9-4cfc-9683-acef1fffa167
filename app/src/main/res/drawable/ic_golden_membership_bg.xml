<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="80dp" android:viewportHeight="80" android:viewportWidth="335" android:width="335dp">
      
    <path android:pathData="M4,0L331,0A4,4 0,0 1,335 4L335,76A4,4 0,0 1,331 80L4,80A4,4 0,0 1,0 76L0,4A4,4 0,0 1,4 0z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="129.17" android:endY="-108.32" android:startX="316.13" android:startY="72" android:type="linear">
                        
                <item android:color="#FF6D1B95" android:offset="0"/>
                        
                <item android:color="#FFFFD473" android:offset="0.7"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
