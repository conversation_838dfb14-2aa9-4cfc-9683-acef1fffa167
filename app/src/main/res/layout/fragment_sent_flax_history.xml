<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.BusinessDealsListViewModel" />
    </data>
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorSurface">
       <!-- <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">-->
            <include
                android:id="@+id/custom_action_bar"
                layout="@layout/item_custom_action_bar_rating" />
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/text_search_flax_history"
            style="@style/Widget.Flashat.GreyTextInput"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            android:autofillHints="phone"
            android:importantForAutofill="yes"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginHorizontal="@dimen/double_margin"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                style="@style/Widget.Flashat.GreyTextInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/textColorSecondary"
                android:inputType="textAutoComplete"
                android:text="@={viewModel.searchTerm}"
                android:drawableEnd="@drawable/ic_search_lens"
                android:drawableTint="@color/textColorHint"
                android:hint="@string/common_search" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.kennyc.view.MultiStateView
            android:id="@+id/multiStateView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:msv_emptyView="@layout/layout_empty_deals"
            app:msv_loadingView="@layout/layout_eds_state_loading"
            app:msv_viewState="loading"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_search_flax_history"
            app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_flax_history_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="1.0" />
        </com.kennyc.view.MultiStateView>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>