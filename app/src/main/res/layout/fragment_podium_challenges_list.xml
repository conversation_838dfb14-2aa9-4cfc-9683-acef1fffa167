<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:applySystemBarInsets="@{`bottom`}"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/likes_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        android:layout_marginStart="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="@+id/like_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/like_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/likes_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_bias="0.0" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="parent"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/likes_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_like"
                                app:layout_constraintBottom_toBottomOf="@+id/likes_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="@+id/likes_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="@+id/likes_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/likes_challenge_ic_bg" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/like_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_likes"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/likes_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/like_for_speakers_description"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenges_like_for_speakers_sub_text"
                                android:textColor="@color/white"
                                app:layout_constraintEnd_toStartOf="@id/likes_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_like"
                                app:layout_constraintTop_toBottomOf="@id/like_for_speakers_text"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_like"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/flag_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        android:layout_marginStart="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="@+id/flag_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/flag_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/flag_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="parent"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/flag_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="@dimen/line_spacing"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_flag"
                                app:layout_constraintBottom_toBottomOf="@+id/flag_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/flag_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/flag_challenge_ic_bg" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/flag_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_flags"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/flag_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/flag_for_speakers_description"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:text="@string/title_podium_flag_challenge_description"
                                android:textColor="@color/white"
                                app:layout_constraintEnd_toStartOf="@id/flag_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_flag"
                                app:layout_constraintTop_toBottomOf="@id/flag_for_speakers_text"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_flag"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/gift_challenge"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/gift_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        android:layout_marginStart="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="@+id/gift_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/gift_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/gift_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/gift_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_gift"
                                app:layout_constraintBottom_toBottomOf="@+id/gift_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/gift_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/gift_challenge_ic_bg" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="parent"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/gift_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_gifts"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/gift_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/gift_for_speakers_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                app:layout_constraintTop_toBottomOf="@id/gift_for_speakers_text"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_gift"
                                app:layout_constraintEnd_toStartOf="@id/gift_challenge_ic_bg"
                                android:text="@string/podium_challenges_gift_sub_text"/>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_gift"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/confour_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        android:layout_marginStart="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="@+id/confour_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/confour_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/confour_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:layout_marginBottom="@dimen/activity_margin"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/confour_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_confour"
                                app:layout_constraintBottom_toBottomOf="@+id/confour_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_bias="1.0"
                                app:layout_constraintStart_toStartOf="@+id/confour_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/confour_challenge_ic_bg"
                                app:layout_constraintVertical_bias="1.0" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="parent"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/confour_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_confour"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/confour_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/confour_for_speakers_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                app:layout_constraintTop_toBottomOf="@id/confour_for_speakers_text"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_confour"
                                app:layout_constraintEnd_toStartOf="@id/confour_challenge_ic_bg"
                                android:text="@string/podium_challenges_confour_sub_text"/>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_confour"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cslPenalty"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/penalty_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        android:layout_marginStart="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="@+id/penalty_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/penalty_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/penalty_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:layout_marginBottom="@dimen/activity_margin"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/penalty_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_penalty"
                                app:layout_constraintBottom_toBottomOf="@+id/penalty_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/penalty_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/penalty_challenge_ic_bg" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:layout_constraintStart_toStartOf="parent"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/penalty_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_penalty"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/penalty_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/penalty_for_speakers_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                app:layout_constraintTop_toBottomOf="@id/penalty_for_speakers_text"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintVertical_chainStyle="packed"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_penalty"
                                app:layout_constraintEnd_toStartOf="@id/penalty_challenge_ic_bg"
                                android:text="@string/podium_challenge_penalty_sub_text"/>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_penalty"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/box_challenge_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        app:layout_constraintStart_toStartOf="@+id/box_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/box_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/box_challenge_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="@dimen/activity_margin"
                                android:adjustViewBounds="true"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/box_challenge_ic"
                                android:layout_width="90dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/element_spacing"
                                android:adjustViewBounds="true"
                                android:src="@drawable/ic_podium_challenge_box"
                                app:layout_constraintBottom_toBottomOf="@+id/box_challenge_ic_bg"
                                app:layout_constraintEnd_toEndOf="@id/box_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="@+id/box_challenge_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/box_challenge_ic_bg" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/box_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_boxes"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/box_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/box_for_speakers_description"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:text="@string/podium_challenge_box_sub_text"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_box"
                                app:layout_constraintEnd_toStartOf="@id/box_challenge_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/box_for_speakers_text"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_box"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_margin"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/knowledge_race_target_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:elevation="4dp"
                        android:src="@drawable/ic_podium_challenges_target"
                        app:layout_constraintStart_toStartOf="@+id/knowledge_race_challenge_layout"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/knowledge_race_challenge_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:elevation="0dp"
                        app:cardCornerRadius="@dimen/activity_margin"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_podium_challenge_list"
                            android:minHeight="@dimen/podium_challenge_list_item_min_height">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/knowledge_race_ic_bg"
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="@dimen/activity_margin"
                                android:adjustViewBounds="true"
                                android:src="@drawable/bg_podium_challenge_item_ellipse"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/knowledge_race_ic"
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/element_spacing"
                                android:adjustViewBounds="true"
                                android:backgroundTint="@color/colorPrimary"
                                android:src="@drawable/ic_podium_challenge_knowledge_race"
                                app:layout_constraintBottom_toBottomOf="@+id/knowledge_race_ic_bg"
                                app:layout_constraintEnd_toEndOf="@id/knowledge_race_ic_bg"
                                app:layout_constraintStart_toStartOf="@+id/knowledge_race_ic_bg"
                                app:layout_constraintTop_toTopOf="@+id/knowledge_race_ic_bg" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@drawable/bg_podium_challenge_list_corner_circle"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/knowledge_race_for_speakers_text"
                                style="@style/TextAppearance.Flashat.Headline6"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginStart="80dp"
                                android:layout_marginTop="@dimen/element_spacing"
                                android:text="@string/podium_challenge_knowledge_title"
                                android:textColor="@color/colorSecondary"
                                app:layout_constraintEnd_toStartOf="@+id/knowledge_race_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/knowledge_race_for_speakers_description"
                                style="@style/TextAppearance.Flashat.Label.Smaller"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/activity_margin"
                                android:layout_marginTop="@dimen/activity_margin"
                                android:text="@string/podium_knowledge_race_sub_text"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toTopOf="@+id/btn_detail_knowledge_race"
                                app:layout_constraintEnd_toStartOf="@id/knowledge_race_ic_bg"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/knowledge_race_for_speakers_text"
                                app:layout_constraintVertical_chainStyle="packed" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btn_detail_knowledge_race"
                                style="@style/Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@string/common_details"
                                android:textAllCaps="false"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                tools:text="Details" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>