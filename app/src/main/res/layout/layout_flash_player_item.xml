<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">



    <data>
        <import type="com.app.messej.data.model.entity.FlashVideo.FlashType"/>
        <variable name="flash" type="com.app.messej.data.model.entity.FlashVideo" />
        <variable name="stats" type="com.app.messej.data.model.FlashVideoStats" />
        <variable name="isPaused" type="Boolean" />
        <variable name="isSelf" type="Boolean" />
        <variable name="isGiftHidden" type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.media3.ui.PlayerView
                android:id="@+id/player_view"
                style="@style/Widget.Flashat.VideoPlayer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:use_controller="true"
                android:clickable="true"
                android:longClickable="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/play_button"
            style="@style/Widget.Flashat.Flash.VideoPlayButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:icon="@drawable/ic_media_play_large"
            goneIfNot="@{isPaused}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_video_bottom_overlay"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/spacer" />

        <View
            android:id="@+id/spacer"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            app:layout_constraintBottom_toTopOf="@+id/player_footer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


<!--
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/gift_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginStart="@dimen/activity_margin"
            android:background="@drawable/bg_send_gift"
            android:padding="@dimen/element_spacing"
            goneIf="@{flash.flashType==FlashType.MINE}"
            android:layout_marginBottom="@dimen/line_spacing"
            app:layout_constraintBottom_toTopOf="@+id/player_footer"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/img_gift_flash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:src="@drawable/ic_podium_gift_circle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/TextAppearance.AppCompat.Caption"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginEnd="8dp"
                android:textColor="@color/textColorOnPrimary"
                android:text="@string/title_send_gift"
                app:layout_constraintBottom_toBottomOf="@+id/img_gift_flash"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/img_gift_flash"
                app:layout_constraintTop_toTopOf="@+id/img_gift_flash" />
        </androidx.constraintlayout.widget.ConstraintLayout>
-->

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cannot_play_overlay"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="#AA000000"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/flash_thumb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:src="@drawable/bg_chat_background"
                app:imageUrl="@{flash.thumbnailUrl}"
                app:flashBlocked="@{!flash.isAvailable}"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="9:16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/reported_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/element_spacing"
                android:text="@string/flash_item_status_reported"
                app:goneIfNot="@{flash.reported}"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.Flashat.Label"
                android:textColor="@color/textColorOnPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/blocked_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/element_spacing"
                app:goneIfNot="@{flash.isBlocked}"
                android:text="@string/flash_item_status_blocked"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.Flashat.Label"
                android:textColor="@color/textColorOnPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/deleted_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/element_spacing"
                app:goneIfNot="@{flash.isDeleted}"
                android:text="@string/flash_item_status_deleted"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.Flashat.Label"
                android:textColor="@color/textColorOnPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/player_footer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:layout_marginBottom="@dimen/activity_margin"
            android:layout_marginStart="@dimen/activity_margin"
            android:layout_marginEnd="@dimen/element_spacing"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/flash_actions"
            app:layout_constraintStart_toStartOf="parent">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/user_dp"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginVertical="@dimen/element_spacing"
                android:layout_marginStart="@dimen/element_spacing"
                android:elevation="2dp"
                android:scaleType="centerCrop"
                android:clickable="true"
                app:imageUrl="@{flash.senderDetails.thumbnail}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                app:riv_corner_radius="32dp"
                tools:src="@drawable/im_user_placeholder_opaque" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/live_podium_indicator"
                android:layout_width="10dp"
                android:layout_height="10dp"
                app:srcCompat="@drawable/ic_dot"
                android:tint="@color/colorPass"
                android:elevation="4dp"
                app:layout_constraintEnd_toEndOf="@id/user_dp"
                app:layout_constraintBottom_toBottomOf="@id/user_dp"
                app:visibleIf="@{stats.userLivePodium &amp;&amp; !isSelf}"
                tools:visibility="visible" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/superstar_premium_badge"
                android:layout_width="@dimen/premium_badge_size"
                android:layout_height="@dimen/premium_badge_size"
                android:elevation="4dp"
                app:userBadge="@{flash.senderDetails.userBadge}"
                app:layout_constraintStart_toStartOf="@id/user_dp"
                app:layout_constraintTop_toTopOf="@id/user_dp"
                tools:src="@drawable/ic_user_badge_premium" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_name"
                style="@style/TextAppearance.Flashat.Headline6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{flash.senderDetails.name}"
                android:textColor="@color/textColorOnPrimary"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/user_dp"
                app:layout_constraintStart_toEndOf="@+id/user_dp"
                app:layout_constraintTop_toTopOf="@+id/user_dp"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Mathew Varghese" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/flash_country_flag"
                android:layout_width="wrap_content"
                android:layout_height="12dp"
                android:layout_marginStart="8dp"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="@+id/user_name"
                app:layout_constraintStart_toEndOf="@+id/user_name"
                app:layout_constraintTop_toTopOf="@+id/user_name"
                app:srcCompat="@drawable/bg_add_flash_button"
                tools:srcCompat="@drawable/flag_france" />

            <com.webtoonscorp.android.readmore.ReadMoreTextView
                android:id="@+id/feed_text"
                style="Widget.Flashat.Postat.Message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/element_spacing"
                android:text="@{flash.caption}"
                app:readLessTextColor="@color/colorAlwaysLightSecondary"
                app:readMoreTextColor="@color/colorAlwaysLightSecondary"
                android:textColor="@color/textColorOnPrimary"
                app:goneIfNullOrBlank="@{flash.caption}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/user_name"
                app:layout_constraintTop_toBottomOf="@+id/user_name"
                app:readLessText="@string/hide"
                app:readMoreText="@string/see_more"
                tools:text="Hi Everyone. This is a text caption" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/flash_actions"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginEnd="@dimen/activity_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/gift_button"
                style="@style/Widget.Flashat.Flash.FlashOverlayButton.NoTint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/element_spacing"
                app:goneIf="@{isGiftHidden}"
                android:text="@{stats.giftCountFormatted}"
                app:icon="@drawable/ic_podium_speaker_gift_circle"
                 />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/comment_button"
                style="@style/Widget.Flashat.Flash.FlashOverlayButton.NoTint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{stats.commentCountFormatted}"
                app:goneIf="@{stats.commentDisabled}"
                android:layout_marginBottom="@dimen/element_spacing"
                tools:text="46"
                app:icon="@drawable/ic_flash_comment" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/share_button"
                style="@style/Widget.Flashat.Flash.FlashOverlayButton.NoTint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/common_share"
                android:visibility="visible"
                app:goneIfNot="@{flash.senderDetails.premiumUser}"
                app:andGoneIfNullOrBlank="@{stats.shareLink}"
                android:layout_marginBottom="@dimen/element_spacing"
                app:icon="@drawable/ic_flash_share" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/more_button"
                style="@style/Widget.Flashat.Flash.FlashOverlayButton.NoTint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                app:icon="@drawable/ic_flash_more" />

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
