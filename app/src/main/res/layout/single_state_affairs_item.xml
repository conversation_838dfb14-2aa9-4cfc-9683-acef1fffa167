<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="headerText"
            type="String" />

        <variable
            name="headerTextColor"
            type="Integer" />

        <variable
            name="isList"
            type="Boolean" />

        <import type="com.app.messej.data.model.enums.StateAffairsTypes" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/line_spacing">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/line_spacing"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_header"
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:text="@{headerText}"
                android:textAllCaps="true"
                android:textColor="@{headerTextColor}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="HI Good"
                tools:textColor="@color/textColorPrimary" />

            <View
                android:id="@+id/divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:background="@color/textColorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/text_viewAll"
                app:layout_constraintStart_toEndOf="@id/text_header"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_viewAll"
                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/line_spacing"
                android:text="@string/state_affair_view_all"
                android:textAllCaps="true"
                android:textColor="@color/textColorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/image_viewAll"
                app:layout_constraintStart_toEndOf="@id/divider"
                app:layout_constraintTop_toTopOf="parent"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/image_viewAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:src="@drawable/ic_view_all_arrow_right"
                android:tint="@color/textColorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/text_viewAll"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/list_layout"
            goneIfNot="@{isList}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/element_spacing"
            app:layout_constraintTop_toBottomOf="@id/layout_header">

            <com.kennyc.view.MultiStateView
                android:id="@+id/multiStateView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:msv_emptyView="@layout/layout_list_state_empty"
                app:msv_loadingView="@layout/layout_eds_state_loading"
                app:msv_viewState="loading">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/list_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </com.kennyc.view.MultiStateView>

        </androidx.constraintlayout.widget.ConstraintLayout>

<HorizontalScrollView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintTop_toBottomOf="@id/layout_header">

    <LinearLayout
        android:id="@+id/layout_count"
        goneIfNot="@{!isList}"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/line_spacing"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <androidx.cardview.widget.CardView
            android:id="@+id/card_golden"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/line_spacing"
            android:layout_marginHorizontal="@dimen/element_spacing">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/total_golden"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/citizenship_golden"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:text="@string/golden_type"
                    android:textColor="@color/colorPrimaryColorDarkest1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/golden_type"
                    tools:textColor="@color/colorPrimaryColorDarkest1" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_total_golden"
                    style="@style/TextAppearance.Flashat.Subtitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/double_margin"
                    android:text="@string/state_affair_total"
                    android:textColor="@color/colorPrimaryColorDarkest1"
                    app:layout_constraintStart_toStartOf="@id/citizenship_golden"
                    app:layout_constraintTop_toBottomOf="@+id/citizenship_golden" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/total_user_count_golden"
                    style="@style/TextAppearance.Flashat.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textColor="@color/white"
                    tools:textColor="@color/colorPrimary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_golden"
                    app:layout_constraintTop_toBottomOf="@+id/text_total_golden"
                    tools:text="220"
                    android:paddingBottom="@dimen/activity_margin"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
        <androidx.cardview.widget.CardView
            android:id="@+id/card_citizen"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/line_spacing"
            android:layout_marginHorizontal="@dimen/element_spacing">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/total_citizens"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/citizenship_citizen"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:text="@string/state_affairs_citizens"
                    android:textColor="@color/colorPrimaryColorDarkest1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/state_affairs_citizens"
                    tools:textColor="@color/colorPrimaryColorDarkest1" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_total_citizen"
                    style="@style/TextAppearance.Flashat.Subtitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/double_margin"
                    android:text="@string/state_affair_total"
                    android:textColor="@color/colorPrimaryColorDarkest1"
                    app:layout_constraintStart_toStartOf="@id/citizenship_citizen"
                    app:layout_constraintTop_toBottomOf="@+id/citizenship_citizen" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/total_user_count_citizen"
                    style="@style/TextAppearance.Flashat.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_citizen"
                    app:layout_constraintTop_toBottomOf="@+id/text_total_citizen"
                    tools:text="220"
                    android:paddingBottom="@dimen/activity_margin"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/card_resident"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/line_spacing"
            android:padding="@dimen/line_spacing"
            android:layout_marginHorizontal="@dimen/element_spacing">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/total_resident"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/citizenship_resident"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:textColor="@color/textColorBusinessPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/state_affairs_Residents"
                    android:text="@string/state_affairs_Residents"
                    tools:textColor="@color/textColorBusinessPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_total_resident"
                    style="@style/TextAppearance.Flashat.Subtitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/double_margin"
                    android:text="@string/state_affair_total"
                    android:textColor="@color/textColorBusinessPrimary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_resident"
                    app:layout_constraintTop_toBottomOf="@+id/citizenship_resident"
                    tools:textColor="@color/textColorBusinessPrimary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/total_user_count_resident"
                    style="@style/TextAppearance.Flashat.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_resident"
                    app:layout_constraintTop_toBottomOf="@+id/text_total_resident"
                    tools:text="220"
                    android:paddingBottom="@dimen/activity_margin"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/card_visitor"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/element_spacing"
            app:cardElevation="@dimen/line_spacing"
            android:padding="@dimen/line_spacing"
            android:layout_marginHorizontal="@dimen/element_spacing">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/total_visitor"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/citizenship_visitor"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/activity_margin"
                    android:textColor="@color/textColorOnSecondary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/state_affairs_visitors"
                    android:text="@string/state_affairs_visitors"
                    tools:textColor="@color/textColorOnSecondary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_total_visitor"
                    style="@style/TextAppearance.Flashat.Subtitle2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/double_margin"
                    android:text="@string/state_affair_total"
                    android:textColor="@color/textColorOnSecondary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_visitor"
                    app:layout_constraintTop_toBottomOf="@+id/citizenship_visitor"
                    tools:textColor="@color/textColorOnSecondary" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/total_user_count_visitor"
                    style="@style/TextAppearance.Flashat.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintStart_toStartOf="@id/citizenship_visitor"
                    app:layout_constraintTop_toBottomOf="@+id/text_total_visitor"
                    tools:text="220"
                    android:paddingBottom="@dimen/activity_margin"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>


    </LinearLayout>
</HorizontalScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>