<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="com.app.messej.ui.premium.UpgradeGoldenViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_gradient_upgrade_premium"
            tools:context=".ui.premium.UpgradePremiumFragment">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scaleType="fitEnd"
                android:src="@drawable/bg_upgrade"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/nestedScrollView" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/nestedScrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <Button
                        android:id="@+id/back"
                        style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/double_margin"
                        app:icon="@drawable/ic_search_location_back"
                        app:iconTint="@color/textColorOnPrimary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/flashat_text"
                        style="@style/TextAppearance.Flashat.Headline1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/double_margin"
                        android:layout_marginTop="@dimen/extra_margin"
                        android:text="@string/upgrade_flashat"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/back" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/premium_text"
                        style="@style/TextAppearance.Flashat.Headline4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/double_margin"
                        android:text="@string/upgrade_premium"
                        android:textColor="@color/textColorOnPrimary"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/flashat_text" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/double_margin"
                        android:adjustViewBounds="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_upgrade_golden_crown"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/flashat_text" />


                    <include
                        android:id="@+id/content_golden"
                        android:layout_height="wrap_content"
                       android:layout_width="0dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/premium_text"
                        android:layout_margin="@dimen/extra_margin"
                        android:paddingBottom="@dimen/element_spacing"
                        layout="@layout/layout_upgrade_premium_view_pager">

                    </include>


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_upgrade"
                        style="@style/Widget.Flashat.SmallRoundedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/double_margin"
                        android:layout_marginBottom="@dimen/extra_margin"
                        android:background="@drawable/bg_upgrade_golden"
                        app:backgroundTint="@null"
                        android:text="@string/common_upgrade_now"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/content_golden" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.core.widget.NestedScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>


</layout>