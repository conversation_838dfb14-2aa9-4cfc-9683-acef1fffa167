<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.app.messej.ui.premium.SubscribeGoldenBottomSheetViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_details"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/activity_margin"
           app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="@string/header_golden_subscribe"
                android:textColor="@color/colorPrimary"
                style="@style/TextAppearance.Flashat.Subtitle1"
                app:layout_constraintTop_toTopOf="parent"/>




            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/header_start_date"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/create_poll_label_start_date"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/double_margin"
                android:layout_marginStart="@dimen/double_margin"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_header"/>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_start_date"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@{viewModel.startDate}"
                tools:text="19/11/2024"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/double_margin"
                android:layout_marginEnd="@dimen/double_margin"
                app:layout_constraintTop_toBottomOf="@id/text_header"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/header_end_date"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/id_card_title_expiry_date"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/activity_margin"
                app:layout_constraintStart_toStartOf="@id/header_start_date"
                app:layout_constraintTop_toBottomOf="@id/header_start_date"/>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_end_date"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@{viewModel.expiryDate}"
                tools:text="19/11/2025"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/activity_margin"
                android:layout_marginEnd="@dimen/double_margin"
                app:layout_constraintTop_toBottomOf="@id/header_start_date"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/header_flix_amount"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/flax_amount"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/activity_margin"
                app:layout_constraintStart_toStartOf="@id/header_start_date"
                app:layout_constraintTop_toBottomOf="@id/header_end_date"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_flix_amount"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="36FLiX/Year"
                android:text="@{@string/flix_subscription_amount(viewModel.price)}"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/textColorPrimary"
                android:layout_marginTop="@dimen/activity_margin"
                android:layout_marginEnd="@dimen/double_margin"
                app:layout_constraintTop_toBottomOf="@id/header_end_date"/>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/flix_subscription_note"
                style="@style/TextAppearance.Flashat.Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/golden_subscribe_note"
                android:textColor="@color/colorPodiumSpeakerResident"
                android:layout_marginTop="@dimen/extra_margin"
                app:layout_constraintStart_toStartOf="@id/header_start_date"
                app:layout_constraintEnd_toEndOf="@id/text_flix_amount"
                app:layout_constraintTop_toBottomOf="@id/header_flix_amount"/>


            <com.google.android.material.button.MaterialButton
                android:id="@+id/purchase_confirm_button"
                style="@style/Widget.Flashat.LargeRoundedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/double_margin"
                android:layout_marginTop="@dimen/extra_margin"
                android:text="@string/flix_purchase_subscribe"
                app:layout_constraintTop_toBottomOf="@id/flix_subscription_note"
                app:layout_constraintVertical_chainStyle="packed"
                android:layout_marginBottom="@dimen/line_spacing"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>