<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable name="isVisitorOrResident" type="Boolean" />
        <variable name="isPresident" type="Boolean" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/profile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:isIconHidden="@{true}"
                    app:title="@{@string/bottom_sheet_profile}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/your_tribe"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    goneIf="@{isVisitorOrResident}"
                    app:layout_constraintTop_toBottomOf="@id/profile"
                    app:title="@{@string/account_management_your_tribe}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/levels"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/your_tribe"
                    app:title="@{@string/account_management_levels}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/rating"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/levels"
                    app:title="@{@string/account_management_rating}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/premium_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/rating"
                    app:title="@{@string/account_management_premium_status}"
                    layout="@layout/item_settings_drawer" />

               <!-- <include
                    android:id="@+id/golden_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/premium_status"
                    app:title="@{@string/account_management_golden_status}"
                    goneIf="@{isPresident}"
                    layout="@layout/item_settings_drawer" />-->

                <include
                    android:id="@+id/invite_friend"
                    android:layout_width="match_parent"
                    app:isIconHidden="@{true}"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@id/premium_status"
                    app:title="@{@string/bottom_sheet_invite_friend}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/location"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/invite_friend"
                    app:title="@{@string/settings_title_locations}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/switch_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    app:layout_constraintTop_toBottomOf="@id/location"
                    app:title="@{@string/account_management_switch_account}"
                    layout="@layout/item_settings_drawer" />

                <include
                    android:id="@+id/restore_your_rating"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:isIconHidden="@{true}"
                    goneIf="@{isVisitorOrResident}"
                    app:layout_constraintTop_toBottomOf="@id/switch_account"
                    app:title="@{@string/account_management_restore_your_rating}"
                    layout="@layout/item_settings_drawer" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>