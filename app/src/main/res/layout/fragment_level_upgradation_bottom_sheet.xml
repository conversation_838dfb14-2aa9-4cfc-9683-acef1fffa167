<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.gift.bottomSheet.LevelUpgradationBottomSheetViewModel" />
        <variable
            name="userType"
            type="String" />

        <variable
            name="idCardDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="userLevelBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="textIdCardDrawBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="sendGiftBackground"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="idColor"
            type="Integer" />

        <variable
            name="usertypeColor"
            type="Integer" />

        <variable
            name="labelTextColor"
            type="Integer" />

        <variable
            name="iconTintColor"
            type="Integer" />

        <variable
            name="textColor"
            type="Integer" />

        <variable
            name="headerTextColor"
            type="Integer" />

        <variable
            name="userIconTint"
            type="Integer" />

        <variable
            name="isResident"
            type="boolean" />

        <variable
            name="userIconTextTint"
            type="Integer" />


        <variable
            name="isPresident"
            type="boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        goneIfNull="@{viewModel.profile}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@{userLevelBackground}"
        android:paddingBottom="@dimen/double_margin"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/bg_idcard_citizen">


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_level_upgradation_close"
        style="@style/Widget.Flashat.Button.TextButton.IconOnly"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:icon="@drawable/ic_close"
        app:iconTint="@{textColor}"
        tools:iconTint="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/btn_level_upgradation_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_layout_Self"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            goneIfNot="@{viewModel.isSelf}"
            tools:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_congrts"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/TextAppearance.Flashat.Headline4"
                android:textSize="32sp"
                android:textColor="@{headerTextColor}"
                tools:text="@string/level_upgradation_title"
                android:text="@string/level_upgradation_title"
                tools:textColor="@color/colorPrimary"
                android:textAllCaps="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_you_are"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/TextAppearance.Flashat.Headline4"
                android:textSize="26sp"
                android:textStyle="normal"
                android:textColor="@{headerTextColor}"
                tools:textColor="@color/colorPrimary"
                app:layout_constraintTop_toBottomOf="@+id/text_congrts"
                android:textAllCaps="true"
                android:text="@string/level_upgradation_self_title"
                android:layout_marginTop="@dimen/activity_margin"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_citizenship_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/TextAppearance.Flashat.Headline4"
                android:textSize="26sp"
                android:textColor="@{headerTextColor}"
                tools:textColor="@color/colorPrimary"
                app:layout_constraintTop_toBottomOf="@+id/text_you_are"
                android:text="@{userType}"
                tools:text="@string/user_citizenship_citizen"
                android:textAllCaps="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header_layout_others"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible"
            goneIf="@{viewModel.isSelf}">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_user_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                style="@style/TextAppearance.Flashat.Headline4"
                android:ellipsize="end"
                android:maxLines="1"
                android:textStyle="bold"
                android:textColor="@{headerTextColor}"
                app:flow_horizontalAlign="start"
                app:layout_constrainedWidth="true"
                tools:textColor="@color/colorPrimary"
                android:text="@{viewModel.profile.name}"
                tools:text="Its Me Sholin For You"
                android:textAllCaps="true"
                android:gravity="center"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_is_now"
                style="@style/TextAppearance.Flashat.Headline4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAllCaps="true"
                android:textColor="@{headerTextColor}"
                android:textStyle="normal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_user_name"
                tools:text="is Now Citizen"
                tools:textColor="@color/colorPrimary" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/send_gift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/text_is_now"
                app:layout_constraintStart_toStartOf="@id/text_is_now"
                app:layout_constraintEnd_toEndOf="@id/text_is_now"
                android:background="@{sendGiftBackground}"
                android:padding="@dimen/element_spacing"
                android:paddingEnd="@dimen/activity_margin"
                goneIf="@{viewModel.isSelf}"
                tools:background="@drawable/bg_level_upgrade_send_gift_others">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_send_gift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    android:src="@drawable/ic_gift"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:tint="@color/colorPrimary"
                    goneIf="@{viewModel.isSelf}"
                    android:layout_marginStart="@dimen/line_spacing"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_send_gift"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textAllCaps="true"
                    android:textColor="@{isPresident?@color/colorPrimaryColorDarkest1:@color/colorPrimary}"
                    tools:textColor="@color/colorPrimary"
                    android:text="@string/title_send_gift"
                    goneIf="@{viewModel.isSelf}"
                    android:layout_marginStart="@dimen/line_spacing"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_send_gift"
                    android:layout_marginEnd="@dimen/activity_margin"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardView_header"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/activity_margin"
        android:layout_marginVertical="@dimen/double_margin"
        app:cardCornerRadius="@dimen/element_spacing"
        app:strokeColor="@{isPresident?null:textColor}"
        tools:strokeColor="@color/colorPrimary"
        app:strokeWidth="2dp"
        app:cardElevation="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_layout">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@{idCardDrawBackground}">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine_main"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent=".56" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent=".24" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent=".75" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine_user"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent=".29" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine_End"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent=".89" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_idCard"
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/activity_margin"
                android:textAllCaps= "@{isPresident ? true : false}"
                android:background="@{isResident==true?@drawable/bg_idcard_resident_white : textIdCardDrawBackground}"
                android:paddingHorizontal="@dimen/element_spacing"
                android:paddingVertical="@dimen/line_spacing"
                android:text="@{isPresident ? @string/title_id_card : @string/id_card_header_identity_card}"
                android:textColor="@{isResident==true?@color/textColorOnSecondary: idColor}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/id_card_header_identity_card"
                tools:background="@drawable/bg_label_idcard_citizen_ambassador"
                tools:textColor="@color/white" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_citizenship"
                style="@style/TextAppearance.Flashat.Headline5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{userType}"
                android:textAllCaps= "@{isPresident ? true : false}"
                android:textColor="@{usertypeColor}"
                app:layout_constraintBottom_toBottomOf="@id/text_idCard"
                app:layout_constraintTop_toTopOf="@id/text_idCard"
                app:layout_constraintStart_toStartOf="@id/guideLine_main"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="@dimen/line_spacing"
                tools:text="Citizen"
                tools:textColor="@color/colorPrimary" />



            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_name_title"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_margin"
                android:layout_marginTop="@dimen/element_spacing"
                tools:layout_marginStart="@dimen/activity_margin"
                android:layout_marginEnd="@dimen/line_spacing"
                android:text="@string/title_nickname_name"
                android:textColor="@{labelTextColor}"
                app:layout_constraintEnd_toStartOf="@id/text_name_barrier"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/guideLine_top"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_name_barrier"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=":"
                android:textColor="@{textColor}"
                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_name_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@id/txt_id_card_name_title"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_name"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{viewModel.profile.name}"
                android:textColor="@{textColor}"
                app:flow_horizontalAlign="start"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_name_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@+id/txt_id_card_name_title"
                tools:text="Sholin"
                tools:textColor="@color/colorPrimaryDark" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_user_title"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/line_spacing"
                android:text="@string/id_card_title_id_code"
                android:textColor="@{labelTextColor}"
                app:layout_constraintEnd_toStartOf="@id/text_idcode_barrier"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_name"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_idcode_barrier"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=":"
                android:textColor="@{textColor}"
                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_user_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@id/txt_id_card_user_title"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_user"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{viewModel.profile.username}"
                android:textColor="@{textColor}"
                app:flow_horizontalAlign="start"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_user_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@+id/txt_id_card_user_title"
                tools:text="Sholin88"
                tools:textColor="@color/colorPrimaryDark" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_nickname_title"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/line_spacing"
                android:text="@string/title_id_card_nick_name"
                android:textColor="@{labelTextColor}"
                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                app:layout_constraintEnd_toStartOf="@id/text_nickName_barrier"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_user_title"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_nickName_barrier"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=":"
                android:textColor="@{textColor}"
                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_nickname_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@id/txt_id_card_nickname_title"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_nickName"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{viewModel.userNickName.nickName}"
                android:textColor="@{textColor}"
                app:flow_horizontalAlign="start"
                app:goneIfNot="@{viewModel.isCurrentUser()==false}"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_nickname_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@+id/txt_id_card_nickname_title"
                tools:text="hey da"
                tools:textColor="@color/colorPrimaryDark" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_dage_title"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/line_spacing"
                android:text="@string/id_card_title_flashat_age"
                android:textColor="@{labelTextColor}"
                app:layout_constraintEnd_toStartOf="@id/text_flashatAge_barrier"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_nickname_title"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_flashatAge_barrier"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=":"
                android:textColor="@{textColor}"
                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_dage_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@id/txt_id_card_dage_title"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_dage"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{String.valueOf(viewModel.profile.dage)}"
                android:textColor="@{textColor}"
                app:flow_horizontalAlign="start"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_dage_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_main"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@+id/txt_id_card_dage_title"
                tools:text="700"
                tools:textColor="@color/colorPrimaryDark" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_flag_title"
                style="@style/TextAppearance.Flashat.Label.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/line_spacing"
                android:text="@string/id_card_title_flag"
                android:textColor="@{labelTextColor}"
                app:layout_constraintEnd_toStartOf="@id/text_flashatAge_barrier"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_name_title"
                app:layout_constraintTop_toBottomOf="@+id/txt_id_card_dage_title"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_flashatFlag_barrier"
                style="@style/TextAppearance.Flashat.Label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=":"
                android:textColor="@{textColor}"
                app:layout_constraintBottom_toBottomOf="@id/txt_id_card_flag_title"
                app:layout_constraintEnd_toEndOf="@id/guideLine_user"
                app:layout_constraintTop_toTopOf="@id/txt_id_card_flag_title"
                tools:textColor="@color/textColorPrimary" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/id_card_flag"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:layout_marginStart="@dimen/element_spacing"
                android:adjustViewBounds="true"
                goneIfNot="@{viewModel.accountDetails.showCountryFlag}"
                app:layout_constraintBottom_toBottomOf="@+id/txt_id_card_flag_title"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/guideLine_user"
                app:layout_constraintTop_toTopOf="@+id/txt_id_card_flag_title"
                tools:srcCompat="@drawable/flag_france" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/superstar_premium_badge"
                goneIfNot="@{viewModel.profile.premiumUser}"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:elevation="4dp"
                android:src="@drawable/ic_user_badge_premium"
                app:layout_constraintStart_toStartOf="@+id/id_card_dp"
                app:layout_constraintTop_toTopOf="@+id/id_card_dp" />

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/id_card_dp"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="@dimen/element_spacing"
                android:layout_marginBottom="@dimen/element_spacing"
                android:scaleType="centerCrop"
                app:imageUrl="@{viewModel.isCurrentUser()? viewModel.currentUserProfile.thumbnail : viewModel.profile.thumbnail}"
                app:layout_constraintBottom_toTopOf="@id/guideLine_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/guideLine_main"
                app:layout_constraintTop_toBottomOf="@+id/guideLine_top"
                app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                app:riv_border_color="@color/white"
                app:riv_border_width="3dp"
                app:riv_corner_radius="@dimen/drawer_header_dp_size"
                tools:src="@drawable/im_user_placeholder_opaque" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_issue_date_title"
                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginTop="@dimen/element_spacing"
                android:text="@string/id_card_title_issue_date"
                android:textColor="@{ isPresident ? idColor : textColor}"
                app:layout_constraintEnd_toStartOf="@id/txt_id_card_expiry_date_title"
                app:layout_constraintStart_toStartOf="@id/guideLine_main"
                app:layout_constraintTop_toBottomOf="@+id/guideLine_bottom"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_issue_date"
                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.profile.issueDate}"
                android:textColor="@{isPresident ? idColor : textColor}"
                app:layout_constraintEnd_toEndOf="@id/txt_id_card_issue_date_title"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_issue_date_title"
                app:layout_constraintTop_toBottomOf="@id/txt_id_card_issue_date_title"
                tools:text="01/01/24"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_expiry_date_title"
                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                goneIfNot="@{viewModel.profile.premiumUser}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginTop="@dimen/element_spacing"
                android:text="@string/id_card_title_expiry_date"
                android:textColor="@{ isPresident ? idColor : textColor}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/txt_id_card_issue_date_title"
                app:layout_constraintTop_toBottomOf="@+id/guideLine_bottom"
                tools:textColor="@color/textColorPrimary" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/txt_id_card_expiry_date"
                style="@style/TextAppearance.Flashat.Label.Smaller.Bold"
                goneIfNot="@{viewModel.profile.premiumUser}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{viewModel.profile.subscriptionExpiryDate}"
                android:textColor="@{isPresident ? idColor : textColor}"
                app:layout_constraintEnd_toEndOf="@id/txt_id_card_expiry_date_title"
                app:layout_constraintStart_toStartOf="@id/txt_id_card_expiry_date_title"
                app:layout_constraintTop_toBottomOf="@id/txt_id_card_expiry_date_title"
                tools:text="05/08/24"
                tools:textColor="@color/textColorPrimary" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>