<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />

        <variable name="hasChallengeOverlay" type="Boolean" />
        <variable name="speakersCollapsed" type="Boolean" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:applySystemBarInsets="@{`ime|bottom`}"
                android:background="@color/colorSurface">

                <include layout="@layout/layout_podium_header"
                    android:id="@+id/header"
                    app:viewModel="@{viewModel}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/ticker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:composableName="com.app.messej.ui.home.promobar.PromoBarKt.PromoBarPreviewSingle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/header"/>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/action_decor_holder_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingTop="2dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ticker">

                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/speaker_top_barrier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:barrierAllowsGoneWidgets="true"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="action_decor_holder_top"/>

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/speaker_split_line"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <include
                    android:id="@+id/main_screen"
                    layout="@layout/item_podium_speaker_main"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintDimensionRatio="1:2"
                    app:goneIf="@{hasChallengeOverlay}"
                    app:layout_constraintEnd_toStartOf="@+id/speaker_split_line"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/speaker_top_barrier"
                    tools:visibility="gone" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/speaker_challenge_overlay"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_top_barrier"
                    tools:background="@color/colorAlwaysDarkSurfaceSecondary"
                    tools:visibility="gone" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/speaker_bottom_barrier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:barrierAllowsGoneWidgets="true"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="main_screen,speaker_challenge_overlay"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/challenge_holder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_bottom_barrier"
                    tools:visibility="visible">

                    <include
                        android:id="@+id/layout_podium_challenge_board"
                        layout="@layout/layout_podium_challenge_board"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/line_spacing"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.kennyc.view.MultiStateView
                    android:id="@+id/live_chat_multiStateView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@id/footer"
                    app:layout_constraintEnd_toStartOf="@+id/waiting_holder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/challenge_holder"
                    app:msv_emptyView="@layout/layout_list_state_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/live_chat"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/line_spacing"
                        tools:itemCount="6"
                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_podium_live_chat"/>

                </com.kennyc.view.MultiStateView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/waiting_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/line_spacing"
                    android:layout_marginEnd="@dimen/line_spacing"
                    app:cardCornerRadius="4dp"
                    app:cardBackgroundColor="@color/colorSurfaceSecondaryDark"
                    app:goneIf="@{viewModel.challengeActive}"
                    app:layout_constraintBottom_toTopOf="@+id/footer"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/speaker_bottom_barrier"
                    tools:visibility="visible">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="70dp"
                        android:layout_height="match_parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/waiting_mic_icon_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/colorSurfaceSecondaryDarker"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/action_speak"
                                style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/element_spacing"
                                android:layout_marginVertical="@dimen/line_spacing"
                                app:backgroundTint="@color/colorPrimary"
                                app:clickable="@{viewModel.showRequestToSpeak}"
                                app:icon="@drawable/ic_podium_mic_speaker_on"
                                app:iconTint="@color/white"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:visibility="visible" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/waiting_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="@dimen/line_spacing"
                                android:text="@{viewModel.waitersCount}"
                                android:textAppearance="@style/TextAppearance.Flashat.Subtitle2"
                                android:textColor="@color/colorPrimaryDark"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/action_speak"
                                app:layout_goneMarginTop="@dimen/line_spacing"
                                tools:text="12" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <com.kennyc.view.MultiStateView
                            android:id="@+id/waiting_list_multiStateView"
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            app:layout_constraintBottom_toTopOf="@id/view_all_button"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/waiting_mic_icon_layout"
                            app:layout_constraintVertical_bias="0"
                            app:msv_emptyView="@layout/layout_eds_state_loading_podium_speakers"
                            app:msv_errorView="@layout/layout_list_state_error"
                            app:msv_loadingView="@layout/layout_eds_state_loading_podium_waiters"
                            app:msv_viewState="content">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/waiting_list"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:paddingTop="@dimen/element_spacing"
                                tools:itemCount="6"
                                tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:listitem="@layout/item_podium_waiting_speaker" />

                        </com.kennyc.view.MultiStateView>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/view_all_button"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="@dimen/element_spacing"
                            android:insetBottom="0dp"
                            android:insetTop="0dp"
                            app:goneIfNot="@{viewModel.iAmElevated &amp;&amp; viewModel.waiters.size()>0}"
                            android:text="@string/podium_waiting_list_view_all"
                            style="@style/Widget.Flashat.TinyRoundedButton.Primary"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            tools:visibility="visible" />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </com.google.android.material.card.MaterialCardView>

                <include
                    android:id="@+id/likes_container"
                    layout="@layout/item_podium_likes_container" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chat_overlay"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="visible"
                    app:layout_constraintEnd_toEndOf="@id/live_chat_multiStateView"
                    app:layout_constraintBottom_toBottomOf="@id/live_chat_multiStateView"
                    app:layout_constraintStart_toStartOf="@id/live_chat_multiStateView"
                    app:layout_constraintTop_toTopOf="@id/live_chat_multiStateView" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/speakers_group"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintDimensionRatio="1:2"
                    android:background="@{hasChallengeOverlay?@color/colorDpBorder:@color/transparent}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/speaker_top_barrier"
                    app:layout_constraintStart_toEndOf="@id/speaker_split_line"
                    tools:translationX="0dp"
                    tools:visibility="gone">

                    <com.kennyc.view.MultiStateView
                        android:id="@+id/speaker_list_multiStateView"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:msv_emptyView="@layout/layout_list_state_empty"
                        app:msv_errorView="@layout/layout_list_state_error"
                        app:msv_loadingView="@layout/layout_eds_state_loading_podium_speakers_lecture"
                        app:msv_viewState="content">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/speaker_list"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:itemCount="8"
                            tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            tools:listitem="@layout/item_podium_speaker"
                            tools:spanCount="2" />

                    </com.kennyc.view.MultiStateView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/footer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/colorSurface"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.kennyc.view.MultiStateView
                        android:id="@+id/chat_textBox_multiStateView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        tools:visibility="gone"
                        android:background="@color/colorSurface"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/like_container"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:paddingTop="@dimen/activity_margin"
                        app:msv_emptyView="@layout/layout_podium_chat_paused_empty"
                        app:msv_errorView="@layout/layout_list_state_error"
                        app:msv_viewState="content">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/chat_input_holder"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:elevation="6dp"
                            app:goneIfNot="@{viewModel.canSendChats}">

                            <com.google.android.material.textfield.TextInputLayout
                                android:id="@+id/chat_input"
                                style="@style/Widget.Flashat.GreyTextInput"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/element_spacing"
                                android:layout_marginEnd="@dimen/line_spacing"
                                app:hintAnimationEnabled="false"
                                app:hintEnabled="false"
                                app:counterEnabled="true"
                                app:counterMaxLength="150"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/chat_send_button"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/input_comment"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:hint="@string/chat_input_hint"
                                    android:inputType="textShortMessage|textMultiLine"
                                    android:focusableInTouchMode="@{!viewModel.challengeRunning &amp;&amp; viewModel.canCommentByUserRating}"
                                    android:clickable="true"
                                    android:maxLines="1"
                                    android:maxLength="150"
                                    android:text="@={viewModel.chatText}"
                                    android:longClickable="false"
                                    android:textIsSelectable="false"/>

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/chat_send_button"
                                style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:icon="@drawable/ic_chat_send"
                                app:iconTint="@color/colorPrimary"
                                android:enabled="@{viewModel.chatText.trim().length>0}"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@id/chat_input"
                                app:layout_constraintTop_toTopOf="@id/chat_input" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.kennyc.view.MultiStateView>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/like_container"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        app:goneIfNot="@{viewModel.showLikeAction}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/action_like"
                            style="@style/Widget.Flashat.PaidLikeButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:icon="@{viewModel.podium.likesDisabled?@drawable/ic_podium_like_disable:@drawable/ic_podium_like}"
                            tools:icon="@drawable/ic_podium_like"
                            android:enabled="@{!viewModel.likeButtonEnabled}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/chat_input_overlay"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        android:background="@color/colorSurface"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/like_container">

<!--                        <include layout="@layout/layout_podium_chat_overlay_user_joined"/>-->

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/action_speakers_toggle"
                    style="@style/Widget.Flashat.Podium.ActionFAB.Secondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/element_spacing"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:visibility="gone"
                    app:icon="@{speakersCollapsed?@drawable/ic_caret_left:@drawable/ic_caret_right}"
                    app:layout_constraintEnd_toEndOf="@+id/speaker_split_line"
                    tools:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/speakers_group"
                    tools:icon="@drawable/ic_caret_right"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
